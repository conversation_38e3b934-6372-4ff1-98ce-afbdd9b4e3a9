{"pipeline": {"prepare": {}, "build": {"outputs": ["build/**"], "dependsOn": ["^build", "^prepare", "prepare"]}, "coverage": {"outputs": [], "dependsOn": []}, "typecheck": {"outputs": [], "dependsOn": []}, "spec": {"outputs": [], "dependsOn": []}, "lint": {"dependsOn": ["^build"], "outputs": []}, "format": {"dependsOn": ["^build"], "outputs": []}, "dev": {"cache": false}, "clean": {"cache": false}, "db:schema": {"cache": false}, "db:migrate": {"cache": false}, "db:migrate:dev": {"cache": false}, "db:setup": {"cache": false}, "db:fresh": {"cache": false}, "db:create:migration": {"cache": false}, "jobs": {"cache": false}}}