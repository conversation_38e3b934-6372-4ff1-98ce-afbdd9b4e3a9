# Ordermentum Libs

Welcome to the Ordermentum Libs Monorepo! This repository contains a collection of TypeScript libraries that are shared and used across various projects within the Ordermentum ecosystem. This README will provide you with an overview of the structure, setup, and guidelines for working with this monorepo.

## Table of Contents

- [Getting Started](#getting-started)
- [Monorepo Structure](#monorepo-structure)
- [Available Packages](#available-packages)
- [Development Guidelines](#development-guidelines)
  - [Branching Strategy](#branching-strategy)
  - [Commit Guidelines](#commit-guidelines)
  - [Versioning](#versioning)
- [Available Scripts](#available-scripts)
- [Running Tests](#running-tests)
- [Building Libraries](#building-libraries)
- [Publishing Libraries](#publishing-libraries)
- [Code Linting and Formatting](#code-linting-and-formatting)
- [Dependency Management](#dependency-management)
- [TypeScript Configuration](#typescript-configuration)
- [Continuous Integration](#continuous-integration)

## Getting Started

To get started with the Ordermentum TypeScript Monorepo on your local machine, follow these steps:

1. Clone this repository to your local machine.
2. Ensure you have [Node.js](https://nodejs.org/) and [Yarn](https://yarnpkg.com/) installed.
3. Run `yarn install` at the root of the repository to install all dependencies.
4. You're now ready to develop, test, and contribute to the libraries!

## Monorepo Structure

The monorepo is organized using [Yarn Workspaces](https://classic.yarnpkg.com/en/docs/workspaces/), which allows managing multiple packages within the same repository.

```
ordermentum-libs/
│
├── packages/           # Contains all the public packages
│   ├── auth-driver/
│   ├── cache-machine/
│   ├── fireflight/
│   └── ...
│
├── internal/          # Contains internal packages and configurations
│   └── ...
│
├── build/             # Build output directory
├── package.json
├── tsconfig.json
├── turbo.json         # Turborepo configuration
├── yarn.lock
└── ...
```

Each package (library) resides within the `packages/` directory and has its own folder containing source code, tests, and a `package.json` file.

## Development Guidelines

### Branching Strategy

We follow the [Gitflow Workflow](https://www.atlassian.com/git/tutorials/comparing-workflows/gitflow-workflow), which involves feature branches and a `main` which the stable production-ready code.

For each feature or bug fix, create a feature branch off of `develop`:
```
git checkout develop
git pull
git checkout -b feature/new-feature
```

Once the feature is ready, create a pull request to merge it back into the `develop` branch.

### Commit Guidelines

We use [Conventional Commits](https://www.conventionalcommits.org/) for clear and standardized commit messages. This helps with generating changelogs and versioning.

### Versioning

We use ChangeSets to manage versions.

Run yarn changeset to indicate that we are releasing a new version of the package with this change. This will bring up some questions for you to answer. Once you have answered these questions, commit the files that have been generated.

We have a github action that will publish once your PR has been merged.

## Running Tests

To run tests for all libraries in the monorepo, you can use the following command:

```
yarn spec
```

This will execute tests for each library using their respective test configurations.

## Building Libraries

To build all libraries in the monorepo, you can use the following command:

```
yarn build
```

This command will trigger the build process for each library and output the compiled code in their respective `build/` or `lib/` directories (depending on the package configuration).

## Publishing Libraries

Library publishing is automated using Changesets.

## Code Linting and Formatting

We maintain consistent code style using tools like ESLint and Prettier. Run the following command to lint and format the code:

```
yarn lint
```

## Dependency Management

Dependencies for all libraries are managed at the monorepo level. We use Yarn Workspaces to ensure consistent and efficient dependency management.

## Available Packages

This monorepo contains the following packages:

| Package | Description |
|---------|-------------|
| `@ordermentum/auth-driver` | A client for Ordermentum auth service |
| `@ordermentum/auth-middleware` | Authentication middleware |
| `@ordermentum/axios-retry` | Axios retry functionality |
| `@ordermentum/cache-machine` | A lightweight Redis caching library |
| `@ordermentum/cart-javascript-sdk` | Cart Browser/Node SDK |
| `@ordermentum/eslint-config-ordermentum` | ESLint configuration |
| `@ordermentum/fireflight` | Type declarations for JSON API standard |
| `@ordermentum/health` | Health check utilities |
| `@ordermentum/payments-javascript-sdk` | SDK for consuming Payment service APIs |
| `@ordermentum/pika` | A boolean expression evaluation language |
| `@ordermentum/prowl` | Preconfigured Express Security |
| `@ordermentum/retry-machine` | Retry functionality |
| `@ordermentum/scheduler` | A unified job scheduler library |
| `@ordermentum/slingshot` | ViewModel/Decorator layer for ES6 |

## Available Scripts

The following scripts are available at the root level:

| Script | Description |
|--------|-------------|
| `yarn build` | Build all packages |
| `yarn typecheck` | Run TypeScript type checking |
| `yarn spec` | Run tests for all packages |
| `yarn lint` | Run linting for all packages |
| `yarn format` | Format code using ESLint |
| `yarn release` | Build, lint, typecheck, and publish packages |

## TypeScript Configuration

The monorepo uses TypeScript for type safety. The main configuration is in the root `tsconfig.json` file, which sets common compiler options for all packages. Individual packages may have their own `tsconfig.json` files that extend the root configuration.

Key TypeScript features used:
- Target: ES2022
- Module: CommonJS
- Strict type checking enabled
- Declaration files generated for library consumers

## Continuous Integration

The monorepo is integrated with a CI/CD pipeline (GitHub Actions) that runs tests, checks code quality, and automates the library release process.

---

Feel free to contribute, report issues, and improve the libraries within this monorepo. Happy coding! 🚀
