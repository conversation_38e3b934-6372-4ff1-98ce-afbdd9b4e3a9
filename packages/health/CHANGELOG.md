# @ordermentum/health

## 1.0.3

### Patch Changes

- a9d1fc7: Enhanced `on` method to support multiple callbacks for the same event. Callbacks are executed in registration order and errors in one callback don't prevent others from executing.

## 1.0.3

### Minor Changes

- Enhanced `on` method to support multiple callbacks for the same event. Callbacks are executed in registration order and errors in one callback don't prevent others from executing.

## 1.0.2

### Patch Changes

- 7de35d5: Update user provided health check type to be a wider net

## 1.0.1

### Patch Changes

- f18e2bc: Lower spammy log level

## 1.0.0

### Major Changes

- e2d5f70: Health checks running on background and returning a health variable

## 0.2.6

### Patch Changes

- 34e9b3c: Moved to monorepo
