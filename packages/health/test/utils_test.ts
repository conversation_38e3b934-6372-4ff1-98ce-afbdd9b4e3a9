/* eslint-disable no-throw-literal */
import { expect } from 'chai';
import sinon from 'sinon';
import { describe, it } from 'mocha';
import { checkRunner, race, setTimeoutPromise } from '../src/utils';
import { HealthStates } from '../src/types';

describe('checkRunner', () => {
  it('should return healthy status when check succeeds', async () => {
    const checkStub = sinon.stub().resolves();
    const runner = checkRunner('test-check', () => checkStub());
    const result = await runner();

    expect(checkStub.calledOnce).to.equal(true);
    expect(result.status).to.equal(HealthStates.HEALTHY);
    expect(result.message).to.equal('Health check successful');
  });

  it('should return healthy status with message from string result', async () => {
    const check = async () => 'String success message';
    const runner = checkRunner('test-check', check);
    const result = await runner();

    expect(result.status).to.equal(HealthStates.HEALTHY);
    expect(result.message).to.equal('String success message');
  });

  it('should return healthy status with default message when check returns empty string', async () => {
    const check = async () => '';
    const runner = checkRunner('test-check', check);
    const result = await runner();

    expect(result.status).to.equal(HealthStates.HEALTHY);
    expect(result.message).to.equal('Health check successful');
  });

  it('should return healthy status with message from object result', async () => {
    const check = async () => ({ message: 'Object success message' });
    const runner = checkRunner('test-check', check);
    const result = await runner();

    expect(result.status).to.equal(HealthStates.HEALTHY);
    expect(result.message).to.equal('Object success message');
  });

  it('should return unhealthy status when check throws Error', async () => {
    const check = async () => {
      throw new Error('Test error');
    };

    const runner = checkRunner('test-check', check);
    const result = await runner();

    expect(result.status).to.equal(HealthStates.UNHEALTHY);
    expect(result.error).to.equal('Test error');
  });

  it('should return unhealthy status when check throws string', async () => {
    const check = async () => {
      throw 'String error';
    };

    const runner = checkRunner('test-check', check);
    const result = await runner();

    expect(result.status).to.equal(HealthStates.UNHEALTHY);
    expect(result.error).to.equal('String error');
  });

  it('should return unhealthy status with unknown error message for function error types', async () => {
    const check = async () => {
      throw () => 'Function error';
    };

    const runner = checkRunner('test-check', check);
    const result = await runner();

    expect(result.status).to.equal(HealthStates.UNHEALTHY);
    expect(result.error).to.equal(`() => 'Function error'`);
  });

  it('should return unhealthy status with unknown error message for other error types', async () => {
    const check = async () => {
      throw {
        message: 'Object error',
      };
    };

    const runner = checkRunner('test-check', check);
    const result = await runner();

    expect(result.status).to.equal(HealthStates.UNHEALTHY);
    expect(result.error).to.equal('Unknown error during health check');
  });
});

describe('race', () => {
  let clock;

  beforeEach(() => {
    clock = sinon.useFakeTimers();
  });

  afterEach(() => {
    clock.restore();
  });

  it('should return healthy result when check completes before timeout', async () => {
    const check = async () => 'Success';
    const runner = checkRunner('test-check', check);
    const resultPromise = race('test-check', runner, 1000);

    clock.tick(100);
    const result = await resultPromise;

    expect(result.status).to.equal(HealthStates.HEALTHY);
    expect(result.message).to.equal('Success');
  });

  it('should return unhealthy timeout result when check exceeds timeout', async () => {
    const check = async () => {
      await setTimeoutPromise(2000);
      return 'Failed to complete';
    };
    const runner = checkRunner('test-check', check);
    const resultPromise = race('test-check', runner, 1000);

    clock.tick(1000);
    const result = await resultPromise;

    expect(result.status).to.equal(HealthStates.UNHEALTHY);
    expect(result.error).to.equal('Timeout: Health check exceeded 1000ms');
  });
});
