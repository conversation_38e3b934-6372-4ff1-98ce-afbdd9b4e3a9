import { expect } from 'chai';
import sinon from 'sinon';
import { describe, before, after, it } from 'mocha';
import bunyan from 'bunyan';
import HealthChecker from '../src/index';
import { HealthStates } from '../src/types';
import { setTimeoutPromise } from '../src/utils';

describe('checker', () => {
  let sandbox: sinon.SinonSandbox;

  const logger = bunyan.createLogger({ name: 'logger', level: 'debug' });

  before(() => {
    sandbox = sinon.createSandbox();
  });
  after(() => {
    sandbox.restore();
  });

  it('checks successfully', async () => {
    const checker = new HealthChecker({
      maxConsecutiveFailures: 1,
      checkIntervalMs: 1000,
      logger,
    });

    checker.register('test', () => Promise.resolve());
    checker.start();

    const results = await checker.health();
    expect(results.healthy).to.equal(true);
    expect(results.results.at(0)?.status).to.equal(HealthStates.HEALTHY);
  });

  it('checks successfully if unhealthy', async () => {
    const checker = new HealthChecker({
      maxConsecutiveFailures: 1,
      checkIntervalMs: 1000,
      logger,
    });

    checker.register('test', () => Promise.reject());
    checker.start();

    const results = await checker.health();
    expect(results.healthy).to.equal(false);
    expect(results.results.at(0)?.status).to.equal(HealthStates.UNHEALTHY);
  });

  it('checks multiple registered checks successfully', async () => {
    const checker = new HealthChecker({
      maxConsecutiveFailures: 1,
      checkIntervalMs: 1000,
      logger,
    });

    checker.register('test', () => Promise.reject());
    checker.register('test2', () => Promise.resolve());
    checker.start();

    const results = await checker.health();
    expect(results.healthy).to.equal(false);
    expect(results.results.at(0)?.status).to.equal(HealthStates.UNHEALTHY);
    expect(results.results.at(1)?.status).to.equal(HealthStates.HEALTHY);
  });

  it('checks timeouts successfully', async () => {
    const checker = new HealthChecker({
      maxConsecutiveFailures: 1,
      checkIntervalMs: 1000,
      logger,
    });

    checker.register('test', async () => {
      await setTimeoutPromise(2000);
      return Promise.reject();
    });
    checker.start();

    const results = await checker.health();
    expect(results.healthy).to.equal(false);
    expect(results.results.at(0)?.status).to.equal(HealthStates.UNHEALTHY);
  });

  it('throws if check is registered after start', async () => {
    const checker = new HealthChecker({
      maxConsecutiveFailures: 1,
      checkIntervalMs: 1000,
      logger,
    });

    checker.start();

    try {
      checker.register('test', () => Promise.resolve());
      expect.fail('Should have thrown');
    } catch (e: any) {
      expect(e.message).to.equal(
        'Cannot register checks after HealthChecker has been started.'
      );
    }
  });

  describe('recurring', () => {
    let pauseStub: sinon.SinonStub;
    let resumeStub: sinon.SinonStub;
    let clock;

    beforeEach(() => {
      pauseStub = sandbox.stub().resolves();
      resumeStub = sandbox.stub().resolves();
      clock = sinon.useFakeTimers(new Date());
    });

    afterEach(() => {
      clock.restore();
    });

    it('pauses on failure', async () => {
      const checker = new HealthChecker({
        maxConsecutiveFailures: 1,
        checkIntervalMs: 1000,
        recurring: true,
        logger,
      });

      checker.register('test', () => Promise.reject());
      checker.on('pause', pauseStub);
      checker.start();

      clock.tick(1000);
      const results = await checker.check();
      expect(results.healthy).to.equal(false);
      expect(results.results.at(0)?.status).to.equal(HealthStates.UNHEALTHY);

      expect(checker.recurring).to.equal(true);
      expect(pauseStub.calledOnce).to.equal(true);
      expect(checker.state).to.equal('paused');
      expect(checker.errors).to.equal(1);
      checker.clear();
    });

    it('resumes on success', async () => {
      const checker = new HealthChecker({
        maxConsecutiveFailures: 1,
        checkIntervalMs: 1000,
        recurring: true,
        logger,
      });

      const healthStub = sandbox
        .stub()
        .onFirstCall()
        .rejects()
        .onSecondCall()
        .resolves();

      const client = {
        healthCheck: healthStub,
      };

      checker.register('test', () => client.healthCheck());
      checker.on('pause', pauseStub);
      checker.on('resume', resumeStub);
      checker.start();

      clock.tick(1000);
      await checker.check();
      expect(checker.recurring).to.equal(true);

      expect(checker.state).to.equal('paused');
      expect(pauseStub.calledOnce).to.equal(true);
      expect(checker.errors).to.equal(1);

      await clock.tickAsync(1000);
      expect(resumeStub.calledOnce).to.equal(true);
      expect(checker.state).to.equal('running');
      expect(checker.errors).to.equal(0);
      checker.clear();
    });

    it('only pauses once', async () => {
      const checker = new HealthChecker({
        maxConsecutiveFailures: 2,
        checkIntervalMs: 1000,
        recurring: true,
        logger,
      });

      const healthStub = sandbox.stub().rejects();

      const client = {
        healthCheck: healthStub,
      };

      checker.register('test', () => client.healthCheck());
      checker.on('pause', pauseStub);
      checker.on('resume', resumeStub);
      checker.start();

      clock.tick(1000);
      await checker.check();
      expect(checker.recurring).to.equal(true);

      expect(checker.state).to.equal('paused');
      expect(pauseStub.calledOnce).to.equal(true);
      expect(checker.errors).to.equal(1);

      await clock.tickAsync(1000);
      expect(resumeStub.calledOnce).to.equal(false);
      expect(checker.state).to.equal('paused');
      checker.clear();
    });

    it('terminates', async () => {
      const checker = new HealthChecker({
        maxConsecutiveFailures: 1,
        checkIntervalMs: 1000,
        recurring: true,
        logger,
      });

      const terminateStub = sandbox.stub().resolves();
      const healthStub = sandbox.stub().rejects();

      const client = {
        healthCheck: healthStub,
      };

      checker.register('test', () => client.healthCheck());
      checker.on('pause', pauseStub);
      checker.on('resume', resumeStub);
      checker.on('terminate', terminateStub);
      checker.start();

      clock.tick(1000);
      await checker.check();
      expect(checker.recurring).to.equal(true);

      expect(checker.state).to.equal('paused');
      expect(pauseStub.calledOnce).to.equal(true);
      expect(checker.errors).to.equal(1);

      await clock.tickAsync(1000);
      expect(resumeStub.calledOnce).to.equal(false);
      expect(checker.errors).to.equal(2);
      expect(checker.state).to.equal('terminated');
      expect(terminateStub.calledOnce).to.equal(true);
    });

    it('supports multiple callbacks for the same event', async () => {
      const checker = new HealthChecker({
        maxConsecutiveFailures: 1,
        checkIntervalMs: 1000,
        recurring: true,
        logger,
      });

      const terminateStub1 = sandbox.stub().resolves();
      const terminateStub2 = sandbox.stub().resolves();
      const terminateStub3 = sandbox.stub().resolves();
      const pauseStub1 = sandbox.stub().resolves();
      const pauseStub2 = sandbox.stub().resolves();
      const healthStub = sandbox.stub().rejects();

      const client = {
        healthCheck: healthStub,
      };

      checker.register('test', () => client.healthCheck());

      // Register multiple callbacks for the same events
      checker.on('pause', pauseStub1);
      checker.on('pause', pauseStub2);
      checker.on('terminate', terminateStub1);
      checker.on('terminate', terminateStub2);
      checker.on('terminate', terminateStub3);

      checker.start();

      // Wait for the first background check to complete and pause
      await clock.tickAsync(1);

      // Should pause and call both pause callbacks
      expect(checker.state).to.equal('paused');
      expect(pauseStub1.calledOnce).to.equal(true);
      expect(pauseStub2.calledOnce).to.equal(true);
      expect(checker.errors).to.equal(1);

      // Wait for the next background check to complete and terminate
      await clock.tickAsync(1000);

      // Should terminate and call all three terminate callbacks
      expect(checker.errors).to.equal(2);
      expect(checker.state).to.equal('terminated');
      expect(terminateStub1.calledOnce).to.equal(true);
      expect(terminateStub2.calledOnce).to.equal(true);
      expect(terminateStub3.calledOnce).to.equal(true);
    });
  });
});
