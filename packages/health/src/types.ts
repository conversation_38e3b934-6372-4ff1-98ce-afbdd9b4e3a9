export const HealthStates = {
  HEALTHY: 'healthy',
  UNHEALTHY: 'unhealthy',
};

export interface IndividualHealthResponse {
  name: string;
  status: typeof HealthStates.HEALTHY | typeof HealthStates.UNHEALTHY;
  error?: string;
  message?: string;
  duration: number;
}

export interface AggregatedHealthResponse {
  healthy: boolean;
  results: IndividualHealthResponse[];
}

export const State = {
  NOT_STARTED: 'not_started',
  PAUSED: 'paused',
  RUNNING: 'running',
  TERMINATED: 'terminated',
};

export type UserProvidedCheckResult =
  | { message?: string; [key: string]: unknown }
  | unknown;

export type UserProvidedCheck = () => Promise<UserProvidedCheckResult>;

export type InternalCheckFunction = () => Promise<IndividualHealthResponse>;

export type KNOWN_CALLBACKS = 'pause' | 'resume' | 'terminate';

export interface Logger {
  info(message?: any, ...optionalParams: any[]): void;
  debug(message?: any, ...optionalParams: any[]): void;
  warn(message?: any, ...optionalParams: any[]): void;
  error(message?: any, ...optionalParams: any[]): void;
}
