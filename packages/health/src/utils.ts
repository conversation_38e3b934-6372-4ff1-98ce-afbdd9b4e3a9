import { promisify } from 'node:util';
import {
  UserProvidedCheck,
  InternalCheckFunction,
  IndividualHealthResponse,
  HealthStates,
} from './types';

export const setTimeoutPromise = promisify(setTimeout);

// Timeout fallback for a check
const timeoutFallback = (
  name: string,
  timeout: number
): Promise<IndividualHealthResponse> =>
  setTimeoutPromise(timeout).then(() => ({
    name,
    status: HealthStates.UNHEALTHY,
    error: `Timeout: Health check exceeded ${timeout}ms`,
    duration: timeout, // Duration is the timeout itself
  }));

export const checkRunner =
  (name: string, check: UserProvidedCheck): InternalCheckFunction =>
  async (): Promise<IndividualHealthResponse> => {
    const startTime = Date.now();
    try {
      const result = await check();
      const duration = Date.now() - startTime;
      // If check() resolves with a string, use it as a message.
      // Otherwise, provide a generic success message.
      let message = 'Health check successful'; // Default message
      if (typeof result === 'string' && result.length > 0) {
        message = result;
      } else if (
        typeof result === 'object' &&
        result !== null &&
        'message' in result &&
        typeof result.message === 'string'
      ) {
        message = result.message;
      }
      return {
        name,
        status: HealthStates.HEALTHY,
        message,
        duration,
      };
    } catch (e: unknown) {
      const duration = Date.now() - startTime;
      let errorMessage = 'Unknown error during health check';
      if (e instanceof Error) {
        errorMessage = e.message;
      } else if (typeof e === 'string') {
        errorMessage = e;
      } else if (e && typeof e === 'function') {
        const maybeMessage = (e as any).toString();
        // Avoid unhelpful '[object Object]' messages
        if (maybeMessage !== '[object Object]') {
          errorMessage = maybeMessage;
        }
      }
      return {
        name,
        status: HealthStates.UNHEALTHY,
        error: errorMessage,
        duration,
      };
    }
  };

// Races a check against a timeout
export const race = (
  name: string,
  checkerFunc: InternalCheckFunction,
  timeoutMs: number
): Promise<IndividualHealthResponse> =>
  Promise.race([checkerFunc(), timeoutFallback(name, timeoutMs)]);
