import {
  AggregatedHealthResponse,
  IndividualHealthResponse,
  InternalCheckFunction,
  UserProvidedCheck,
  HealthStates,
  Logger,
  State,
  KNOWN_CALLBACKS,
} from './types';
import { checkRunner, race } from './utils';

export interface HealthCheckerConfig {
  /**
   * The number of consecutive failures after which the health checker
   * will transition to a terminated state.
   */
  maxConsecutiveFailures: number;
  /**
   * The time in milliseconds to wait between periodic health checks
   * if `recurring` is true.
   */
  checkIntervalMs: number;
  /**
   * If true, health checks will be performed periodically at the interval
   * specified by `checkIntervalMs`. If false, checks are only performed on demand
   * via the `health()` or `check()` methods. Defaults to false.
   */
  recurring?: boolean;
  /**
   * A logger instance (conforming to the Logger interface) for the health
   * checker to use for outputting information, warnings, and errors.
   */
  logger: Logger;
  /**
   * The timeout in milliseconds for each individual health check function.
   * If a check takes longer than this, it will be considered to have failed.
   * Defaults to 1000ms (1 second).
   */
  checkTimeoutMs?: number;
}

const DEFAULT_CHECK_TIMEOUT_MS = 1000;

export class HealthChecker {
  #maxConsecutiveFailures: number;

  #checkIntervalMs: number;

  #recurring: boolean;

  #logger: Logger;

  #checkTimeoutMs: number;

  #pendingChecks = new Map<string, UserProvidedCheck>();

  #pendingCallbacks = new Map<KNOWN_CALLBACKS, (() => Promise<unknown>)[]>();

  // Operational state - initialized by start()
  #registry: Map<string, InternalCheckFunction> = new Map();

  #state: typeof State.NOT_STARTED = State.NOT_STARTED;

  #errors: number = 0;

  #callbacks: Map<KNOWN_CALLBACKS, (() => Promise<unknown>)[]> = new Map();

  #latestHealthResponse: AggregatedHealthResponse = {
    healthy: false,
    results: [],
  };

  #backgroundCheckIntervalId: NodeJS.Timeout | null = null;

  #isCurrentlyChecking = false;

  #isStarted = false; // To ensure start() is called only once

  constructor(config: HealthCheckerConfig) {
    this.#maxConsecutiveFailures = config.maxConsecutiveFailures;
    this.#checkIntervalMs = config.checkIntervalMs;
    this.#recurring = config.recurring ?? false;
    this.#logger = config.logger;
    this.#checkTimeoutMs = config.checkTimeoutMs ?? DEFAULT_CHECK_TIMEOUT_MS;
    this.#logger.debug(
      'HealthChecker constructed with core config. Call .start() to activate.'
    );
  }

  /**
   * Registers a health check function to be executed.
   * This method should be called before `start()`.
   * If a check with the same name already exists, it will be overridden.
   *
   * @param name - A unique name for the health check.
   * @param check - An asynchronous function that performs the health check.
   *                It should return a promise that resolves with any value on success
   *                or rejects with an error on failure.
   * @returns The HealthChecker instance for chaining.
   * @throws Error if called after `start()`.
   *
   * @example
   * ```typescript
   * const checker = new HealthChecker({ ...config })
   *   .register('database', async () => {
   *     // Perform database health check
   *     const isConnected = await myDb.ping();
   *     if (!isConnected) {
   *       throw new Error('Database is not responding');
   *     }
   *     return 'Database connection successful';
   *   })
   *   .register('external-api', async () => {
   *     // Perform external API health check
   *     const response = await fetch('https://api.example.com/health');
   *     if (!response.ok) {
   *       throw new Error(`External API returned status ${response.status}`);
   *     }
   *     return 'External API is healthy';
   *   });
   * ```
   */
  public register(name: string, check: UserProvidedCheck): this {
    if (this.#isStarted) {
      this.#logger.warn(
        'Cannot register checks after HealthChecker has been started. Ignoring.'
      );
      throw new Error(
        'Cannot register checks after HealthChecker has been started.'
      );
    }
    if (this.#pendingChecks.has(name)) {
      this.#logger.warn(
        `Pending health check "${name}" is being overridden during configuration.`
      );
    }
    this.#pendingChecks.set(name, check);
    return this;
  }

  /**
   * Registers a callback function to be executed on specific lifecycle events.
   * This method should be called before `start()`.
   * Multiple callbacks can be registered for the same event and will be executed in registration order.
   *
   * Supported event names are: 'pause', 'resume', 'terminate'.
   *
   * @param name - The name of the lifecycle event to attach the callback to.
   *               Must be one of `KNOWN_CALLBACKS` ('pause', 'resume', 'terminate').
   * @param callback - An asynchronous function to be executed when the event occurs.
   *                   It should return a promise that resolves when the callback logic is complete.
   * @returns The HealthChecker instance for chaining.
   * @throws Error if called after `start()`.
   *
   * @example
   * ```typescript
   * const checker = new HealthChecker({ ...config })
   *   .on('terminate', async () => {
   *     console.error('System is terminating due to critical health failures.');
   *     // Perform cleanup or notification tasks
   *     // e.g., await notifyAdminSystem('System terminated');
   *   })
   *   .on('terminate', async () => {
   *     console.log('Additional termination cleanup');
   *   })
   *   .on('pause', async () => {
   *     console.warn('System is paused due to health issues.');
   *   });
   * ```
   */
  public on(name: KNOWN_CALLBACKS, callback: () => Promise<unknown>): this {
    if (this.#isStarted) {
      this.#logger.warn(
        'Cannot add callbacks after HealthChecker has been started. Ignoring.'
      );
      throw new Error(
        'Cannot add callbacks after HealthChecker has been started.'
      );
    }

    const existingCallbacks = this.#pendingCallbacks.get(name) || [];
    existingCallbacks.push(callback);
    this.#pendingCallbacks.set(name, existingCallbacks);

    this.#logger.debug(
      `Registered callback for "${name}" event. Total callbacks for this event: ${existingCallbacks.length}`
    );

    return this;
  }

  /**
   * Handles process signals (e.g., SIGTERM, SIGINT) to initiate a graceful shutdown of the HealthChecker.
   *
   * This method logs the received signal, then attempts to gracefully terminate the HealthChecker's
   * operations by calling `this.terminate()`. After the termination attempt (successful or not),
   * it logs a final message and exits the process.
   *
   * Special behavior:
   * - If `process.env.NODE_ENV` is 'development', the process exits immediately without
   *   attempting graceful termination.
   *
   * @param signal - The name of the signal received (e.g., 'SIGTERM'), used for logging.
   * @private
   */
  private _shutdownHandler = async (signal: string) => {
    if (process.env.NODE_ENV === 'development') {
      process.exit();
    }

    this.#logger.info(
      `Received ${signal} signal. Initiating graceful shutdown of HealthChecker.`
    );

    try {
      // Attempt to run the HealthChecker's terminate logic, which may include
      // executing registered 'terminate' callbacks for cleanup.
      await this.terminate();
      this.#logger.info('HealthChecker termination process completed.');
    } catch (error) {
      this.#logger.error(
        'Error during HealthChecker termination process:',
        error
      );
    }

    this.#logger.info('Shutdown handler: Exiting process now.');
    process.exit();
  };

  /**
   * Finalizes the configuration and starts the health checker.
   * This method should be called after all checks and callbacks have been registered.
   *
   * @returns The HealthChecker instance for chaining.
   * @throws Error if called multiple times.
   *
   * @example
   * ```typescript
   * const checker = new HealthChecker({ ...config })
   *   .register('database', async () => { ... })
   *   .addCallback('terminate', async () => { ... })
   *   .start();
   * ```
   */
  public start(): this {
    if (this.#isStarted) {
      this.#logger.warn(
        'HealthChecker start() called multiple times. Ignoring subsequent calls.'
      );
      return this;
    }

    this.#state = State.RUNNING;
    this.#errors = 0;
    this.#registry = new Map<string, InternalCheckFunction>();
    for (const [name, check] of this.#pendingChecks.entries()) {
      this._registerCheck(name, check);
    }
    this.#callbacks = new Map<KNOWN_CALLBACKS, (() => Promise<unknown>)[]>();
    for (const [eventName, callbacks] of this.#pendingCallbacks.entries()) {
      this.#callbacks.set(eventName, [...callbacks]);
    }

    this.#latestHealthResponse = {
      healthy: false,
      results: [],
    };

    this.#isStarted = true;
    this.#logger.info('HealthChecker started and configured.');

    if (this.#recurring) {
      this._scheduleNextBackgroundCheck(true);
    }

    for (const signal of ['SIGTERM', 'SIGINT']) {
      process.on(signal, this._shutdownHandler);
    }
    return this;
  }

  private _ensureStarted(methodName: string): void {
    if (!this.#isStarted) {
      throw new Error(
        `HealthChecker must be started with .start() before calling .${methodName}().`
      );
    }
  }

  get recurring(): boolean {
    return this.#recurring;
  }

  set recurring(recurring: boolean) {
    const oldRecurring = this.#recurring;
    this.#recurring = recurring;
    if (this.#isStarted) {
      if (!oldRecurring && this.#recurring) {
        this._scheduleNextBackgroundCheck(true);
      } else if (oldRecurring && !this.#recurring) {
        this._clearBackgroundCheck();
      }
    }
  }

  get state(): typeof State.NOT_STARTED {
    this._ensureStarted('get state');
    return this.#state;
  }

  get errors(): number {
    this._ensureStarted('get errors');
    return this.#errors;
  }

  // Internal method to populate the actual registry from a UserProvidedCheck
  private _registerCheck(name: string, check: UserProvidedCheck): void {
    // This is now only called from start() with pendingChecks, so no need to check #isStarted
    // It populates the operational #registry
    if (this.#registry.has(name)) {
      // This case should ideally not happen if pendingChecks handles overrides properly
      this.#logger.warn(
        `Internal: Health check "${name}" is being duplicated in registry.`
      );
    }
    this.#registry.set(name, checkRunner(name, check));
  }

  public async check(): Promise<AggregatedHealthResponse> {
    this._ensureStarted('check');
    this.#logger.debug('Performing on-demand health check...');
    const runners: Promise<IndividualHealthResponse>[] = [];
    if (this.#registry.size === 0) {
      // Should be initialized by start()
      this.#logger.error(
        'On-demand check called before registry was initialized (post-start). Returning empty healthy state.'
      );
      return { healthy: true, results: [] }; // Or false, depending on desired behavior pre-full-init
    }
    for (const [name, internalCheckFn] of this.#registry.entries()) {
      runners.push(race(name, internalCheckFn, this.#checkTimeoutMs));
    }
    const results = await Promise.all(runners);
    const healthy = !results.some(r => r.status === HealthStates.UNHEALTHY);
    this.#logger.debug('On-demand health check results: %j', {
      healthy,
      results,
    });
    return { healthy, results };
  }

  public clear(): void {
    this._ensureStarted('clear');
    this.#logger.debug('clear() called. Clearing background check if active.');
    this._clearBackgroundCheck();
  }

  private _clearBackgroundCheck(): void {
    if (this.#backgroundCheckIntervalId) {
      clearTimeout(this.#backgroundCheckIntervalId);
      this.#backgroundCheckIntervalId = null;
      this.#logger.debug('Background health check interval cleared.');
    }
  }

  private async _pause(): Promise<void> {
    if (this.#state !== State.PAUSED) {
      this.#logger.debug('Pausing health checks.');
      this.#state = State.PAUSED;
      const pauseCallbacks = this.#callbacks.get('pause') || [];
      for (const callback of pauseCallbacks) {
        try {
          await callback();
        } catch (e) {
          this.#logger.error('Error in pause callback:', e);
        }
      }
    }
  }

  private async _terminate(): Promise<void> {
    this.#logger.info(
      `Terminating health checks due to ${this.#errors} errors (threshold: ${
        this.#maxConsecutiveFailures
      }).`
    );
    this.#state = State.TERMINATED;
    this._clearBackgroundCheck();
    const terminateCallbacks = this.#callbacks.get('terminate') || [];
    for (const callback of terminateCallbacks) {
      try {
        await callback();
      } catch (e) {
        this.#logger.error('Error in terminate callback:', e);
      }
    }
  }

  private async _resume(): Promise<void> {
    if (this.#state === State.RUNNING && this.#errors === 0) {
      this.#logger.debug(
        'Resume method called. Health checks are already running and healthy.'
      );
      return;
    }

    this.#logger.info('Resuming health checks.');
    this.#state = State.RUNNING;
    this.#errors = 0;
    const resumeCallbacks = this.#callbacks.get('resume') || [];
    for (const callback of resumeCallbacks) {
      try {
        await callback();
      } catch (e) {
        this.#logger.error('Error in resume callback:', e);
      }
    }
  }

  private async _performBackgroundCheck(): Promise<void> {
    if (!this.#isStarted) {
      this.#logger.debug(
        'Background check called before HealthChecker was started.'
      );
      return;
    }

    if (this.#isCurrentlyChecking || this.#state === State.TERMINATED) {
      this.#logger.debug(
        this.#isCurrentlyChecking
          ? 'Background check already in progress, skipping.'
          : 'HealthChecker is terminated, no background check.'
      );
      return;
    }

    this.#isCurrentlyChecking = true;
    this.#logger.debug('Performing background health check cycle...');
    const currentCheckResults = await this.check();
    this.#latestHealthResponse = currentCheckResults;
    if (currentCheckResults.healthy) {
      if (this.#state === State.PAUSED) await this._resume();
      if (this.#errors > 0) this.#errors = 0;
    } else {
      this.#errors += 1;
      this.#logger.warn(
        `Background check: Unhealthy. Errors: ${this.#errors}/${
          this.#maxConsecutiveFailures
        }`
      );
      if (
        this.#errors > this.#maxConsecutiveFailures &&
        this.#state !== State.TERMINATED
      )
        await this._terminate();
      else if (this.#state !== State.PAUSED && this.#state !== State.TERMINATED)
        await this._pause();
    }
    this.#isCurrentlyChecking = false;
    if (this.#recurring && this.#state !== State.TERMINATED) {
      this._scheduleNextBackgroundCheck();
    }
  }

  private _scheduleNextBackgroundCheck(isInitialCall = false): void {
    if (!this.#isStarted && !isInitialCall) {
      // Don't schedule if not started, unless it's the explicit initial call from start()
      this.#logger.debug(
        'Background check called before HealthChecker was started and is not the initial call'
      );
      return;
    }

    this._clearBackgroundCheck();
    if (!this.#recurring || this.#state === State.TERMINATED) {
      this.#logger.debug(
        'HealthChecker is not recurring or is terminated. Skipping background check scheduling.'
      );
      return;
    }

    const delay = isInitialCall ? 0 : this.#checkIntervalMs;
    this.#logger.debug(`Scheduling next background check in ${delay}ms.`);

    this.#backgroundCheckIntervalId = setTimeout(() => {
      if (!this.#isStarted && this.#state !== State.TERMINATED) {
        this.#logger.debug(
          'HealthChecker was cleared or not started before scheduled check execution. Aborting.'
        );
        return;
      }

      this._performBackgroundCheck().catch(e => {
        this.#logger.error('Unhandled error during background check:', e);
        if (
          this.#recurring &&
          this.#state !== State.TERMINATED &&
          this.#isStarted
        )
          this._scheduleNextBackgroundCheck();
      });
    }, delay);
  }

  public async health(): Promise<AggregatedHealthResponse> {
    this._ensureStarted('health');
    if (!this.#recurring) {
      this.#logger.debug('Non-recurring: health() performs on-demand check.');
      return this.check();
    }
    this.#logger.debug('Recurring: Returning latest cached health response.');
    return this.#latestHealthResponse;
  }

  /**
   * Returns a request handler function suitable for use with Express.
   * The handler will respond with the current health status.
   * - 200 OK if healthy.
   * - 503 Service Unavailable if unhealthy.
   *
   * @example
   * ```javascript
   * // For Express
   * const app = express();
   * const healthChecker = new HealthChecker({ ...config });
   * // ... register checks, start checker ...
   * app.get('/health', healthChecker.makeExpressHandler());
   * ```
   */
  public makeExpressHandler() {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const self = this;
    return async function expressHealthCheckRequestHandler(...args: any[]) {
      const res = args[1]; // Express response object is the second argument
      const next = args[2]; // Express next function

      try {
        self._ensureStarted('makeExpressHandler');
        const healthStatus = await self.health();
        const statusCode = healthStatus.healthy ? 200 : 503;

        if (
          res &&
          typeof res.status === 'function' &&
          typeof res.json === 'function'
        ) {
          res.status(statusCode).json(healthStatus);
        } else {
          self.#logger.error(
            'makeExpressHandler: Response object does not look like an Express response object.'
          );
          if (typeof next === 'function') {
            next(new Error('Failed to send health check response via Express'));
          }
        }
      } catch (err: unknown) {
        let message = 'Internal Server Error in Express health handler';
        if (err instanceof Error) {
          message = err.message;
        } else if (typeof err === 'string') {
          message = err;
        }
        self.#logger.error('Error in makeExpressHandler:', err);

        if (
          res &&
          typeof res.status === 'function' &&
          typeof res.json === 'function'
        ) {
          res.status(500).json({ error: message });
        } else if (typeof next === 'function') {
          // If response object is not usable, pass error to Express error handler
          if (err instanceof Error) {
            next(err);
          } else {
            next(new Error(message));
          }
        }
      }
    };
  }

  /**
   * Returns a request handler function suitable for use with Hapi.
   * The handler will respond with the current health status.
   * - 200 OK if healthy.
   * - 503 Service Unavailable if unhealthy.
   *
   * @example
   * ```javascript
   * // For Hapi
   * const server = Hapi.server({ ... });
   * const healthChecker = new HealthChecker({ ...config });
   * // ... register checks, start checker ...
   * server.route({
   *   method: 'GET',
   *   path: '/health',
   *   handler: healthChecker.makeHapiHandler()
   * });
   * ```
   */
  public makeHapiHandler() {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const self = this;
    return async function hapiHealthCheckRequestHandler(...args: any[]) {
      const h = args[1]; // Hapi toolkit is the second argument

      try {
        self._ensureStarted('makeHapiHandler');
        const healthStatus = await self.health();
        const statusCode = healthStatus.healthy ? 200 : 503;

        if (h && typeof h.response === 'function') {
          const hapiResponse = h.response(healthStatus);
          if (typeof hapiResponse.code === 'function') {
            return hapiResponse.code(statusCode);
          }
        }
        // If h or h.response or hapiResponse.code is not a function
        self.#logger.error(
          'makeHapiHandler: Toolkit object does not look like a Hapi toolkit object.'
        );
        // For Hapi, returning an error or a specially formatted error response is typical
        // Constructing a Boom error is common, but we avoid Boom dependency here.
        // Returning a simple error response if possible.
        if (h && typeof h.response === 'function') {
          const errorResponse = h.response({
            error: 'Failed to send health check response via Hapi',
          });
          if (typeof errorResponse.code === 'function') {
            return errorResponse.code(500);
          }
        }
        // If truly stuck, Hapi will eventually timeout the request or handle the lack of response.
        throw new Error(
          'Failed to send health check response via Hapi due to invalid toolkit'
        );
      } catch (err: unknown) {
        let message = 'Internal Server Error in Hapi health handler';
        if (err instanceof Error) {
          message = err.message;
        } else if (typeof err === 'string') {
          message = err;
        }
        self.#logger.error('Error in makeHapiHandler:', err);

        if (h && typeof h.response === 'function') {
          const hapiErrorResponse = h.response({ error: message });
          if (typeof hapiErrorResponse.code === 'function') {
            return hapiErrorResponse.code(500);
          }
        }
        // If hapi toolkit is not available to send a response, rethrow to let Hapi handle it.
        // This will likely result in a 500 error response from Hapi itself.
        if (err instanceof Error) throw err;
        throw new Error(message);
      }
    };
  }

  /**
   * Manually triggers the termination sequence for this HealthChecker instance.
   * This will clear any background checks, set the state to TERMINATED,
   * and execute any registered 'terminate' callbacks.
   *
   * @returns A promise that resolves when the termination logic is complete.
   */
  public terminate(): Promise<void> {
    this._ensureStarted('terminate'); // Or decide if it can be called even if not fully started
    this.#logger.info(
      'Manual termination triggered for HealthChecker instance...'
    );
    return this._terminate();
  }
}

export default HealthChecker;
