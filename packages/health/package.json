{"name": "@ordermentum/health", "version": "1.0.3", "description": "Ordermentum Live/Health Checks", "main": "build/index.js", "repository": "**************:ordermentum/health.git", "author": "Ordermentum Pty Ltd", "license": "MIT", "types": "build/index.d.ts", "files": ["build/*"], "devDependencies": {"@istanbuljs/nyc-config-typescript": "^1.0.2", "@types/bunyan": "^1.8.8", "@types/chai": "^4.3.5", "@types/mocha": "^10.0.1", "@types/node": "17.0.45", "@types/sinon": "^10.0.14", "bunyan": "^1.8.15", "chai": "4.3.6", "eslint": "^8.57.0", "husky": "7.0.4", "lint-staged": "12.5.0", "mocha": "9.2.2", "mocha-sinon": "2.1.2", "nyc": "^15.1.0", "pino": "^8.11.0", "sinon": "^15.0.4", "ts-node": "10.9.2", "ts-node-dev": "1.1.8", "turbo": "1.13.4", "typescript": "5.1.6"}, "scripts": {"build": "yarn tsc", "lint": "eslint src/** test/**", "coverage": "./node_modules/.bin/nyc npm run spec", "spec:runner": "NODE_ENV=test ./node_modules/.bin/mocha --require ts-node/register", "spec": "npm run spec:runner 'test/**/*.{ts,js}'", "typecheck": "tsc --noEmit"}, "nyc": {"extension": [".ts", ".js"], "exclude": ["**/*.d.ts", "coverage/*.js", "build/"], "reporter": ["text", "html"], "all": true}, "dependencies": {}, "packageManager": "yarn@1.22.22"}