# Health Checker

A robust and flexible module to perform health checks for your application. It allows registering multiple asynchronous checks, running them periodically, and managing the overall health state of the application. You can also register callbacks for specific lifecycle events like pause, resume, and terminate.

## Features

*   Register multiple custom health checks.
*   Run checks periodically at a configurable interval.
*   Define a failure threshold before the service is considered terminated.
*   Pause and resume health checks.
*   Register multiple callbacks for `pause`, `resume`, and `terminate` events.
*   Custom logger integration.

## Installation

```bash
npm install @ordermentum/health
# or
yarn add @ordermentum/health
```

## Usage

```typescript
import HealthChecker, {
  HealthStates,
  Logger, // Optional: if you want to type your custom logger
  HealthCheckerConfig, // Optional: if you want to type your config object explicitly
} from '@ordermentum/health'; // Default import for HealthChecker

// --- Define a custom logger (optional) ---
const logger: Logger = {
  debug: (...args: any[]) => console.log('[DEBUG]', ...args),
  info: (...args: any[]) => console.info('[INFO]', ...args),
  warn: (...args: any[]) => console.warn('[WARN]', ...args),
  error: (...args: any[]) => console.error('[ERROR]', ...args),
};

// --- Configuration values ---
const healthCheckIntervalSeconds = 30;
const healthCheckFailuresThreshold = 1;
const individualCheckTimeoutMs = 2000;

// --- Instantiate HealthChecker with core config, then chain builder methods ---
const healthChecker = new HealthChecker({
  maxConsecutiveFailures: healthCheckFailuresThreshold,
  checkIntervalMs: healthCheckIntervalSeconds * 1000, // IMPORTANT: checkIntervalMs is in milliseconds
  recurring: true,
  logger,
  checkTimeoutMs: individualCheckTimeoutMs,
})
.register('critical-service-A', async () => {
  await new Promise(resolve => setTimeout(resolve, Math.random() * 100));
  if (Math.random() < 0.05) throw new Error('Service A check failed');
  return 'Service A is responding correctly';
})
.on('terminate', async () => {
  logger.error('HEALTH: System critically unhealthy. Terminating.');
  setTimeout(() => console.error("SIMULATED PROCESS EXIT"), 500);
})
.on('terminate', async () => {
  // Multiple callbacks can be registered for the same event
  logger.info('HEALTH: Performing cleanup tasks...');
  // Additional cleanup logic here
})
.register('database-main', async () => { // Chain more checks
  await new Promise(resolve => setTimeout(resolve, 70));
  if (Math.random() < 0.02) throw new Error('Main DB connection failed');
  return 'Main database is responsive';
})
.on('pause', async () => { // Chain more callbacks
  logger.warn('HEALTH: System experiencing issues. Pausing tasks.');
})
.start(); // Finalize setup and start the HealthChecker

// --- Using the HealthChecker ---
// The HealthChecker is now running its checks in the background periodically.

async function logCurrentHealthStatus() {
  try {
    const currentStatus = await healthChecker.health(); // Returns cached result if recurring
    logger.info(`-----------------------------------------------------`);
    logger.info(`Current overall health: ${currentStatus.healthy ? 'Healthy' : 'Unhealthy'}`);
    logger.info(`Current HealthChecker state: ${healthChecker.state}, Consecutive errors: ${healthChecker.errors}`);
    currentStatus.results.forEach(result => {
      const detail = result.status === HealthStates.UNHEALTHY 
        ? `Error: ${result.error || 'Unknown'}` 
        : `Message: ${result.message || 'OK'}`;
      const durationInfo = result.duration !== undefined ? `(${result.duration}ms)` : '';

      if (result.status === HealthStates.UNHEALTHY) {
        logger.warn(`  - ${result.name}: ${result.status} ${durationInfo} - ${detail}`);
      } else {
        logger.info(`  - ${result.name}: ${result.status} ${durationInfo} - ${detail}`);
      }
    });
    logger.info(`-----------------------------------------------------`);
  } catch (error) {
    logger.error('Failed to get health status:', error);
  }
}

// Log health status periodically to observe behavior
const loggingInterval = setInterval(logCurrentHealthStatus, 15000);
logCurrentHealthStatus(); // Initial log

// Example of an on-demand check (e.g., for a diagnostic API endpoint)
async function runOnDemandHealthCheck() {
  logger.info('<<<<< Performing an ON-DEMAND health check... >>>>>');
  try {
    const onDemandStatus = await healthChecker.check(); // Performs fresh checks
    logger.info(`On-demand overall health: ${onDemandStatus.healthy ? 'Healthy' : 'Unhealthy'}`);
    onDemandStatus.results.forEach(result => {
       const detail = result.status === HealthStates.UNHEALTHY ? result.error : result.message;
      logger.info(`  - (On-demand) ${result.name}: ${result.status} (${result.duration}ms) - ${detail}`);
    });
  } catch (error) {
     logger.error('Failed to run on-demand health check:', error);
  }
  logger.info('<<<<< On-demand health check finished. >>>>>');
}

setTimeout(runOnDemandHealthCheck, 40000); // Trigger an on-demand check after 40s

// To stop the HealthChecker (e.g., on application shutdown):
// healthChecker.clear();
// clearInterval(loggingInterval);

```

## API

### `new HealthChecker(config: HealthCheckerConfig)`

Creates a new `HealthChecker` instance but does not start it. 
Configuration for checks and lifecycle callbacks should be chained using `.register()` and `.on()` methods, followed by a call to `.start()`.

**`HealthCheckerConfig` Options:**

*   `maxConsecutiveFailures` (number): The number of consecutive failures allowed before the checker transitions to a `TERMINATED` state.
*   `checkIntervalMs` (number): The time in milliseconds to wait between health checks when `recurring` is true.
*   `recurring` (boolean, optional, default: `false`): If `true` (and after `.start()` is called), periodic background checks are enabled.
*   `logger` (Logger): A logger instance (conforming to the `Logger` interface defined in `src/types.ts`).
*   `checkTimeoutMs` (number, optional, default: `1000`): Timeout in milliseconds for each individual check function.

### `healthChecker.register(name: string, check: UserProvidedCheck): this`

Registers a health check function. Call before `.start()`.

*   `name` (string): A unique name for the health check.
*   `check`: The function to execute for the health check. It should be an async function or a function that returns a Promise. It can also be an object with a `check` method.
    *   If the promise resolves, the check is considered healthy.
    *   If the promise rejects or throws an error, the check is considered unhealthy.

### `healthChecker.on(name: KNOWN_CALLBACKS, callback: () => Promise<void>): this`

Registers a callback for lifecycle events. Call before `.start()`. Multiple callbacks can be registered for the same event and will be executed in registration order.

*   `name` ('pause' | 'resume' | 'terminate'): The lifecycle event to listen for.
*   `callback` (() => Promise<void>): An async function to execute when the event occurs.

### `healthChecker.start(): this`

Finalizes the configuration (processes all registered checks and callbacks) and starts the `HealthChecker`. If `recurring` is true, background checks will begin. This method must be called after the constructor and any `register`/`on` calls.

### `async healthChecker.health(): Promise<AggregatedHealthResponse>`

Retrieves the latest health status. Call after `.start()`.

*   If `recurring` is `true`, it returns the latest cached status from background checks.
*   If `recurring` is `false`, it performs an on-demand check (equivalent to `healthChecker.check()`).

*   Returns an `AggregatedHealthResponse` object:
    ```typescript
    interface AggregatedHealthResponse {
      healthy: boolean; // Overall health status
      results: Array<{
        name: string;
        status: HealthStates; // 'HEALTHY' or 'UNHEALTHY'
        error?: string; // Error message if unhealthy
        message?: string; // Success message from the check
        duration: number; // Duration of the check in ms
      }>;
    }
    ```

### `async healthChecker.check(): Promise<AggregatedHealthResponse>`

Runs all registered health checks on-demand. Call after `.start()`.

*   Returns a `HealthResponse` object (same structure as `healthChecker.health()`).

### `healthChecker.clear()`

Stops recurring background checks. Call after `.start()`.

### `healthChecker.state: State`

Read-only property returning the current `State` (`NOT_STARTED`, `RUNNING`, `PAUSED`, `TERMINATED`). Value is `NOT_STARTED` until `.start()` is called.

### `healthChecker.errors: number`

Read-only property for current consecutive failures. Meaningful after `.start()`.

### `healthChecker.recurring: boolean`

Read/write property for recurring behavior. Can be changed before or after `.start()`.

## States

The `HealthChecker` operates in the following states (see `src/types.ts` for `State` enum):

*   **`State.NOT_STARTED`**: The initial state after instantiation, before `.start()` is called.
*   **`State.RUNNING`**: Actively performing/scheduling checks (after `.start()`).
*   **`State.PAUSED`**: If `recurring` is true, the checker enters this state after a health check fails, but before the `failures` threshold is met. It will continue to run checks at `waitTime` intervals. If a subsequent check passes, it transitions back to `RUNNING`. The `pause` callback is triggered upon entering this state.
*   **`State.TERMINATED`**: If `recurring` is true and the number of consecutive failures (`errors`) exceeds the `failures` threshold, the checker enters this state. No more checks will be scheduled. The `terminate` callback is triggered.

This module provides a flexible way to monitor the health of various components of your application.
