# Pika Cheatsheet

## syntax

```ruby
orange and (dark or light)
price >= 42 and price < 100
reference == "OMO123"
reference != nil
invoice.reference != nil
unix(delivery_date) > now()
```

## Standard Library

| Function        |                Arguments                 |   Return Value |
| --------------- | :--------------------------------------: | -------------: |
| contains        |        in: string, value: string         |        boolean |
| unix            |                in: string                | unix timestamp |
| startsWith      |        in: string, value: string         |        boolean |
| endsWith        |        in: string, value: string         |        boolean |
| dateAdd         | in: string, amount: number, unit: string | unix timestamp |
| dateSub         | in: string, amount: number, unit: string | unix timestamp |
| datediff        |        date: string, date: string        |         number |
| datediffHours   |        date: string, date: string        |         number |
| datediffMinutes |        date: string, date: string        |         number |
