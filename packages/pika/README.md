# Pika

Pika is a boolean expression evaluation language for Node.js.

It provides a simple API to evaluate boolean expressions in a simple to understand language.

Think of it like the SQL WHERE clause.

## Use Cases

- Evaluating complex business rules that involve multiple conditions and variables
- Filtering data based on complex criteria
- Implementing validation rules for user input in a form or application

## Installation

Use the package manager [npm](https://www.npmjs.com/) to install Pika.

```bash
npm install @ordermentum/pika
# OR
yarn add @ordermentum/pika
```

## Usage

```
import { evaluate } from '@ordermentum/pika';

const context = {
  a: true,
  b: false,
  c: true
};

const expression = 'a and (b or c)';
const result =  evaluate(expression, context);
console.log(result); // false
```

## Testing

To run the tests for  Pika, use the following command:

yarn test
