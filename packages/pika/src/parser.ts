import * as Expr from './expr';
import { Token } from './scanner';
import { TokenTypes } from './token_types';

class ParserError extends Error {
  token: any;

  constructor(message: string, token: any) {
    super(message);
    this.name = 'ParserError';
    this.token = token;
  }
}

export class Parser {
  tokens: Token[];

  current: number = 0;

  constructor(tokens: Token[]) {
    this.tokens = tokens;
  }

  parse() {
    const statements: any[] = [];

    while (!this.isAtEnd()) {
      statements.push(this.expression());
    }

    return statements;
  }

  statement() {
    return this.expressionStatement();
  }

  expressionStatement() {
    const expr = this.expression();
    this.consume(TokenTypes.SEMICOLON, "Expect ';' after expression.");
    return new Expr.ExpressionStatement(expr);
  }

  expression() {
    return this.or();
  }

  equality() {
    let expr = this.comparison();

    while (this.match(TokenTypes.BANG_EQUAL, TokenTypes.EQUAL_EQUAL)) {
      const operator = this.previous();
      const right = this.comparison();
      expr = new Expr.BinaryExpression(expr, operator, right);
    }

    return expr;
  }

  comparison() {
    let expr = this.term();

    while (
      this.match(
        TokenTypes.GREATER,
        TokenTypes.GREATER_EQUAL,
        TokenTypes.LESS,
        TokenTypes.LESS_EQUAL,
        TokenTypes.EQUAL_EQUAL
      )
    ) {
      const operator = this.previous();
      const right = this.term();
      expr = new Expr.BinaryExpression(expr, operator, right);
    }

    return expr;
  }

  cast() {
    let expr = this.primary();

    while (this.match(TokenTypes.AS)) {
      const operator = this.previous();
      const right = this.primary();
      expr = new Expr.CastExpression(expr, operator, right);
    }

    return expr;
  }

  term() {
    let expr = this.factor();

    while (this.match(TokenTypes.MINUS, TokenTypes.PLUS)) {
      const operator = this.previous();
      const right = this.factor();
      expr = new Expr.BinaryExpression(expr, operator, right);
    }

    return expr;
  }

  factor() {
    let expr = this.unary();

    while (this.match(TokenTypes.SLASH, TokenTypes.STAR)) {
      const operator = this.previous();
      const right = this.unary();
      expr = new Expr.BinaryExpression(expr, operator, right);
    }

    return expr;
  }

  unary() {
    if (this.match(TokenTypes.BANG, TokenTypes.MINUS)) {
      const operator = this.previous();
      const right = this.unary();
      return new Expr.UnaryExpression(operator, right);
    }

    return this.cast();
  }

  primary() {
    if (this.match(TokenTypes.FALSE)) return new Expr.LiteralExpression(false);
    if (this.match(TokenTypes.TRUE)) return new Expr.LiteralExpression(true);
    if (this.match(TokenTypes.NIL)) return new Expr.LiteralExpression(null);

    if (this.match(TokenTypes.NUMBER, TokenTypes.STRING)) {
      return new Expr.LiteralExpression(this.previous().literal);
    }

    if (this.match(TokenTypes.IDENTIFIER)) {
      const previous = this.previous();
      if (!previous.text) {
        throw this.error(previous, 'Expect variable name.');
      }

      // is nested identifier
      if (this.match(TokenTypes.DOT)) {
        const object = this.primary();
        return new Expr.VariableExpression(previous.text, object);
      }
      // is function call
      if (this.match(TokenTypes.LEFT_PAREN)) {
        const args: any[] = [];
        if (!this.check(TokenTypes.RIGHT_PAREN)) {
          do {
            args.push(this.expression());
          } while (this.match(TokenTypes.COMMA));
        }
        this.consume(TokenTypes.RIGHT_PAREN, "Expect ')' after arguments.");
        return new Expr.CallExpression(
          new Expr.VariableExpression(previous.text),
          args
        );
      }

      return new Expr.VariableExpression(previous.text);
    }

    if (this.match(TokenTypes.LEFT_PAREN)) {
      const expr = this.expression();
      this.consume(TokenTypes.RIGHT_PAREN, "Expect ')' after expression.");
      return new Expr.GroupingExpression(expr);
    }

    throw this.error(this.peek(), 'Expect expression.');
  }

  consume(type: TokenTypes, message: string) {
    if (this.check(type)) {
      return this.advance();
    }
    throw this.error(this.peek(), message);
  }

  error(token: any, message: string) {
    return new ParserError(message, token);
  }

  synchronize() {
    this.advance();

    while (!this.isAtEnd()) {
      if (this.previous().type === TokenTypes.SEMICOLON) return;
      this.advance();
    }
  }

  private and() {
    let expr = this.equality();

    while (this.match(TokenTypes.AND)) {
      const operator = this.previous();
      const right = this.equality();
      expr = new Expr.LogicalExpression(expr, operator, right);
    }

    return expr;
  }

  private or() {
    let expr = this.and();

    while (this.match(TokenTypes.OR)) {
      const operator = this.previous();
      const right = this.and();
      expr = new Expr.LogicalExpression(expr, operator, right);
    }

    return expr;
  }

  match(...types: TokenTypes[]) {
    for (const type of types) {
      if (this.check(type)) {
        this.advance();
        return true;
      }
    }

    return false;
  }

  check(type: TokenTypes) {
    if (this.isAtEnd()) return false;
    return this.peek().type === type;
  }

  advance() {
    if (!this.isAtEnd()) this.current += 1;
    return this.previous();
  }

  isAtEnd() {
    return this.peek().type === TokenTypes.EOF;
  }

  peek() {
    return this.tokens[this.current];
  }

  previous() {
    return this.tokens[this.current - 1];
  }
}
