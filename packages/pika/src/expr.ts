/* eslint-disable no-empty-function */
/* eslint-disable max-classes-per-file */
import type { ASTPrinter } from './ast_printer';
import type { Interpreter } from './interpreter';

export class ExpressionStatement {
  constructor(public expression: any) {}

  print(printer: ASTPrinter) {
    return printer.printExpressionStatement(this);
  }

  accept(visitor: Interpreter) {
    return visitor.evaluate(this);
  }
}
export class VariableExpression {
  constructor(public name: string, public child?: any) {}

  print(printer: ASTPrinter) {
    return printer.printVariableExpression(this);
  }

  accept(visitor: Interpreter) {
    return visitor.variable(this);
  }
}

// call expression
export class CallExpression {
  constructor(public callee: VariableExpression, public args: any[]) {}

  print(printer: ASTPrinter) {
    return printer.printCallExpression(this);
  }

  accept(visitor: Interpreter) {
    return visitor.call(this);
  }
}

export class LogicalExpression {
  constructor(public left: any, public operator: any, public right: any) {}

  print(printer: ASTPrinter) {
    return printer.printLogicalExpression(this);
  }

  accept(visitor: Interpreter) {
    return visitor.logical(this);
  }
}

export class BinaryExpression {
  constructor(public left: any, public operator: any, public right: any) {}

  print(printer: ASTPrinter) {
    return printer.printBinaryExpression(this);
  }

  accept(visitor: Interpreter) {
    return visitor.binary(this);
  }
}
export class UnaryExpression {
  constructor(public operator: any, public right: any) {}

  print(printer: ASTPrinter) {
    return printer.printUnaryExpression(this);
  }

  accept(visitor: Interpreter) {
    return visitor.unary(this);
  }
}
export class LiteralExpression {
  constructor(public value: any) {}

  print(printer: ASTPrinter) {
    return printer.printLiteralExpression(this);
  }

  accept(visitor: Interpreter) {
    return visitor.literal(this);
  }
}
export class GroupingExpression {
  constructor(public expression: any) {}

  print(printer: ASTPrinter) {
    return printer.printGroupingExpression(this);
  }

  accept(visitor: Interpreter) {
    return visitor.grouping(this);
  }
}

export class CastExpression {
  constructor(public expression: any, public operator: any, public type: any) {}

  print(printer: ASTPrinter) {
    return printer.printCastExpression(this);
  }

  accept(visitor: Interpreter) {
    return visitor.cast(this);
  }
}
