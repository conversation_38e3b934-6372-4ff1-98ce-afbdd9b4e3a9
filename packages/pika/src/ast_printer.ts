export class ASTPrinter {
  print(expression: any) {
    if (!expression) return '';
    if (Array.isArray(expression)) {
      return expression.map(e => e.print(this)).join('');
    }
    return expression[0].print(this);
  }

  printExpressionStatement(statement: any) {
    return this.parenthesize(';', statement.expression);
  }

  printVariableExpression(expression: any) {
    if (expression.child) {
      return this.parenthesize(expression.name, expression.child);
    }
    return this.parenthesize(expression.name);
  }

  printBinaryExpression(expression: any) {
    return this.parenthesize(
      expression.operator.text,
      expression.left,
      expression.right
    );
  }

  printUnaryExpression(expression: any) {
    return this.parenthesize(expression.operator.text, expression.right);
  }

  printCallExpression(expression: any) {
    return this.parenthesize(`${expression.callee.name}`, ...expression.args);
  }

  printLiteralExpression(expression: any) {
    if (expression.value === null) return 'nil';
    return expression.value;
  }

  printLogicalExpression(expression: any) {
    return this.parenthesize(
      expression.operator.text,
      expression.left,
      expression.right
    );
  }

  printCastExpression(expression: any) {
    return this.parenthesize(expression.type.text, expression.value);
  }

  printGroupingExpression(expression: any) {
    return this.parenthesize('group', expression.expression);
  }

  private parenthesize(name: string, ...expressions: any[]) {
    let result = `(${name}`;

    for (const expression of expressions) {
      result += ` ${expression.print(this)}`;
    }
    result += ')';
    return result;
  }
}
