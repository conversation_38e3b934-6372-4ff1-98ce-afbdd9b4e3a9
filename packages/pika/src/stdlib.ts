// stdlib for pika
export const Stdlib = {
  now: () => {
    const date = new Date();
    return date.getTime();
  },
  unix: (value: string) => {
    const date = new Date(value);
    return date.getTime();
  },
  cast: (value: any, type: string) => {
    switch (type) {
      case 'date':
        return new Date(value);
      case 'string':
        return String(value);
      case 'number':
        return Number(value);
      case 'boolean':
        return Boolean(value);
      default:
        return value;
    }
  },
  contains: (str: string, substr: string) => str.includes(substr),
  startsWith: (str: string, substr: string) => str.startsWith(substr),
  endsWith: (str: string, substr: string) => str.endsWith(substr),

  dateAdd(date: string, amount: number, unit: string) {
    const d = new Date(date);
    switch (unit) {
      case 'year':
        d.setFullYear(d.getFullYear() + amount);
        break;
      case 'month':
        d.setMonth(d.getMonth() + amount);
        break;
      case 'day':
        d.setDate(d.getDate() + amount);
        break;
      case 'hour':
        d.setHours(d.getHours() + amount);
        break;
      case 'minute':
        d.setMinutes(d.getMinutes() + amount);
        break;
      case 'second':
        d.setSeconds(d.getSeconds() + amount);
        break;
      default:
        break;
    }
    return d.getTime();
  },

  dateSub(date: string, amount: number, unit: string) {
    const d = new Date(date);
    switch (unit) {
      case 'year':
        d.setFullYear(d.getFullYear() - amount);
        break;
      case 'month':
        d.setMonth(d.getMonth() - amount);
        break;
      case 'day':
        d.setDate(d.getDate() - amount);
        break;
      case 'hour':
        d.setHours(d.getHours() - amount);
        break;
      case 'minute':
        d.setMinutes(d.getMinutes() - amount);
        break;
      case 'second':
        d.setSeconds(d.getSeconds() - amount);
        break;
      default:
        break;
    }
    return d.getTime();
  },

  // date diff in days
  datediff: (date1: string, date2: string) => {
    const start = new Date(date1);
    const finish = new Date(date2);
    const diffTime = Math.abs(finish.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  },
  // date diff in hours
  datediffHours: (date1: string, date2: string) => {
    const start = new Date(date1);
    const finish = new Date(date2);
    const diffTime = Math.abs(finish.getTime() - start.getTime());
    const diffHours = Math.ceil(diffTime / (1000 * 60 * 60));
    return diffHours;
  },
  // date diff in minutes
  datediffMinutes: (date1: string, date2: string) => {
    const start = new Date(date1);
    const finish = new Date(date2);
    const diffTime = Math.abs(finish.getTime() - start.getTime());
    const diffMinutes = Math.ceil(diffTime / (1000 * 60));
    return diffMinutes;
  },
};
