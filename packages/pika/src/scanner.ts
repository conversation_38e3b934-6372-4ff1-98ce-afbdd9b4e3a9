import { TokenTypes } from './token_types';

export type Token = {
  type: TokenTypes;
  text?: string;
  literal?: any;
};

export const KeywordMap = new Map<string, TokenTypes>([
  ['and', TokenTypes.AND],
  ['false', TokenTypes.FALSE],
  ['nil', TokenTypes.NIL],
  ['or', TokenTypes.OR],
  ['as', TokenTypes.AS],
  ['true', TokenTypes.TRUE],
]);

export class Scanner {
  private source: string;

  private tokens: Token[] = [];

  private start: number = 0;

  private current: number = 0;

  private line: number = 1;

  constructor(source: string) {
    this.source = source;
  }

  scan() {
    while (!this.isAtEnd()) {
      // We are at the beginning of the next lexeme.
      this.start = this.current;
      this.scanToken();
    }

    this.tokens.push({
      type: TokenTypes.EOF,
      text: '',
      literal: undefined,
    });
    return this.tokens;
  }

  scanToken() {
    const c = this.advance();
    switch (c) {
      case '(':
        this.addToken(TokenTypes.LEFT_PAREN);
        break;
      case ')':
        this.addToken(TokenTypes.RIGHT_PAREN);
        break;
      case '{':
        this.addToken(TokenTypes.LEFT_BRACE);
        break;
      case '}':
        this.addToken(TokenTypes.RIGHT_BRACE);
        break;
      case ',':
        this.addToken(TokenTypes.COMMA);
        break;
      case '.':
        this.addToken(TokenTypes.DOT);
        break;
      case '-':
        this.addToken(TokenTypes.MINUS);
        break;
      case '+':
        this.addToken(TokenTypes.PLUS);
        break;
      case ';':
        this.addToken(TokenTypes.SEMICOLON);
        break;
      case '*':
        this.addToken(TokenTypes.STAR);
        break;
      case ':':
        this.addToken(
          this.match(':') ? TokenTypes.COLON_COLON : TokenTypes.COLON
        );
        break;
      case '!':
        this.addToken(
          this.match('=') ? TokenTypes.BANG_EQUAL : TokenTypes.BANG
        );
        break;
      case '=':
        this.addToken(
          this.match('=') ? TokenTypes.EQUAL_EQUAL : TokenTypes.EQUAL
        );
        break;
      case '<':
        this.addToken(
          this.match('=') ? TokenTypes.LESS_EQUAL : TokenTypes.LESS
        );
        break;
      case '>':
        this.addToken(
          this.match('=') ? TokenTypes.GREATER_EQUAL : TokenTypes.GREATER
        );
        break;
      case '/':
        if (this.match('/')) {
          // A comment goes until the end of the line.
          while (this.peek() !== '\n' && !this.isAtEnd()) {
            this.advance();
          }
        } else {
          this.addToken(TokenTypes.SLASH);
        }
        break;

      case ' ':
      case '\r':
      case '\t':
        // Ignore whitespace.
        break;

      case '\n':
        // Ignore whitespace.
        break;

      case '"':
        this.string();
        break;

      default:
        if (this.isDigit(c)) {
          this.number();
        } else if (this.isAlpha(c)) {
          this.identifier();
        } else {
          // eslint-disable-next-line no-console
          console.error(`Unexpected character: ${c}`);
        }
        break;
    }
  }

  identifier() {
    while (this.isAlphaNumeric(this.peek())) {
      this.advance();
    }
    const text = this.source.substring(this.start, this.current);
    const type = KeywordMap.get(text) || TokenTypes.IDENTIFIER;
    this.addToken(type);
  }

  isAlpha(c: string) {
    return (c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z') || c === '_';
  }

  isAlphaNumeric(c: string) {
    return this.isAlpha(c) || this.isDigit(c);
  }

  isDigit(c: string) {
    return c >= '0' && c <= '9';
  }

  number() {
    while (this.isDigit(this.peek())) {
      this.advance();
    }
    // Look for a fractional part.
    if (this.peek() === '.' && this.isDigit(this.peekNext())) {
      // Consume the "."
      this.advance();
      while (this.isDigit(this.peek())) {
        this.advance();
      }
    }
    this.addToken(
      TokenTypes.NUMBER,
      Number(this.source.substring(this.start, this.current))
    );
  }

  peek() {
    if (this.isAtEnd()) {
      return '\0';
    }
    return this.source[this.current];
  }

  peekNext() {
    if (this.current + 1 >= this.source.length) {
      return '\0';
    }
    return this.source[this.current + 1];
  }

  string() {
    while (this.peek() !== '"' && !this.isAtEnd()) {
      if (this.peek() === '\n') this.line += 1;
      this.advance();
    }

    if (this.isAtEnd()) {
      return;
    }

    // The closing ".
    this.advance();

    // Trim the surrounding quotes.
    const value: string = this.source.substring(
      this.start + 1,
      this.current - 1
    );
    this.addToken(TokenTypes.STRING, value);
  }

  match(expected: string) {
    if (this.isAtEnd()) {
      return false;
    }
    if (this.source[this.current] !== expected) {
      return false;
    }
    this.current += 1;
    return true;
  }

  addToken(type: TokenTypes, literal?: any) {
    const text = this.source.substring(this.start, this.current);
    this.tokens.push({
      type,
      text,
      literal,
    });
  }

  isAtEnd() {
    return this.current >= this.source.length;
  }

  advance() {
    this.current += 1;
    return this.source[this.current - 1];
  }
}
