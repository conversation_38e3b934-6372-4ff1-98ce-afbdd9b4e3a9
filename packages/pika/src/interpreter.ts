import { TokenTypes } from './token_types';
import { Stdlib } from './stdlib';

export class RuntimeError extends Error {}
export class Interpreter {
  public context: Record<string, any>;

  constructor(context: Record<string, any>) {
    this.context = context;
  }

  exec(statements: any[]) {
    let result;
    for (const value of statements) {
      result = this.evaluate(value);
    }
    return result;
  }

  evaluate(expression: any) {
    const val = expression.accept(this);
    return val;
  }

  literal(expression: any) {
    return expression.value;
  }

  grouping(expression: any) {
    return this.evaluate(expression.expression);
  }

  unary(expression: any) {
    const right = this.evaluate(expression.right);
    switch (expression.operator.type) {
      case TokenTypes.MINUS:
        return -right;
      case TokenTypes.BANG:
        return !right;
      default:
        throw new RuntimeError('Invalid unary operator.');
    }
  }

  logical(expression: any) {
    const left = this.evaluate(expression.left);

    if (expression.operator.type === TokenTypes.OR) {
      if (this.isTruthy(left)) return left;
    } else if (!this.isTruthy(left)) return left;

    return this.evaluate(expression.right);
  }

  call(expression: any) {
    const callee =
      Stdlib[expression.callee.name] ?? this.variable(expression.callee);
    const args = expression.args.map((arg: any) => this.evaluate(arg));

    if (typeof callee !== 'function') {
      throw new RuntimeError('Can only call functions and classes.');
    }

    return callee(...args);
  }

  cast(expression: any) {
    const value = this.evaluate(expression.value);
    if (expression.type) {
      if (expression.type.text === 'number') return Number(value);
      if (expression.type.text === 'string') return String(value);
      if (expression.type.text === 'boolean') return Boolean(value);
      if (expression.type.text === 'date') return Boolean(value);
    }
    throw new RuntimeError('Invalid cast operator.');
  }

  isTruthy(value: any) {
    if (value === null) return false;
    if (typeof value === 'boolean') return value;
    return true;
  }

  binary(expression: any) {
    const left = this.evaluate(expression.left);
    const right = this.evaluate(expression.right);

    switch (expression.operator.type) {
      case TokenTypes.MINUS:
        return left - right;
      case TokenTypes.PLUS:
        return left + right;
      case TokenTypes.SLASH:
        return left / right;
      case TokenTypes.STAR:
        return left * right;
      case TokenTypes.GREATER:
        return left > right;
      case TokenTypes.GREATER_EQUAL:
        return left >= right;
      case TokenTypes.LESS:
        return left < right;
      case TokenTypes.LESS_EQUAL:
        return left <= right;
      case TokenTypes.BANG_EQUAL:
        return left !== right;
      case TokenTypes.EQUAL_EQUAL:
        return left === right;
      default:
        throw new RuntimeError('Invalid binary operator.');
    }
  }

  variable(expression: any, value?: any) {
    // FIXME: should we implement optional chaining and throw an exception in case of undefined?
    // if value provided, we use that for context and continue to loop to the end
    if (value) {
      if (expression.child) {
        const parent = value?.[expression.name];
        return this.variable(expression.child, parent);
      }
      return value?.[expression.name];
    }
    return this.variable(expression, this.context);
  }
}
