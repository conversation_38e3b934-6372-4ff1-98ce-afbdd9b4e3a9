import { <PERSON>anner } from './scanner';
import { Interpreter } from './interpreter';
import { Parser } from './parser';
import { ASTPrinter } from './ast_printer';

export const evaluate = (expression: string, context: Record<string, any>) => {
  const scanner = new Scanner(expression);
  const tokens = scanner.scan();
  const parser = new Parser(tokens);
  const ast = parser.parse();
  const interpreter = new Interpreter(context);
  return interpreter.exec(ast);
};

export const print = (expression: string) => {
  const scanner = new Scanner(expression);
  const tokens = scanner.scan();
  const parser = new Parser(tokens);
  const ast = parser.parse();

  if (!ast) {
    return '';
  }
  return new ASTPrinter().print(ast);
};

export default evaluate;
