{"name": "@ordermentum/pika", "version": "0.1.0", "main": "build/index.js", "types": "build/index.d.ts", "files": ["build/*"], "license": "UNLICENSED", "scripts": {"spec": "yarn run mocha --require ts-node/register --extensions ts,tsx 'test/**/*.{ts,tsx}'", "prepublish": "yarn run build", "build": "tsc"}, "dependencies": {}, "devDependencies": {"@types/chai": "^4.3.6", "@types/mocha": "^10.0.2", "chai": "^4.3.10", "mocha": "^10.2.0", "ts-node": "^10.9.1", "typescript": "^5.2.2"}}