import { expect } from 'chai';
import { Parser } from '../src/parser';
import { TokenTypes } from '../src/token_types';

describe('#parse', () => {
  it('should return an empty array for an empty input', () => {
    const parser = new Parser([{ type: TokenTypes.EOF }]);
    const result = parser.parse();
    expect(result).to.deep.equal([]);
  });

  it('should parse a simple expression', () => {
    const tokens: any[] = [
      { type: 'LEFT_PAREN', literal: undefined, text: '(' },
      { type: 'NUMBER', literal: 2, text: '2' },
      { type: 'STAR', literal: undefined, text: '*' },
      { type: 'NUMBER', literal: 3, text: '3' },
      { type: 'RIGHT_PAREN', literal: undefined, text: ')' },
      { type: 'EOF', literal: undefined, text: '' },
    ];
    const parser = new Parser(tokens);
    const result = parser.parse();
    expect(result).to.deep.equal([
      {
        expression: {
          left: { value: 2 },
          operator: { literal: undefined, type: 'STAR', text: '*' },
          right: { value: 3 },
        },
      },
    ]);
  });

  it('should handle operator precedence correctly', () => {
    const tokens: any[] = [
      { type: 'LEFT_PAREN', literal: undefined, text: '(' },
      { type: 'NUMBER', literal: 1, text: '1' },
      { type: 'PLUS', literal: undefined, text: '+' },
      { type: 'NUMBER', literal: 2, text: '2' },
      { type: 'RIGHT_PAREN', literal: undefined, text: ')' },
      { type: 'STAR', literal: undefined, text: '*' },
      { type: 'NUMBER', literal: 3, text: '3' },
      { type: 'EOF', literal: undefined, text: '' },
    ];
    const parser = new Parser(tokens);
    const result = parser.parse();
    expect(result).to.deep.equal([
      {
        left: {
          expression: {
            left: { value: 1 },
            operator: { literal: undefined, type: 'PLUS', text: '+' },
            right: { value: 2 },
          },
        },
        operator: { literal: undefined, type: 'STAR', text: '*' },
        right: { value: 3 },
      },
    ]);
  });

  it('should parse statements with and/or', () => {
    const tokens: any[] = [
      { type: 'FALSE', literal: undefined, text: 'false' },
      { type: 'OR', literal: undefined, text: 'or' },
      { type: 'FALSE', literal: undefined, text: 'false' },
      { type: 'AND', literal: undefined, text: 'and' },
      { type: 'TRUE', literal: undefined, text: 'true' },
      { type: 'EOF', literal: undefined, text: '' },
    ];
    const parser = new Parser(tokens);
    const result = parser.parse();
    expect(result).to.deep.equal([
      {
        left: { value: false },
        operator: { literal: undefined, type: 'OR', text: 'or' },
        right: {
          left: { value: false },
          operator: {
            literal: undefined,
            type: 'AND',
            text: 'and',
          },
          right: { value: true },
        },
      },
    ]);
  });

  it('should handle parentheses correctly', () => {
    const tokens: any[] = [
      { type: 'NUMBER', literal: 1, text: '1' },
      { type: 'PLUS', literal: undefined, text: '+' },
      { type: 'LEFT_PAREN', literal: undefined, text: '(' },
      { type: 'NUMBER', literal: 2, text: '2' },
      { type: 'STAR', literal: undefined, text: '*' },
      { type: 'LEFT_PAREN', literal: undefined, text: '(' },
      { type: 'NUMBER', literal: 3, text: '3' },
      { type: 'MINUS', literal: undefined, text: '-' },
      { type: 'NUMBER', literal: 4, text: '4' },
      { type: 'RIGHT_PAREN', literal: undefined, text: ')' },
      { type: 'RIGHT_PAREN', literal: undefined, text: ')' },
      { type: 'EOF', literal: undefined, text: '' },
    ];
    const parser = new Parser(tokens);
    const result = parser.parse();
    expect(result).to.deep.equal([
      {
        left: { value: 1 },
        operator: { literal: undefined, type: 'PLUS', text: '+' },
        right: {
          expression: {
            left: { value: 2 },
            operator: { literal: undefined, type: 'STAR', text: '*' },
            right: {
              expression: {
                left: { value: 3 },
                operator: { literal: undefined, type: 'MINUS', text: '-' },
                right: { value: 4 },
              },
            },
          },
        },
      },
    ]);
  });

  it.skip('should handle as cast', () => {
    const tokens: any[] = [
      { type: 'STRING', text: '"true"', literal: 'true' },
      { type: 'AS', literal: undefined, text: 'as' },
      { type: 'BOOLEAN_TYPE', literal: undefined, text: 'boolean' },
      { type: 'EOF', literal: undefined, text: '' },
    ];
    const parser = new Parser(tokens);
    const result = parser.parse();
    expect(result).to.deep.equal([
      {
        expression: { value: 'a' },
        operator: { literal: undefined, type: 'AS', text: 'as' },
        type: { name: 'b', child: undefined },
      },
    ]);
  });
});
