import { expect } from 'chai';
import { <PERSON>anner } from '../src/scanner';

describe('Scanner', () => {
  it('should tokenize a simple expression with addition', () => {
    const input = '1 + 2';
    const scanner = new Scanner(input);
    const tokens = scanner.scan();
    expect(tokens).to.deep.equal([
      { type: 'NUMBER', literal: 1, text: '1' },
      { type: 'PLUS', literal: undefined, text: '+' },
      { type: 'NUMBER', literal: 2, text: '2' },
      { type: 'EOF', literal: undefined, text: '' },
    ]);
  });

  it('should tokenize a simple expression with subtraction', () => {
    const input = '1 - 2';
    const scanner = new Scanner(input);
    const tokens = scanner.scan();
    expect(tokens).to.deep.equal([
      { type: 'NUMBER', literal: 1, text: '1' },
      { type: 'MINUS', literal: undefined, text: '-' },
      { type: 'NUMBER', literal: 2, text: '2' },
      { type: 'EOF', literal: undefined, text: '' },
    ]);
  });

  it('should tokenize an expression with dot notation', () => {
    const input = 'a.b';
    const scanner = new Scanner(input);
    const tokens = scanner.scan();
    expect(tokens).to.deep.equal([
      { type: 'IDENTIFIER', literal: undefined, text: 'a' },
      { type: 'DOT', literal: undefined, text: '.' },
      { type: 'IDENTIFIER', literal: undefined, text: 'b' },
      { type: 'EOF', literal: undefined, text: '' },
    ]);
  });

  it('should tokenize a simple expression with multiplication', () => {
    const input = '2 * 3';
    const scanner = new Scanner(input);
    const tokens = scanner.scan();
    expect(tokens).to.deep.equal([
      { type: 'NUMBER', literal: 2, text: '2' },
      { type: 'STAR', literal: undefined, text: '*' },
      { type: 'NUMBER', literal: 3, text: '3' },
      { type: 'EOF', literal: undefined, text: '' },
    ]);
  });

  it('should tokenize a simple expression with division', () => {
    const input = '6 / 2';
    const scanner = new Scanner(input);
    const tokens = scanner.scan();
    expect(tokens).to.deep.equal([
      { type: 'NUMBER', literal: 6, text: '6' },
      { type: 'SLASH', literal: undefined, text: '/' },
      { type: 'NUMBER', literal: 2, text: '2' },
      { type: 'EOF', literal: undefined, text: '' },
    ]);
  });

  it.skip('should tokenize a simple expression with modulo', () => {
    const input = '7 % 3';
    const scanner = new Scanner(input);
    const tokens = scanner.scan();
    expect(tokens).to.deep.equal([
      { type: 'NUMBER', literal: 7, text: '7' },
      { type: 'PERCENT', literal: undefined, text: '%' },
      { type: 'NUMBER', literal: 3, text: '3' },
      { type: 'EOF', literal: undefined, text: '' },
    ]);
  });

  it('should tokenize a simple expression with grouping', () => {
    const input = '(1 + 2) * 3';
    const scanner = new Scanner(input);
    const tokens = scanner.scan();
    expect(tokens).to.deep.equal([
      { type: 'LEFT_PAREN', literal: undefined, text: '(' },
      { type: 'NUMBER', literal: 1, text: '1' },
      { type: 'PLUS', literal: undefined, text: '+' },
      { type: 'NUMBER', literal: 2, text: '2' },
      { type: 'RIGHT_PAREN', literal: undefined, text: ')' },
      { type: 'STAR', literal: undefined, text: '*' },
      { type: 'NUMBER', literal: 3, text: '3' },
      { type: 'EOF', literal: undefined, text: '' },
    ]);
  });

  it('should handle nested grouping', () => {
    const input = '1 + (2 * (3 - 4))';
    const scanner = new Scanner(input);
    const tokens = scanner.scan();
    expect(tokens).to.deep.equal([
      { type: 'NUMBER', literal: 1, text: '1' },
      { type: 'PLUS', literal: undefined, text: '+' },
      { type: 'LEFT_PAREN', literal: undefined, text: '(' },
      { type: 'NUMBER', literal: 2, text: '2' },
      { type: 'STAR', literal: undefined, text: '*' },
      { type: 'LEFT_PAREN', literal: undefined, text: '(' },
      { type: 'NUMBER', literal: 3, text: '3' },
      { type: 'MINUS', literal: undefined, text: '-' },
      { type: 'NUMBER', literal: 4, text: '4' },
      { type: 'RIGHT_PAREN', literal: undefined, text: ')' },
      { type: 'RIGHT_PAREN', literal: undefined, text: ')' },
      { type: 'EOF', literal: undefined, text: '' },
    ]);
  });

  it('should tokenize a simple string', () => {
    const input = '"hello, world"';
    const scanner = new Scanner(input);
    const tokens = scanner.scan();
    expect(tokens).to.deep.equal([
      { type: 'STRING', literal: 'hello, world', text: input },
      { type: 'EOF', literal: undefined, text: '' },
    ]);
  });

  it('should tokenize a simple expression with greater than', () => {
    const input = '1 > 2';
    const scanner = new Scanner(input);
    const tokens = scanner.scan();
    expect(tokens).to.deep.equal([
      { type: 'NUMBER', literal: 1, text: '1' },
      { type: 'GREATER', literal: undefined, text: '>' },
      { type: 'NUMBER', literal: 2, text: '2' },
      { type: 'EOF', literal: undefined, text: '' },
    ]);
  });

  it('should tokenize a simple expression with less than', () => {
    const input = '3 < 4';
    const scanner = new Scanner(input);
    const tokens = scanner.scan();
    expect(tokens).to.deep.equal([
      { type: 'NUMBER', literal: 3, text: '3' },
      { type: 'LESS', literal: undefined, text: '<' },
      { type: 'NUMBER', literal: 4, text: '4' },
      { type: 'EOF', literal: undefined, text: '' },
    ]);
  });
  it('should tokenize a simple identifier', () => {
    const input = 'foo';
    const scanner = new Scanner(input);
    const tokens = scanner.scan();
    expect(tokens).to.deep.equal([
      { type: 'IDENTIFIER', literal: undefined, text: input },
      { type: 'EOF', literal: undefined, text: '' },
    ]);
  });

  it('should tokenize an identifier with digits', () => {
    const input = 'bar123';
    const scanner = new Scanner(input);
    const tokens = scanner.scan();
    expect(tokens).to.deep.equal([
      { type: 'IDENTIFIER', literal: undefined, text: input },
      { type: 'EOF', literal: undefined, text: '' },
    ]);
  });

  it('should tokenize an identifier with underscores', () => {
    const input = '_baz_qux_';
    const scanner = new Scanner(input);
    const tokens = scanner.scan();
    expect(tokens).to.deep.equal([
      { type: 'IDENTIFIER', literal: undefined, text: input },
      { type: 'EOF', literal: undefined, text: '' },
    ]);
  });

  it('should tokenize the true keyword', () => {
    const input = 'true';
    const scanner = new Scanner(input);
    const tokens = scanner.scan();
    expect(tokens).to.deep.equal([
      { type: 'TRUE', literal: undefined, text: input },
      { type: 'EOF', literal: undefined, text: '' },
    ]);
  });

  it('should tokenize the false keyword', () => {
    const input = 'false';
    const scanner = new Scanner(input);
    const tokens = scanner.scan();
    expect(tokens).to.deep.equal([
      { type: 'FALSE', literal: undefined, text: input },
      { type: 'EOF', literal: undefined, text: '' },
    ]);
  });

  it('should tokenize the nil keyword', () => {
    const input = 'nil';
    const scanner = new Scanner(input);
    const tokens = scanner.scan();
    expect(tokens).to.deep.equal([
      { type: 'NIL', literal: undefined, text: input },
      { type: 'EOF', literal: undefined, text: '' },
    ]);
  });

  it('should parse AND ', () => {
    const input = 'and';
    const scanner = new Scanner(input);
    const tokens = scanner.scan();
    expect(tokens).to.deep.equal([
      { type: 'AND', literal: undefined, text: input },
      { type: 'EOF', literal: undefined, text: '' },
    ]);
  });

  it('should parse OR', () => {
    const input = 'or';
    const scanner = new Scanner(input);
    const tokens = scanner.scan();
    expect(tokens).to.deep.equal([
      { type: 'OR', literal: undefined, text: input },
      { type: 'EOF', literal: undefined, text: '' },
    ]);
  });

  it('should parse combined and or', () => {
    const input = 'A and B or C';
    const scanner = new Scanner(input);
    const tokens = scanner.scan();
    expect(tokens).to.deep.equal([
      { type: 'IDENTIFIER', literal: undefined, text: 'A' },
      { type: 'AND', literal: undefined, text: 'and' },
      { type: 'IDENTIFIER', literal: undefined, text: 'B' },
      { type: 'OR', literal: undefined, text: 'or' },
      { type: 'IDENTIFIER', literal: undefined, text: 'C' },
      { type: 'EOF', literal: undefined, text: '' },
    ]);
  });

  // FIXME: the scanner needs to know about types for this to be implemented in the parser
  describe('casting', () => {
    it('should parse double colon', () => {
      const input = 'a::string';
      const scanner = new Scanner(input);
      const tokens = scanner.scan();
      expect(tokens).to.deep.equal([
        { type: 'IDENTIFIER', literal: undefined, text: 'a' },
        { type: 'COLON_COLON', literal: undefined, text: '::' },
        { type: 'IDENTIFIER', literal: undefined, text: 'string' },
        { type: 'EOF', literal: undefined, text: '' },
      ]);
    });

    it('should parse string with double colon cast', () => {
      const input = '"1.0"::number';
      const scanner = new Scanner(input);
      const tokens = scanner.scan();
      expect(tokens).to.deep.equal([
        { type: 'STRING', literal: '1.0', text: '"1.0"' },
        { type: 'COLON_COLON', literal: undefined, text: '::' },
        { type: 'IDENTIFIER', literal: undefined, text: 'number' },
        { type: 'EOF', literal: undefined, text: '' },
      ]);
    });

    it('should parse as casts', () => {
      const input = '"true" as boolean';
      const scanner = new Scanner(input);
      const tokens = scanner.scan();
      expect(tokens).to.deep.equal([
        { type: 'STRING', text: '"true"', literal: 'true' },
        { type: 'AS', literal: undefined, text: 'as' },
        { type: 'IDENTIFIER', literal: undefined, text: 'boolean' },
        { type: 'EOF', literal: undefined, text: '' },
      ]);
    });
  });
});
