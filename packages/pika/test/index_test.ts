import { expect } from 'chai';
import { evaluate } from '../src';

describe('index', () => {
  it('should evaluate true for a simple and expression', () => {
    const expression = 'A and B';
    const context = { A: true, B: true };
    const result = evaluate(expression, context);
    expect(result).to.equal(true);
  });

  it('should evaluate false for a simple or expression', () => {
    const expression = 'A or B';
    const context = { A: false, B: false };
    const result = evaluate(expression, context);
    expect(result).to.equal(false);
  });

  it('should evaluate false for an expression with NOT operator', () => {
    const expression = '!B';
    const context = { B: true };
    const result = evaluate(expression, context);
    expect(result).to.equal(false);
  });

  it('should evaluate true for a complex expression with parentheses', () => {
    const expression = '(A and B) or (C and D)';
    const context = { A: true, B: true, C: true, D: false };
    const result = evaluate(expression, context);
    expect(result).to.equal(true);
  });

  it('should evaluate false for an expression with NOT operator', () => {
    const expression = 'A and !B';
    const context = { A: true, B: true };
    const result = evaluate(expression, context);
    expect(result).to.equal(false);
  });

  it('should evaluate true for an expression with greater than operator', () => {
    const expression = 'A > B';

    const context = { A: 5, B: 3 };
    const result = evaluate(expression, context);
    expect(result).to.equal(true);
  });

  it('should evaluate false for an expression with less than operator', () => {
    const expression = 'A < B';
    const context = { A: 10, B: 5 };
    const result = evaluate(expression, context);
    expect(result).to.equal(false);
  });

  it.skip('should throw an error for an invalid expression', () => {
    const expression = 'A &';
    const context = { A: true };
    expect(() => evaluate(expression, context)).to.throw('Invalid expression.');
  });

  it.skip('should throw an error for an undefined variable', () => {
    const expression = 'A and B';
    const context = { A: true };
    expect(() => evaluate(expression, context)).to.throw(
      'Variable "B" not found in the context.'
    );
  });

  it('should evaluate true for a complex expression with multiple operators', () => {
    const expression = 'A and B or C > D and !E';
    const context = { A: true, B: true, C: 10, D: 5, E: false };
    const result = evaluate(expression, context);
    expect(result).to.equal(true);
  });

  it('should evaluate true for an expression with all operators', () => {
    const expression = 'A and B or C > D and (!E or F)';
    const context = { A: true, B: true, C: 10, D: 5, E: true, F: true };
    const result = evaluate(expression, context);
    expect(result).to.equal(true);
  });

  it('should evaluate true for an expression with a mix of booleans and numbers', () => {
    const expression = '(A and B) or (C > D) and E';
    const context: Record<string, any> = {
      A: true,
      B: true,
      C: 5,
      D: 3,
      E: true,
    };
    const result = evaluate(expression, context);
    expect(result).to.equal(true);
  });

  it('should support literal values', () => {
    const expression = 'true and false';
    const result = evaluate(expression, {});
    expect(result).to.equal(false);
  });

  it('should support numeric values', () => {
    const expression = '5 > 3';
    const result = evaluate(expression, {});
    expect(result).to.equal(true);
  });

  it('should support function calls', () => {
    const expression = 'A()';
    const context = { A: () => 'dog' };
    const result = evaluate(expression, context);
    expect(result).to.equal('dog');
  });

  it('should support function calls with arguments', () => {
    const expression = 'A(1, 2)';
    const context = { A: (a: number, b: number) => a + b };
    const result = evaluate(expression, context);
    expect(result).to.equal(3);
  });

  it('should support dot notation', () => {
    const expression = 'A.B > 3';
    const context = { A: { B: 7 } };
    const result = evaluate(expression, context);
    expect(result).to.equal(true);
  });

  it('should support long and complex using every operator and feature and literal values', () => {
    const expression =
      'A and B or C > D and (!E or F) and 10 > 5 and 5 > 3 and true and a() and O.B > 3 and a(5, 2) and reference == "OMO123"';
    const context = {
      A: true,
      B: true,
      C: 10,
      D: 5,
      E: true,
      F: true,
      reference: 'OMO123',
      a: (a: number, b: number) => a > b,
      O: { B: 7 },
    };
    const result = evaluate(expression, context);
    expect(result).to.equal(true);
  });

  it('should support nested dot notation', () => {
    const expression = 'A.B.C > 10';
    const context = { A: { B: { C: 500 } } };
    const result = evaluate(expression, context);
    expect(result).to.equal(true);
  });

  it.skip('should support dot notation with function calls', () => {
    const expression = 'A.B()';
    const context = { A: { B: () => 'dog' } };
    const result = evaluate(expression, context);
    expect(result).to.equal('dog');
  });

  it('support stdlib functions', () => {
    const expression = 'contains("hello", "ll")';
    const result = evaluate(expression, {});
    expect(result).to.equal(true);
  });

  it('should support unix functions', () => {
    const expression = 'unix("2020-01-01") > unix("2019-01-01")';
    const result = evaluate(expression, {});
    expect(result).to.equal(true);
  });

  it('should support SQL casting', () => {
    const expression = 'cast("123", "number") > 100';
    const result = evaluate(expression, {});
    expect(result).to.equal(true);
  });

  it('should support standard fraud test', () => {
    const context = {
      order: {
        total: 100,
      },
      retailer: {
        name: 'bad man',
        address: {
          suburb: 'lalor',
        },
      },
      isFirstOrder: true,
    };

    const expression =
      '(retailer.address.suburb == "Lalor" or retailer.address.suburb == "lalor") and isFirstOrder';
    const result = evaluate(expression, context);
    expect(result).to.equal(true);
  });

  it('should support date comparison', () => {
    const context = {
      retailer: {
        name: 'bad man',
        createdAt: new Date(2018, 1, 1).toISOString(),
      },
      isFirstOrder: true,
    };

    const expression =
      'unix(retailer.createdAt) > dateSub(now(), 90, "days") and isFirstOrder';
    const result = evaluate(expression, context);
    expect(result).to.equal(false);
  });


  it('should support standard fraud test (verbose)', () => {
    const context = {
      order: {
        total: 100,
      },
      retailer: {
        name: 'bad man',
        address: {
          suburb: 'lalor',
        },
      },
      isFirstOrder: true,
    };

    const expression =
      'contains(retailer.address.suburb, "lalor") and isFirstOrder == true';
    const result = evaluate(expression, context);
    expect(result).to.equal(true);

    const context2 = { ...context };
    context2.retailer.address.suburb = 'test';
    const result2 = evaluate(expression, context2);
    expect(result2).to.equal(false);
  });

  it.skip('should support postgresql casting', () => {
    const expression = '"123"::number > 100';
    const result = evaluate(expression, {});
    expect(result).to.equal(true);
  });
});
