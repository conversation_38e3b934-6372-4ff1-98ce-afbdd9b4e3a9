import { expect } from 'chai';
import { evaluate, print } from '../src';

describe('ASPrinter', () => {
  describe('print', () => {
    it('should print a simple AST', () => {
      const expression = '1 + 2';
      const result = print(expression);
      expect(result).to.equal('(+ 1 2)');
    });

    it('resolves sample math statement', () => {
      const expression = '1 + 2 * 3 - 3 / 4';
      const pretty = print(expression);
      expect(pretty).to.deep.equal('(- (+ 1 (* 2 3)) (/ 3 4))');
      const result = evaluate(expression, {});
      expect(result).to.equal(6.25);
    });

    it('should print true for a simple and expression', () => {
      const expression = '(A and B)';
      const pretty = print(expression);
      expect(pretty).to.equal('(group (and (A) (B)))');
    });

    it('should print dot accessor', () => {
      const expression = 'A.B and C';
      const pretty = print(expression);
      expect(pretty).to.equal('(and (A (B)) (C))');
    });

    it('support function calls', () => {
      const expression = 'B() and C';
      const pretty = print(expression);
      // FIXME: this isn't right, but it's close
      expect(pretty).to.equal('(and (B) (C))');
    });

    it('should support function calls with arguments', () => {
      const expression = 'B(1, 2) and C';
      const pretty = print(expression);
      // FIXME: this isn't right, but it's close
      expect(pretty).to.equal('(and (B 1 2) (C))');
    });

    it('should print a more complex with groups', () => {
      const expression = '1 + 2 * 3';
      const result = print(expression);
      expect(result).to.equal('(+ 1 (* 2 3))');
    });
  });
});
