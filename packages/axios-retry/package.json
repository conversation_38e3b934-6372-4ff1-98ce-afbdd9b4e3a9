{"name": "@ordermentum/axios-retry", "version": "1.0.1", "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "files": ["build/*"], "scripts": {"build": "yarn clean && tsc", "clean": "rm -rf build", "format": "eslint --fix '**/*.{ts,js}'", "typecheck": "tsc --noEmit"}, "dependencies": {"axios-retry": "^3.8.0"}, "devDependencies": {"eslint-config-custom": "*", "tsconfig": "*"}, "peerDependencies": {"axios": ">= 1.5.0"}}