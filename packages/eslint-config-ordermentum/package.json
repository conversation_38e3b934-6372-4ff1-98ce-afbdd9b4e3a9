{"name": "@ordermentum/eslint-config-ordermentum", "version": "3.0.1", "main": "./index.js", "repository": "**************:ordermentum/libs.git", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "devDependencies": {"@changesets/cli": "^2.27.1", "@emotion/eslint-plugin": "^11.10", "@typescript-eslint/eslint-plugin": "^8.17.0", "@typescript-eslint/parser": "^8.17.0", "eslint": "^8.57.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-jsx-no-leaked-values": "^0.1.24", "eslint-plugin-mocha": "^10.0.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-react": "^7.32.1", "eslint-plugin-react-hooks": "^4", "eslint-plugin-tailwindcss": "^3.17.5", "eslint-plugin-unicorn": "^45.0.2", "prettier": "^2.8.3", "typescript": "^4.9.4"}, "peerDependencies": {"@emotion/eslint-plugin": "^11.10", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^8.32.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-jsx-no-leaked-values": "^0.1.24", "eslint-plugin-mocha": "^10.0.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-promise": "^7.0.0", "eslint-plugin-react": "^7.32.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-tailwindcss": "^3.17.5", "eslint-plugin-unicorn": "^56.0.0", "prettier": "^2.8.3"}}