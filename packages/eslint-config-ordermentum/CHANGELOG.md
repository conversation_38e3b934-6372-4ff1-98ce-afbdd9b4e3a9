# @ordermentum/eslint-config-ordermentum

## 3.0.1

### Patch Changes

- db8ca12: Add no floating promises as a warn statement

## 3.0.0

### Major Changes

- eb56c36: Major bumps in peerDependencies

### Minor Changes

- eb56c36: Move eslint-config-ordermentum to libs

## 2.6.1

### Patch Changes

- c60f52f: Fix extend of eslint-plugin-tailwind

## 2.6.0

### Minor Changes

- 30fc640: Add eslint-plugin-tailwind to browser config

## 2.5.0

### Minor Changes

- 4d64902: Disable prop-types checks in typescript files

## 2.4.0

### Minor Changes

- 66bacbf: Add eslint-plugin-jsx-no-leaked-values

### Patch Changes

- 55e9bb5: Minor fixes for typescript specific rules

## 2.3.3

### Patch Changes

- 5b8a2fa: Apply @typescript-eslint rules only in ts files

## 2.3.2

### Patch Changes

- feb04f6: Correct rule name declaration '@typescript-eslint/no-unnecessary-condition'

## 2.3.1

### Patch Changes

- 253395d: Updated changeset command to version before publishing

## 2.3.0

### Minor Changes

- 55bc092: Add react hooks rules and exhaustive deps warnings

### Patch Changes

- e151d55: warn on unnecessary conditional in typescript'
