/* eslint-disable no-unused-expressions */
import sinon from 'sinon';
import axios, { AxiosError } from 'axios';
import bunyan from 'bunyan';
import { describe, it, beforeEach, afterEach } from 'mocha';
import { expect } from './setup';
import { Client, jitter } from '../src/client';
import { TokensResponse } from '../src/types';

describe('Zai HTTP client tests', () => {
  let sandbox: sinon.SinonSandbox;
  let mockAxiosInstance: any;
  let mockLogger: any;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    mockAxiosInstance = {
      create: sandbox.stub(),
      request: sandbox.stub(),
      interceptors: {
        response: {
          use: sandbox.stub(),
        },
      },
    };
    mockLogger = {
      debug: sandbox.stub(),
      error: sandbox.stub(),
      createLogger: sandbox.stub(),
    };

    sandbox.stub(axios, 'create').returns(mockAxiosInstance);
    sandbox.stub(bunyan, 'createLogger').returns(mockLogger);
  });

  afterEach(() => {
    sandbox.restore();
  });

  // AuthClient is not exported from the client module, so we test its functionality through the Client class

  describe('Client', () => {
    const validOptions = {
      clientId: 'test-client-id',
      clientSecret: 'test-client-secret',
      scope: 'test-scope',
    };

    it('should create a Client with default options', () => {
      const client = new Client(validOptions);

      expect(client.clientId).to.equal('test-client-id');
      expect(client.clientSecret).to.equal('test-client-secret');
      expect(client.scope).to.equal('test-scope');
      expect(client.baseURL).to.equal('https://secure.api.promisepay.com');
      expect(client.authBaseURL).to.equal(
        'https://au-0000.auth.assemblypay.com/'
      );
      expect(client.retries).to.equal(3);
      expect(client.authClient).to.be.an('object');
    });

    it('should create a Client with custom options', () => {
      const customOptions = {
        ...validOptions,
        baseURL: 'https://custom.api.example.com',
        dataURL: 'https://custom-data.api.example.com/',
        authBaseURL: 'https://custom-auth.example.com/',
        timeout: 60000,
        retries: 5,
      };

      const client = new Client(customOptions);

      expect(client.baseURL).to.equal('https://custom.api.example.com');
      expect(client.authBaseURL).to.equal('https://custom-auth.example.com/');
      expect(client.retries).to.equal(5);
      expect(axios.create).to.have.been.calledWith({
        baseURL: 'https://custom.api.example.com',
        timeout: 60000,
        responseType: 'json',
      });
    });

    it('should create a Client with custom logger', () => {
      const customLogger = bunyan.createLogger({ name: 'custom-logger' });
      const customOptions = {
        ...validOptions,
        logger: customLogger,
      };

      const client = new Client(customOptions);

      expect(client.logger).to.equal(customLogger);
    });

    describe('getHeaders', () => {
      it('should return headers with Authorization when secure is true', () => {
        const client = new Client(validOptions);
        const mockToken: TokensResponse = { access_token: 'test-token-123' };
        client.token = mockToken;

        const headers = client.getHeaders(true);

        expect(headers).to.deep.equal({
          'User-Agent': 'Assembly Payments Node',
          Authorization: 'Bearer test-token-123',
        });
      });

      it('should return headers without Authorization when secure is false', () => {
        const client = new Client(validOptions);
        const mockToken: TokensResponse = { access_token: 'test-token-123' };
        client.token = mockToken;

        const headers = client.getHeaders(false);

        expect(headers).to.deep.equal({
          'User-Agent': 'Assembly Payments Node',
        });
      });

      it('should return headers with Authorization when secure is not provided (defaults to true)', () => {
        const client = new Client(validOptions);
        const mockToken: TokensResponse = { access_token: 'test-token-123' };
        client.token = mockToken;

        const headers = client.getHeaders();

        expect(headers).to.deep.equal({
          'User-Agent': 'Assembly Payments Node',
          Authorization: 'Bearer test-token-123',
        });
      });

      it('should handle undefined token gracefully', () => {
        const client = new Client(validOptions);
        client.token = undefined;

        const headers = client.getHeaders(true);

        expect(headers).to.deep.equal({
          'User-Agent': 'Assembly Payments Node',
          Authorization: 'Bearer undefined',
        });
      });
    });

    describe('conditionalRefresh', () => {
      it('should not refresh when secure is false', async () => {
        const client = new Client(validOptions);
        const refreshSpy = sandbox.stub(client, 'refresh');

        await client.conditionalRefresh(false);

        expect(refreshSpy).to.not.have.been.called;
      });

      it('should refresh when no token is present', async () => {
        const client = new Client(validOptions);
        const refreshSpy = sandbox.stub(client, 'refresh').resolves();
        client.token = undefined;

        await client.conditionalRefresh(true);

        expect(refreshSpy).to.have.been.calledOnce;
      });

      it('should refresh when no refreshAt is present', async () => {
        const client = new Client(validOptions);
        const refreshSpy = sandbox.stub(client, 'refresh').resolves();
        client.token = { access_token: 'test-token' };
        client.refreshAt = undefined;

        await client.conditionalRefresh(true);

        expect(refreshSpy).to.have.been.calledOnce;
      });

      it('should refresh when in refresh window', async () => {
        const client = new Client(validOptions);
        const refreshSpy = sandbox.stub(client, 'refresh').resolves();
        client.token = { access_token: 'test-token' };
        client.refreshAt = new Date(Date.now() - 1000); // 1 second ago

        await client.conditionalRefresh(true);

        expect(refreshSpy).to.have.been.calledOnce;
      });

      it('should not refresh when not in refresh window', async () => {
        const client = new Client(validOptions);
        const refreshSpy = sandbox.stub(client, 'refresh').resolves();
        client.token = { access_token: 'test-token' };
        client.refreshAt = new Date(Date.now() + 10000); // 10 seconds in the future

        await client.conditionalRefresh(true);

        expect(refreshSpy).to.not.have.been.called;
      });
    });

    describe('request', () => {
      it('should make a successful request', async () => {
        const mockResponse = { data: { result: 'success' } };
        mockAxiosInstance.request.resolves(mockResponse);

        const client = new Client(validOptions);
        const conditionalRefreshSpy = sandbox
          .stub(client, 'conditionalRefresh')
          .resolves();
        const getHeadersSpy = sandbox
          .stub(client, 'getHeaders')
          .returns({ 'User-Agent': 'test' });

        const result = await client.request({
          secure: true,
          method: 'GET',
          url: '/test',
        });

        expect(conditionalRefreshSpy).to.have.been.calledWith(true);
        expect(getHeadersSpy).to.have.been.calledWith(true);
        expect(result).to.deep.equal(mockResponse.data);
      });

      it('should handle API errors and log them', async () => {
        const mockError = {
          message: 'Request failed',
          response: {
            status: 400,
            statusText: 'Bad Request',
            data: { error: 'Invalid request' },
          },
        };
        mockAxiosInstance.request.rejects(mockError);

        const client = new Client(validOptions);
        client.logger = mockLogger;
        sandbox.stub(client, 'conditionalRefresh').resolves();
        sandbox.stub(client, 'getHeaders').returns({ 'User-Agent': 'test' });

        try {
          await client.request({ secure: true, method: 'GET', url: '/test' });

          expect.fail('Should have thrown an error');
        } catch (error) {
          expect(error).to.deep.equal({
            message: 'Request failed',
            response: {
              data: {
                error: 'Invalid request'
              },
              status: 400,
              statusText: 'Bad Request',
            }
          });

          expect(mockLogger.debug).to.have.been.called;
        }
      });

      it('should handle errors without response', async () => {
        const mockError = {
          message: 'Network error',
        };
        mockAxiosInstance.request.rejects(mockError);

        const client = new Client(validOptions);
        client.logger = mockLogger;

        sandbox.stub(client, 'conditionalRefresh').resolves();
        sandbox.stub(client, 'getHeaders').returns({ 'User-Agent': 'test' });

        try {
          await client.request({ secure: true, method: 'GET', url: '/test' });
          expect.fail('Should have thrown an error');
        } catch (error) {
          expect(error).to.deep.equal({
            message: 'Network error',
          });

          expect(mockLogger.debug).to.have.been.called;
        }
      });

      it('should log elapsed time for successful requests', async () => {
        const mockResponse = { data: { result: 'success' } };
        mockAxiosInstance.request.resolves(mockResponse);

        const client = new Client(validOptions);
        client.logger = mockLogger;
        sandbox.stub(client, 'conditionalRefresh').resolves();
        sandbox.stub(client, 'getHeaders').returns({ 'User-Agent': 'test' });

        await client.request({ secure: true, method: 'GET', url: '/test' });

        expect(mockLogger.debug).to.have.been.called;
      });
    });
  });

  describe('jitter', () => {
    it('should return a number between 1 and 5', () => {
      const results: number[] = [];
      for (let i = 0; i < 100; i += 1) {
        results.push(jitter());
      }

      for (const result of results) {
        expect(result).to.be.a('number');
        expect(result).to.be.at.least(1);
        expect(result).to.be.at.most(5);
        expect(Number.isInteger(result)).to.be.true;
      }
    });

    it('should return different values over multiple calls', () => {
      const results = new Set<number>();
      for (let i = 0; i < 50; i += 1) {
        results.add(jitter());
      }

      // With 50 calls, we should get some variation (not all the same value)
      expect(results.size).to.be.greaterThan(1);
    });
  });
});
