# Zai Package

A comprehensive Zai API SDK for Ordermentum that provides utilities for integrating with the Zai (AKA Assembly Pay or Promise Pay) payment platform. This SDK enables seamless payment processing, account management, and transaction handling through a unified TypeScript interface.

## 🚀 Quick Start

```typescript
import { createClient } from '@ordermentum/zai';

// Create a client instance
const zaiClient = createClient({
  clientId: 'your-client-id',
  clientSecret: 'your-client-secret',
  scope: 'your-scope',
  baseURL: 'https://secure.api.promisepay.com/',
  authBaseURL: 'https://au-0000.auth.assemblypay.com/',
  dataURL: 'https://secure.api.promisepay.com/',
});

// Create a user
const user = await zaiClient.users.create({
  id: 'user-123',
  first_name: '<PERSON>',
  last_name: 'Do<PERSON>',
  email: '<EMAIL>',
  country: 'AUS',
});

// Create an item for payment
const item = await zaiClient.items.create({
  id: 'item-456',
  name: 'Product Purchase',
  amount: 10000, // Amount in cents
  payment_type: 2, // Express payment
  buyer_id: 'buyer-123',
  seller_id: 'seller-456',
  currency: 'AUD',
});
```

## ⚙️ Configuration

Configure the SDK with your Zai credentials and endpoints:

| Parameter | Description | Required | Default |
|-----------|-------------|----------|---------|
| `clientId` | Client ID provided by Zai | Yes | - |
| `clientSecret` | Client secret provided by Zai | Yes | - |
| `scope` | OAuth scope for API access | Yes | - |
| `baseURL` | Base URL for API requests | No | `https://test.api.promisepay.com/` |
| `authBaseURL` | Authentication base URL | No | `https://au-0000.auth.assemblypay.com/` |
| `dataURL` | Data API URL | No | `https://secure.api.promisepay.com/` |
| `retries` | Number of retry attempts | No | `3` |
| `timeout` | Request timeout in milliseconds | No | `180000` |
| `logger` | Bunyan logger instance | No | Default logger |

### Environment Setup

For different environments:

```typescript
// Production
const client = createClient({
  clientId: process.env.ZAI_CLIENT_ID,
  clientSecret: process.env.ZAI_CLIENT_SECRET,
  scope: process.env.ZAI_SCOPE,
  baseURL: 'https://secure.api.promisepay.com/',
  authBaseURL: 'https://au-0000.auth.assemblypay.com/',
});

// Sandbox/Test
const client = createClient({
  clientId: process.env.ZAI_CLIENT_ID,
  clientSecret: process.env.ZAI_CLIENT_SECRET,
  scope: process.env.ZAI_SCOPE,
  baseURL: 'https://test.api.promisepay.com/',
  authBaseURL: 'https://au-0000.auth.assemblypay.com/',
});
```

## 🏗️ Architecture

### Client Structure

The SDK provides a comprehensive client with organized resource modules:

```typescript
const client = createClient(options);

// Available resources
client.accounts        // Account management
client.bankAccounts    // Bank account operations
client.cardAccounts    // Card account operations
client.walletAccounts  // Wallet account operations
client.users           // User management
client.items           // Item/transaction management
client.charges         // Charge operations
client.transactions    // Transaction history
client.fees            // Fee management
client.disbursements   // Disbursement operations
client.marketplace     // Marketplace settings
client.callbacks       // Webhook callbacks
client.companies       // Company management
client.tokens          // Token management
client.tools           // Utility tools
```

### Authentication & Token Management

The SDK handles OAuth2 authentication automatically:

- **Automatic Token Refresh**: Tokens are refreshed automatically before expiration
- **Jitter Implementation**: Includes randomized refresh timing to prevent thundering herd
- **Retry Logic**: Built-in retry mechanism with exponential backoff
- **Secure Token Storage**: Tokens are stored securely in memory

## 📖 Usage Examples

### User Management

```typescript
// Create a user
const user = await client.users.create({
  id: 'user-123',
  first_name: 'John',
  last_name: 'Doe',
  email: '<EMAIL>',
  mobile: '+***********',
  country: 'AUS',
  address_line1: '123 Main St',
  city: 'Melbourne',
  state: 'VIC',
  zip: '3000',
});

// Get user details
const userDetails = await client.users.show('user-123');

// Update user
const updatedUser = await client.users.update('user-123', {
  first_name: 'Jane',
  mobile: '+***********',
});
```

### Account Management

```typescript
// Create bank account
const bankAccount = await client.bankAccounts.create({
  user_id: 'user-123',
  bank_name: 'Commonwealth Bank',
  account_name: 'John Doe',
  routing_number: '062000',
  account_number: '*********',
  account_type: 'checking',
  holder_type: 'personal',
  country: 'AUS',
});

// Create card account
const cardAccount = await client.cardAccounts.create({
  user_id: 'user-123',
  full_name: 'John Doe',
  number: '****************',
  expiry_month: 12,
  expiry_year: 2025,
  cvv: '123',
});
```

### Payment Processing

```typescript
// Create an item for payment
const item = await client.items.create({
  id: 'item-456',
  name: 'Product Purchase',
  amount: 10000, // $100.00 in cents
  payment_type: 2, // Express payment
  buyer_id: 'buyer-123',
  seller_id: 'seller-456',
  currency: 'AUD',
  description: 'Monthly subscription payment',
});

// Make payment
const payment = await client.items.makePayment('item-456', {
  account_id: 'card-account-id',
  device_id: 'device-123',
  ip_address: '***********',
});

// Release payment to seller
const release = await client.items.releasePayment('item-456', {
  release_amount: 10000,
});
```

### Transaction Management

```typescript
// Get transaction history
const transactions = await client.transactions.listTransactions({
  limit: 50,
  offset: 0,
});

// Get specific transaction
const transaction = await client.transactions.show('transaction-id');

// Get item transactions
const itemTransactions = await client.items.listTransactions('item-456');
```

### Webhook Management

```typescript
// Create callback
const callback = await client.callbacks.create({
  url: 'https://your-app.com/webhooks/zai',
  object_type: 'items',
  enabled: 'true',
});

// List callbacks
const callbacks = await client.callbacks.list();

// Update callback
const updatedCallback = await client.callbacks.update('callback-id', {
  enabled: 'false',
});
```

### Fee Management

```typescript
// Create fee
const fee = await client.fees.create({
  name: 'Processing Fee',
  fee_type_id: '2', // Percentage
  amount: 250, // 2.5%
  to: 'seller',
});

// List fees
const fees = await client.fees.list();

// Calculate fee for amount
const calculatedFee = await client.fees.show('fee-id', {
  amount: 10000,
});
```

### Company Management

```typescript
// Create company
const company = await client.companies.create({
  name: 'ACME Corp',
  legal_name: 'ACME Corporation Pty Ltd',
  tax_number: '*********',
  country: 'AUS',
  user_id: 'user-123',
  address_line1: '456 Business St',
  city: 'Sydney',
  state: 'NSW',
  zip: '2000',
});
```

## 🔧 Features

### ✅ Core Features
- **OAuth2 Authentication** - Automatic token management and refresh
- **Comprehensive API Coverage** - Full Zai API functionality
- **TypeScript Support** - Complete type definitions for all endpoints
- **Automatic Retries** - Built-in retry logic with exponential backoff
- **Request Logging** - Structured logging with Bunyan
- **Error Handling** - Comprehensive error handling and reporting

### ✅ Payment Processing
- **Multiple Payment Methods** - Credit cards, bank accounts, wallets
- **Express Payments** - Fast payment processing
- **Partial Refunds** - Support for partial refund operations
- **Fee Management** - Flexible fee structure configuration
- **Transaction History** - Complete transaction tracking

### ✅ Account Management
- **Multi-Account Types** - Bank, card, wallet, and BPay accounts
- **Account Verification** - Built-in verification processes
- **User Management** - Complete user lifecycle management
- **Company Profiles** - Business account management

### ✅ Advanced Features
- **Webhook Integration** - Real-time callback notifications
- **Batch Processing** - Bulk transaction handling
- **Marketplace Management** - Multi-vendor platform support
- **Disbursement Control** - Flexible payout scheduling
- **Fraud Protection** - Built-in security measures

## 📊 Type Definitions

The SDK includes comprehensive TypeScript definitions for all API responses and requests:

```typescript
import { User, Item, Transaction, BankAccount, CardAccount } from '@ordermentum/zai';

// All types are fully typed for IntelliSense support
const user: User = await client.users.show('user-123');
const item: Item = await client.items.show('item-456');
const transaction: Transaction = await client.transactions.show('txn-789');
```

## 🛠️ Error Handling

The SDK provides structured error handling:

```typescript
try {
  const user = await client.users.create(userData);
} catch (error) {
  if (error.status === 400) {
    console.error('Validation error:', error.data);
  } else if (error.status === 401) {
    console.error('Authentication error:', error.message);
  } else {
    console.error('API error:', error.message);
  }
}
```

## 📝 Logging

The SDK uses Bunyan for structured logging:

```typescript
import bunyan from 'bunyan';

const logger = bunyan.createLogger({
  name: 'my-app-zai',
  level: 'info',
});

const client = createClient({
  clientId: 'your-client-id',
  clientSecret: 'your-client-secret',
  scope: 'your-scope',
  logger: logger,
});
```

## 🔐 Security

- **Token Security**: Automatic token refresh and secure storage
- **HTTPS Only**: All communications over HTTPS
- **Input Validation**: Request validation before API calls
- **Rate Limiting**: Built-in rate limiting protection
- **Audit Logging**: Complete request/response logging

## 📚 API Reference

For detailed API documentation, refer to the [Zai API Documentation](https://docs.assemblypayments.com/).

## 🤝 Support

For support and questions:
- **Email**: <EMAIL>
- **Documentation**: [Zai Developer Docs](https://developer.assemblypayments.com/)
- **API Status**: [Zai Status Page](https://status.assemblypayments.com/)

## 📄 License

This project is licensed under the Apache-2.0 License.
