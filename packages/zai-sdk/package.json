{"name": "@ordermentum/zai", "version": "0.4.0", "description": "Zai API SDK", "main": "build/index.js", "types": "build/index.d.ts", "files": ["build/*"], "author": "Ordermentum <<EMAIL>>", "license": "Apache-2", "scripts": {"lint": "yarn eslint 'src/**/*.{ts,js}'", "build:coverage": "nyc check-coverage --statements 70 --branches 60 --functions 60 --lines 70", "test": "NODE_ENV=test nyc npm run spec", "report": "./node_modules/.bin/nyc report --reporter=html", "spec": "mocha 'test/**/*.test.ts'", "spec:runner": "mocha", "build": "yarn clean && yarn tsc", "prepublish": "yarn run build && yarn spec", "clean": "rm -rf build", "reporter": "nyc --reporter=html yarn run test", "typecheck": "tsc --noEmit", "format": "eslint --fix '**/*.{ts,js}'"}, "dependencies": {"axios": "1.11.0", "bunyan": "1.8.15"}, "devDependencies": {"@babel/eslint-parser": "7.28.0", "@types/bunyan": "1.8.11", "@types/mocha": "8.2.3", "@types/node": "14.18.63", "chai": "4.5.0", "chai-as-promised": "^7.1.1", "cross-env": "7.0.3", "mocha": "9.2.2", "nyc": "15.1.0", "sinon": "10.0.1", "sinon-chai": "3.7.0", "ts-node": "10.9.2", "typescript": "5.1.3"}}