{"name": "@ordermentum/slingshot", "version": "1.0.3", "description": "View Models for ES6", "main": "lib/index.js", "files": ["lib/*"], "scripts": {"test": "cross-env NODE_ENV=test npm run spec", "lint": "eslint 'src/**/*.{ts,js}'", "typecheck": "tsc --noEmit", "clean": "rm -rf build", "build": "yarn clean && yarn tsc", "spec": "mocha -R spec -r ts-node/register test/*.*", "prepublish": "yarn run build"}, "repository": {"type": "git", "url": "git+https://github.com/ordermentum/slingshot.git"}, "keywords": ["view", "models", "views", "models", "decorators"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/ordermentum/slingshot/issues"}, "homepage": "https://github.com/ordermentum/slingshot#readme", "dependencies": {}, "devDependencies": {"cross-env": "7.0.3", "lodash.pick": "4.4.0", "@types/chai": "^4.3.3", "@types/mocha": "^9.1.1", "@types/node": "^18.7.15", "@types/sinon": "^10.0.13", "chai": "^3.5.0", "eslint": "^8.57.0", "istanbul": "^0.4.4", "mocha": "^2.5.3", "ts-node": "^10.9.1", "typescript": "^4.8.2"}}