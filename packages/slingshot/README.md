# Slingshot

ViewModel/Decorator layer for ES6.

## Example - Class Based (Recommended)

```javascript

View model: 

import { view } from '@ordermentum/slingshot';

class ProductView extends view.Base<{Type}> {
  get total() {
    return this.model.subtotal + this.model.tax;
  }

  toJSON() {
    name: this.model.name
    subtotal: this.model.subtotal,
    tax: this.model.tax,
    total: this.total
  }
}

export const ProductViewModel = view.decorate(ProductView);
export default ProductViewModel;

Usage: 
import ProductViewModel from './views/product_view_model';
or 
import {ProductViewModel} from './views/product_view_model';

const product = {name: 'Coffee', subtotal: 10, tax: 1};
const view = ProductViewModel.build(product);

console.log(view.subtotal);
console.log(view.tax);
console.log(view.total);

// Arrays and Iterators

const products = [
  {name: 'Sourdough', subtotal: 1500, tax: 150},
  {name: 'So<PERSON> and Linseed', subtotal: 1500, tax: 150}
];

const productViews = ProductViewModel.build(products);

productViews.forEach((product) => {
  console.log(`${product.name}: ${product.total}`);
});
```

## Example - Builder method based (Succinct but not recommended)
```javascript
export const ProductViewModel = ViewModelBuilder<{Type}>()(
  ['name', 'total', 'tax'],
  model => ({
    get total() {
      return model.subtotal + model.tax;
    }

    toJSON() {
      name: model.name
      subtotal: model.subtotal,
      tax: model.tax,
      total: this.total
    }
  })
);
```


## Example - Legacy class based (deprecated)
```javascript
import { ViewModel, build } from '@ordermentum/slingshot';

class ProductViewModel extends ViewModel {
  static delegates = ['name', 'subtotal', 'tax'];

  get total() {
    return this.model.subtotal + this.model.tax;
  }
}

const product = {name: 'Coffee', subtotal: 10, tax: 1};
const view = ProductViewModel.build(product);
// or
// const view = build(product, ProductViewModel);

console.log(view.subtotal);
console.log(view.tax);
console.log(view.total);

// Arrays and Iterators

const products = [
  {name: 'Sourdough', subtotal: 1500, tax: 150},
  {name: 'Soy and Linseed', subtotal: 1500, tax: 150}
];

const productViews = ProductViewModel.build(products);

productViews.forEach((product) => {
  console.log(`${product.name}: ${product.total}`);
});
```