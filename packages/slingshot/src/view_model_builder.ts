function builder<T, K extends keyof T, U = any>(
  model: T,
  delegates: K[],
  f: (t: T) => U
): Pick<T, K> & U {
  const result = <Pick<T, K>>{};

  for (const k of delegates) {
    result[k] = model[k];
  }

  const v = f(model);
  const r = { ...result, ...v };
  return r;
}

export default function foo<T>() {
  return function bar<K extends keyof T, U>(delegates: K[], f: (t: T) => U) {
    return {
      build(attrs: T | T[]): any {
        if (Array.isArray(attrs)) return this.buildMany(attrs);
        return this.buildOne(attrs);
      },
      buildOne(attrs: T) {
        return builder(attrs, delegates, f);
      },
      buildMany(attrs: T[]) {
        return attrs.map(t => builder(t, delegates, f));
      },
    };
  };
}
