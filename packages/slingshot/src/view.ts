/* eslint-disable no-redeclare */
import { ViewClass } from './types';

export class Base<I = any> {
  model: I;

  constructor(m: I) {
    this.model = m;
  }

  toJSON(): any {
    return this.model;
  }
}

interface GenericBuilder {
  <M = any, T = any>(View: ViewClass<M, T>, m: M): T;
  <M = any, T = any>(View: ViewClass<M, T>, m: M[]): Array<T>;
  <M = any, T = any>(View: ViewClass<M, T>, m: M | Array<M>): T | T[];
}

export const builder: GenericBuilder = <M = any, T = any>(
  View: ViewClass<M, T>,
  m: M | Array<M>
) => (Array.isArray(m) ? m.map(l => new View(l)) : new View(m));

export class WrapperClass<M = any, T = any> {
  View: ViewClass<M, T>;

  constructor(View: ViewClass<M, T>) {
    this.View = View;
  }

  builder(model: M | M[]) {
    return builder(this.View, model);
  }

  build(model: M | M[]) {
    if (Array.isArray(model)) {
      return this.buildMany(model);
    }
    return this.buildOne(model) as T;
  }

  buildOne(model: M): T {
    return new this.View(model);
  }

  buildMany(model: M[]): T[] {
    return model.map(l => new this.View(l));
  }
}

export function decorate<M = any, T = any>(View: ViewClass<M, T>) {
  function build(m: M): T;
  function build(m: M[]): T[];
  function build(m: M | M[]): T | T[];
  function build(m: M | M[]): T | T[] {
    return builder(View, m);
  }

  return {
    build,
  };
}

export const view = decorate;
