import ViewModelCollection from './collection';
import { ViewClass } from './types';

export function create(object: any, ViewModel: ViewClass): any {
  const view = new ViewModel(object);

  const delegates: Array<string> = ViewModel.delegates
    ? ViewModel.delegates
    : Object.keys(object);

  return new Proxy(object, {
    get(target, name) {
      if (name in view) {
        return view[name];
      }

      return delegates.findIndex(value => value === name) !== -1
        ? target[name]
        : undefined;
    },
  });
}

export function isIterator<T>(object: any): object is Iterable<T> {
  return Symbol.iterator in object;
}

export default function build<T = any>(
  models: T | T[] | Iterable<T>,
  ViewModel: ViewClass
): any | ViewModelCollection {
  if (Array.isArray(models)) {
    return new ViewModelCollection(new Set<T>(models), ViewModel);
  }

  if (isIterator<T>(models)) {
    // @ts-ignore
    return new ViewModelCollection(new Set<T>(...models), ViewModel);
  }

  return create(models, ViewModel);
}
