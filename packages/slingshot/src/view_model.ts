import build from './builder';

type MaybeGet = {
  get: (params: { plain: boolean }) => any;
} & any;

export class ViewModel<T = MaybeGet> {
  static delegates: string[] = [];

  model: T;

  static build(model: any): any {
    return build(model, this);
  }

  constructor(model: any) {
    this.model = model;
  }

  toPlain(): any {
    // @ts-ignore
    if (this?.model?.get) {
      // @ts-ignore
      return this.model.get({ plain: true });
    }

    return this.model as any;
  }

  toJSON() {
    throw new Error('Please define toJSON() method');
  }
}

export default ViewModel;
