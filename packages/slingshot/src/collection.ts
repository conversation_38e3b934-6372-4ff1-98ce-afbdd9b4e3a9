import { create } from './builder';
import { ViewClass } from './types';

export default class ViewModelCollection {
  entries: any[];

  ViewModel: any;

  constructor(entries: any[] | Set<any>, ViewModel: ViewClass) {
    if (Array.isArray(entries)) {
      this.entries = entries;
    } else {
      this.entries = Array.from(entries);
    }
    this.ViewModel = ViewModel;
  }

  [Symbol.iterator]() {
    const { ViewModel } = this;
    const iter = this.entries[Symbol.iterator]();

    const iterator = {
      next() {
        const entry = iter.next();

        if (entry.done) {
          return { done: true };
        }

        return {
          value: create(entry.value, ViewModel),
          done: false,
        };
      },
    };
    return iterator;
  }

  toArray(): any[] {
    return Array.from([...this.entries]).map(entry =>
      create(entry, this.ViewModel)
    );
  }

  toJSON(opts?: Object): Object {
    return this.toArray().map(entry => entry.toJSON(opts));
  }
}
