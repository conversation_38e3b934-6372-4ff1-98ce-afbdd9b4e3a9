import { expect } from 'chai';
import pick from 'lodash.pick';
import { ViewModelBuilder } from '../src/index';
import { view } from '../src';
import { timer } from './utils';

const viewModelFunction = (m) => ({ x: m.y * 2 });
const viewModelGetter = (m) => ({
  get x() {
    return m.y * 2;
  },
});

class ViewModelClass extends view.Base<{ y: number }> {
  get x() {
    return this.model.y * 2;
  }

  toJSON() {
    return { x: this.x };
  }
}
const ViewModel = view.decorate(ViewModelClass);

const View1 = ViewModelBuilder<any>()(['x'], viewModelFunction);
const View2 = ViewModelBuilder<any>()(['x'], viewModelGetter);

const model = {
  y: 2,
};

describe('simple viewModelBuilder', () => {
  it('property', () => {
    const time = timer('property');
    for (let i = 0; i < 1000; i++) {
      const v = View1.build(model);
      // @ts-ignore
      expect(v.x).to.equal(4);
      expect(JSON.stringify(v)).to.equal('{"x":4}');
    }
    time();
  });

  it('accessor', () => {
    const time = timer('accessor');
    for (let i = 0; i < 1000; i++) {
      const v = View2.build(model);
      // @ts-ignore
      expect(v.x).to.equal(4);
      expect(JSON.stringify(v)).to.equal('{"x":4}');
    }
    time();
  });

  it('class', () => {
    const time = timer('class');
    for (let i = 0; i < 1000; i++) {
      // @ts-ignore
      const v = ViewModel.build(model);
      expect(JSON.stringify(v)).to.equal('{"x":4}');
      expect(v.x).to.equal(4);
    }
    time();
  });

  it('raw class', () => {
    const time = timer('raw-class');
    for (let i = 0; i < 1000; i++) {
      const v = new ViewModelClass(model);
      expect(JSON.stringify(v)).to.equal('{"x":4}');
      expect(v.x).to.equal(4);
    }
    time();
  });

  it('lodash', () => {
    const time = timer('lodash');
    for (let i = 0; i < 1000; i++) {
      const v = pick(model, ['y']);
      expect(v.y).to.equal(2);
      expect(JSON.stringify(v)).to.equal('{"y":2}');
    }
    time();
  });

  it('array test', () => {
    const time = timer('array');
    const models = new Array(1000).fill(model);
    const views = ViewModel.build(models);
    const resp = views.map((v) => v.toJSON());
    JSON.stringify(resp);
    expect(resp.length).to.equal(1000);
    time();
  });
});
