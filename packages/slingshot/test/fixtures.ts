export default {
  id: 'FAE4DEEC-D138-46FA-BE41-75A8DC65685C',
  name: 'Order - #OMO3108',
  number: 'OMO3108',
  freight: null,
  totalCost: '1380.0000',
  totalFreight: '0.0000',
  subtotal: '1380.0000',
  totalGST: '138.0000',
  total: '1518.0000',
  totalDue: '1518.0000',
  properties: {
    demo: false,
    trial: false,
    process: 'Dispatched',
    xero_error: '',
    xero_failed: '2019-10-18T04:12:47.268Z',
    xero_uploaded: '2019-10-17T02:53:22.501Z',
    placedByRetailer: true,
  },
  discounts: [],
  placedOn: '2019-10-17T02:53:09.475Z',
  deliveryDate: '2019-10-24T13:00:00.000Z',
  reference: 'reegan',
  notes: null,
  type: 'manual',
  cancelledAt: null,
  completeAt: '2019-10-17T02:53:09.475Z',
  comment: '',
  supplierId: '54BB8536-02E0-4D29-BB9E-3B8A520B0CA1',
  retailerId: '8ec8e38d-cfaf-43df-98ee-6104a701090e',
  purchaserId: 'AAC29CB4-C30A-49DB-997B-122306CDA259',
  invoiceId: '8a91a841-fc00-4937-947b-0f53af313fc1',
  userId: '7269fc85-65ac-4d1d-9208-0119f1322ab9',
  createdAt: '2019-10-17T02:53:09.523Z',
  updatedById: 'e5c46758-0bf5-43d9-a09d-b40cbe830bfb',
  updatedAt: '2019-10-18T04:12:47.330Z',
  deletedAt: null,
  lockedAt: null,
  created_at: '2019-10-17T02:53:09.523Z',
  updated_at: '2019-10-18T04:12:47.330Z',
  deleted_at: null,
  supplier_id: '54BB8536-02E0-4D29-BB9E-3B8A520B0CA1',
  retailer_id: '8ec8e38d-cfaf-43df-98ee-6104a701090e',
  purchaser_id: 'AAC29CB4-C30A-49DB-997B-122306CDA259',
  invoice_id: '8a91a841-fc00-4937-947b-0f53af313fc1',
  user_id: '7269fc85-65ac-4d1d-9208-0119f1322ab9',
  updated_by_id: 'e5c46758-0bf5-43d9-a09d-b40cbe830bfb',
  retailer: {
    id: '8ec8e38d-cfaf-43df-98ee-6104a701090e',
    legalName: 'Dog Food',
    tradingName: 'Dog Food',
    slug: '',
    properties: {},
    dataAttributes: {
      venue: 'Cafe',
    },
    settings: {},
    phone: '**********',
    liquorLicense: '',
    abn: '',
    email: '<EMAIL>',
    addressId: 'a0abcda8-d94f-49e7-8cca-ac3748da039a',
    billingAddressId: null,
    paymentMethodId: null,
    userId: '68fcabfd-ae3e-4691-96ef-5e6baca4ff4e',
    createdAt: '2019-05-10T00:00:27.191Z',
    updatedAt: '2019-05-10T00:00:34.774Z',
    verifiedAt: '2019-05-10T19:00:11.676Z',
    deletedAt: null,
    updatedById: null,
    contactFirstName: '',
    contactLastName: '',
    contactEmail: '',
    replacementId: null,
    profilePercent: 30,
    created_at: '2019-05-10T00:00:27.191Z',
    updated_at: '2019-05-10T00:00:34.774Z',
    deleted_at: null,
    user_id: '68fcabfd-ae3e-4691-96ef-5e6baca4ff4e',
    updated_by_id: null,
    address_id: 'a0abcda8-d94f-49e7-8cca-ac3748da039a',
    billing_address_id: null,
    payment_method_id: null,
    address: {
      formatted:
        'Unit 7, 256-258 Musgrave Road  Coopers Plains QLD 4170 Australia',
      id: 'a0abcda8-d94f-49e7-8cca-ac3748da039a',
      name: '',
      number: '',
      type: 'delivery',
      description: '',
      street1: 'Unit 7, 256-258 Musgrave Road',
      street2: '',
      suburb: 'Coopers Plains',
      state: 'QLD',
      postcode: '4170',
      country: 'Australia',
      latitude: -27.5611916,
      coordinates: {
        type: 'Point',
        coordinates: [153.0350641, -27.5611916],
      },
      longitude: 153.0350641,
      updatedById: null,
      userId: null,
      addressableType: '',
      addressableId: '8ec8e38d-cfaf-43df-98ee-6104a701090e',
      createdAt: '2019-05-10T00:00:27.198Z',
      updatedAt: '2019-05-10T00:00:31.729Z',
      deletedAt: null,
      created_at: '2019-05-10T00:00:27.198Z',
      updated_at: '2019-05-10T00:00:31.729Z',
      deleted_at: null,
    },
  },
  supplier: {
    id: '54BB8536-02E0-4D29-BB9E-3B8A520B0CA1',
    name: 'RANDOM SUPPLIER',
    slug: 'random-supplier',
    tradingName: 'RANDOM SUPPLIER',
    dataAttributes: {
      tier: 'B',
      categories: ['Functional Snacks'],
      accountPlatform: 'XERO',
      primaryCategory: 'Other - Food',
      primaryProducts: 'Functional Snacks',
      promotedCategories: ['Functional Snacks'],
      blacklistPromotedCategories: ['Functional Snacks'],
    },
    accountSettings: {
      dayStart: '0',
      insights: {
        trending: '',
        mostOrdered: true,
      },
      monthStart: '1',
      surcharges: {
        amex: 0,
        visa: 0,
        direct: 0,
        enabled: false,
        mastercard: 0,
      },
      orderPrefix: '',
      sendInvoice: true,
      orderExports: {
        ordersCsv: true,
        ordersPickPdf: true,
        ordersInvoicePdf: true,
        ordersDeliveryPdf: true,
        ordersLineItemsCsv: true,
        ordersDeliverySummaryPdf: true,
        ordersDeliveryManifestPdf: true,
        ordersLineItemsDetailsCsv: false,
        ordersProductionReportPdf: true,
      },
      paymentTerms: {
        days: true,
        endOfWeek: true,
        endOfMonth: true,
      },
      invoicePrefix: '',
      invoiceStatus: 'draft',
      paymentMethod: {
        other: true,
        creditCard: true,
        directDebit: true,
      },
      invoiceSetting: {
        creation: true,
        delivery: false,
      },
      productExports: {
        pricesCsv: true,
        productsCsv: true,
      },
      salePercentage: '5',
      templatePrefix: 'OMF',
      connectionEmail: '<EMAIL>',
      includeWeekends: true,
      retailerExports: {
        retailersCsv: true,
        retailersUsersCsv: true,
      },
      supportHolidays: true,
      invoiceFrequency: {
        weekly: false,
        onPlace: true,
      },
      invoiceEmailDelay: '0',
      enableDeliveryDate: true,
      sendMarketplaceOrder: false,
      defaultSalesAccountCode: '200',
      includeOrderUpdatesInReports: true,
    },
    settings: {
      brand: {},
      intercom: false,
    },
    shortName: 'secret',
    active: true,
    phone: '**********',
    abn: '***********',
    email: '<EMAIL>',
    addressId: 'c1c1f5ea-4c39-4368-9edf-656aff72b328',
    createdAt: '2018-03-27T03:05:50.274Z',
    updatedAt: '2019-10-14T21:55:40.145Z',
    deletedAt: null,
    billingStart: '2018-04-02T14:00:00.000Z',
    billingEnd: null,
    userId: 'f1b0d678-4255-43a8-8f09-ec74d323b7fe',
    updatedById: 'f1b0d678-4255-43a8-8f09-ec74d323b7fe',
    paymentRecurrence: 'FREQ=DAILY;BYHOUR=16;BYMINUTE=00;TZID=Australia/Sydney',
    invoiceSetting: 'on_placement',
    timezone: 'Australia/Sydney',
    paymentMethodId: '6ca66f41-2d02-4857-8b97-3edba2c1afae',
    contactFirstName: '',
    contactLastName: '',
    contactEmail: '<EMAIL>',
    corporateEntity: '',
    created_at: '2018-03-27T03:05:50.274Z',
    updated_at: '2019-10-14T21:55:40.145Z',
    deleted_at: null,
    user_id: 'f1b0d678-4255-43a8-8f09-ec74d323b7fe',
    address_id: 'c1c1f5ea-4c39-4368-9edf-656aff72b328',
    address: {
      formatted: '123 Fake Street Sydney NSW 2000 Australia',
      id: 'c1c1f5ea-4c39-4368-9edf-656aff72b328',
      name: '',
      number: '',
      type: 'delivery',
      description: '',
      street1: '123 Fake Street',
      street2: '',
      suburb: 'Sydney',
      state: 'NSW',
      postcode: '2000',
      country: 'Australia',
      latitude: -37.0,
      coordinates: {
        type: 'Point',
        coordinates: [145.0, -37.0],
      },
      longitude: 145.0,
      updatedById: null,
      userId: null,
      addressableType: 'retailer',
      addressableId: '54BB8536-02E0-4D29-BB9E-3B8A520B0CA1',
      createdAt: '2018-03-27T03:05:50.265Z',
      updatedAt: '2019-10-14T21:55:52.685Z',
      deletedAt: null,
      created_at: '2018-03-27T03:05:50.265Z',
      updated_at: '2019-10-14T21:55:52.685Z',
      deleted_at: null,
    },
  },
  purchaser: {
    id: 'AAC29CB4-C30A-49DB-997B-122306CDA259',
    name: 'Dog Food',
    supplierId: '54BB8536-02E0-4D29-BB9E-3B8A520B0CA1',
    retailerId: '8ec8e38d-cfaf-43df-98ee-6104a701090e',
    userId: '68fcabfd-ae3e-4691-96ef-5e6baca4ff4e',
    activatedAt: '2019-05-10T00:00:28.449Z',
    disabledAt: null,
    archivedAt: null,
    stopCredit: false,
    minimumOrderValue: '0.0000',
    freightGroupId: '60f08b76-1c5d-41fa-bb50-953c5b88d395',
    priceGroupId: '3ebb9a62-632f-4388-9e94-e209addb8604',
    visibilityGroupId: '49f66fdb-3270-4843-afae-fc9f6f97e2d8',
    invoiceScheduleSetting: 'immediate',
    invoiceSchedule: '',
    invoiceFrequency: 'on_place',
    invoiceSetting: 'on_placement',
    paymentMethodId: null,
    defaultPaymentMethodType: 'card',
    paymentMethodTypes: ['card', 'direct'],
    paymentSchedule: 'immediate',
    paymentDelay: 0,
    notes: null,
    reference: '438',
    orderedAt: '2019-10-17T02:53:09.523Z',
    deliveryInstructions: null,
    customerSyncedAt: null,
    deletedAt: null,
    externalCustomerId: null,
    createdAt: '2019-05-10T00:00:28.464Z',
    updatedAt: '2019-10-17T02:53:18.211Z',
    updatedById: null,
    deletedById: null,
    replacementId: null,
    created_at: '2019-05-10T00:00:28.464Z',
    updated_at: '2019-10-17T02:53:18.211Z',
    deleted_at: null,
    visibility_group_id: '49f66fdb-3270-4843-afae-fc9f6f97e2d8',
    retailer_id: '8ec8e38d-cfaf-43df-98ee-6104a701090e',
    price_group_id: '3ebb9a62-632f-4388-9e94-e209addb8604',
    freight_group_id: '60f08b76-1c5d-41fa-bb50-953c5b88d395',
    supplier_id: '54BB8536-02E0-4D29-BB9E-3B8A520B0CA1',
    payment_method_id: '6a895dad-c2e7-45d7-bae8-75a7cb0480e1',
    user_id: '68fcabfd-ae3e-4691-96ef-5e6baca4ff4e',
    updated_by_id: null,
    deleted_by_id: null,
  },
  invoice: {
    locked: false,
    id: '8a91a841-fc00-4937-947b-0f53af313fc1',
    number: 'OMI3108',
    reference: 'PAYMENT 123',
    cancelledAt: null,
    payable: true,
    paidAt: '2019-10-17T02:53:17.957Z',
    processing: false,
    processingError: '',
    dueAt: '2019-10-17T02:53:09.533Z',
    chargeAt: '2019-10-17T02:53:09.533Z',
    paidSupplierAt: null,
    paymentTransactionId: null,
    settlementReference: null,
    sentLateNotificationAt: false,
    sentOverdueNotificationAt: false,
    paymentMethod: 'card',
    supplierId: '54BB8536-02E0-4D29-BB9E-3B8A520B0CA1',
    retailerId: '8ec8e38d-cfaf-43df-98ee-6104a701090e',
    purchaserId: 'AAC29CB4-C30A-49DB-997B-122306CDA259',
    userId: null,
    chargedByUserId: '7269fc85-65ac-4d1d-9208-0119f1322ab9',
    invoiceSentAt: '2019-10-17T02:53:22.877Z',
    invoiceSendAt: null,
    invoiceSyncedAt: null,
    externalId: '',
    syncError: '',
    paymentMethodId: null,
    date: '2019-10-17T02:53:09.533Z',
    createdAt: '2019-10-17T02:53:09.542Z',
    updatedAt: '2019-10-22T01:27:17.055Z',
    updatedById: '57108eb2-051f-4f42-aa99-0d4c09999537',
    deletedAt: null,
    properties: {
      xero_error: '',
      xero_failed: '2019-10-18T04:12:47.268Z',
      xero_uploaded: '2019-10-17T02:53:22.501Z',
    },
    discounts: [],
    submittedAt: null,
    totalCost: '1380.0000',
    totalFreight: '0.0000',
    subtotal: '1380.0000',
    totalGST: '138.0000',
    surcharge: '0.0000',
    total: '1518.0000',
    totalDue: '1518.0000',
    credit: '0.0000',
    creditNoteIds: [],
    created_at: '2019-10-17T02:53:09.542Z',
    updated_at: '2019-10-22T01:27:17.055Z',
    deleted_at: null,
    supplier_id: '54BB8536-02E0-4D29-BB9E-3B8A520B0CA1',
    retailer_id: '8ec8e38d-cfaf-43df-98ee-6104a701090e',
    purchaser_id: 'AAC29CB4-C30A-49DB-997B-122306CDA259',
    user_id: null,
    updated_by_id: '57108eb2-051f-4f42-aa99-0d4c09999537',
    charged_by_user_id: '7269fc85-65ac-4d1d-9208-0119f1322ab9',
  },
  user: {
    id: '7269fc85-65ac-4d1d-9208-0119f1322ab9',
    email: '<EMAIL>',
    dob: null,
    phone: '**********',
    firstName: 'Glen',
    lastName: 'King',
    createdAt: '2019-05-10T00:31:38.449Z',
    updatedAt: '2019-10-17T02:54:38.178Z',
    deletedAt: null,
    lastSeenAt: '2019-10-17T02:53:51.369Z',
    updatedById: null,
    userId: null,
    emails: {},
    passwordResetToken: null,
    passwordResetSentAt: null,
    verifiedAt: '2019-05-10T00:31:38.446Z',
    created_at: '2019-05-10T00:31:38.449Z',
    updated_at: '2019-10-17T02:54:38.178Z',
    deleted_at: null,
  },
  updatedBy: {
    id: 'e5c46758-0bf5-43d9-a09d-b40cbe830bfb',
    email: '<EMAIL>',
    dob: '1900-01-01T00:00:00.000Z',
    phone: '',
    firstName: 'Orders',
    lastName: 'secret',
    createdAt: '2018-10-23T00:20:31.992Z',
    updatedAt: '2019-10-23T06:00:02.591Z',
    deletedAt: null,
    lastSeenAt: '2019-10-23T05:59:17.271Z',
    updatedById: null,
    userId: null,
    emails: {},
    passwordResetToken: null,
    passwordResetSentAt: null,
    verifiedAt: '2019-02-07T21:53:59.425Z',
    created_at: '2018-10-23T00:20:31.992Z',
    updated_at: '2019-10-23T06:00:02.591Z',
    deleted_at: null,
  },
  lineItems: [
    {
      id: '68c21bae-e41e-4939-9c59-008afcd0c74f',
      sku: 'CHOC-40',
      tax: 48,
      name: 'Caramel Muffins 40x43g',
      price: 60,
      total: 528,
      number: 2,
      weight: null,
      product: {
        id: 'dac8b20c-af00-4a26-9741-c3fbf0eb2b60',
        sku: 'CHOC-40',
        uom: 'unit',
        cost: 0,
        name: 'Caramel Muffins 40x43g',
        sort: 0,
        unit: '',
        price: 80,
        weight: null,
        taxable: true,
        user_id: '68fcabfd-ae3e-4691-96ef-5e6baca4ff4e',
        visible: false,
        image_id: null,
        tax_type: 'excludes',
        lead_time: null,
        pack_size: 0,
        unit_size: 1,
        batch_code: '00000000',
        created_at: '2018-03-27T06:12:57.939+00:00',
        deleted_at: null,
        properties: {},
        sales_code: '200',
        updated_at: '2019-02-08T04:23:31.886+00:00',
        category_id: null,
        description: 'Caramel Muffins 40x43g',
        external_id: null,
        promotional: false,
        supplier_id: '54BB8536-02E0-4D29-BB9E-3B8A520B0CA1',
        legacy_image: {
          url: '',
          etag: '8c2e78e4f9d4869aa475ad65c3c53439',
          tags: [],
          type: 'upload',
          bytes: 120876,
          width: 350,
          format: 'jpg',
          height: 350,
          version: 1522302580,
          public_id: 'uwefpnplcvfaqzqouh2m',
          signature: '306e893a877db6a3901592ae20b67a0a9dc30efe',
          created_at: '2018-03-29T05:49:40Z',
          secure_url: '',
          placeholder: false,
          resource_type: 'image',
          original_filename: '330d82b2b4641407054c637fa658d0a9',
        },
        min_quantity: 1,
        out_of_stock: false,
        universal_id: '',
        deleted_by_id: null,
        delivery_days: [
          'monday',
          'tuesday',
          'wednesday',
          'thursday',
          'friday',
          'saturday',
          'sunday',
        ],
        material_code: '',
        random_weight: false,
        updated_by_id: '68fcabfd-ae3e-4691-96ef-5e6baca4ff4e',
        deactivated_at: null,
      },
      taxable: true,
      order_id: '56ad8bda-415e-48fc-86c8-a1539c1ae727',
      quantity: 8,
      subtotal: 480,
      batch_code: '00000000',
      created_at: '2019-10-17T02:53:09.527+00:00',
      deleted_at: null,
      product_id: 'dac8b20c-af00-4a26-9741-c3fbf0eb2b60',
      properties: {},
      updated_at: '2019-10-17T02:53:09.527+00:00',
      adjusted_weight: null,
      serialized_product: {
        id: 'dac8b20c-af00-4a26-9741-c3fbf0eb2b60',
        SKU: 'CHOC-40',
        uom: 'unit',
        cost: '0.0000',
        name: 'Caramel Muffins 40x43g',
        unit: '',
        addTax: true,
        userId: '68fcabfd-ae3e-4691-96ef-5e6baca4ff4e',
        weight: null,
        imageId: null,
        taxType: 'excludes',
        user_id: '68fcabfd-ae3e-4691-96ef-5e6baca4ff4e',
        image_id: null,
        leadTime: null,
        packSize: 0,
        unitSize: 1,
        basePrice: '80.0000',
        batchCode: '00000000',
        createdAt: '2018-03-27T06:12:57.939Z',
        deletedAt: null,
        salesCode: '200',
        sortOrder: 0,
        updatedAt: '2019-02-08T04:23:31.886Z',
        categoryId: null,
        created_at: '2018-03-27T06:12:57.939Z',
        deleted_at: null,
        externalId: null,
        outOfStock: false,
        properties: {},
        supplierId: '54BB8536-02E0-4D29-BB9E-3B8A520B0CA1',
        updated_at: '2019-02-08T04:23:31.886Z',
        category_id: null,
        deletedById: null,
        description: 'Caramel Muffins 40x43g',
        legacyImage: {
          url: '',
          etag: '8c2e78e4f9d4869aa475ad65c3c53439',
          tags: [],
          type: 'upload',
          bytes: 120876,
          width: 350,
          format: 'jpg',
          height: 350,
          version: 1522302580,
          public_id: 'uwefpnplcvfaqzqouh2m',
          signature: '306e893a877db6a3901592ae20b67a0a9dc30efe',
          created_at: '2018-03-29T05:49:40Z',
          secure_url: '',
          placeholder: false,
          resource_type: 'image',
          original_filename: '330d82b2b4641407054c637fa658d0a9',
        },
        minQuantity: 1,
        promotional: false,
        supplier_id: '54BB8536-02E0-4D29-BB9E-3B8A520B0CA1',
        universalId: '',
        updatedById: '68fcabfd-ae3e-4691-96ef-5e6baca4ff4e',
        deliveryDays: [
          'monday',
          'tuesday',
          'wednesday',
          'thursday',
          'friday',
          'saturday',
          'sunday',
        ],
        materialCode: '',
        randomWeight: false,
        deactivatedAt: null,
        deleted_by_id: null,
        updated_by_id: '68fcabfd-ae3e-4691-96ef-5e6baca4ff4e',
      },
    },
    {
      id: '3e22a9f5-c162-4d86-9bb6-2381c8f51c91',
      sku: 'COCO-40',
      tax: 42,
      name: 'Chocolate Muffins 40x43g',
      price: 60,
      total: 462,
      number: 1,
      weight: null,
      product: {
        id: '3e128650-371e-407b-859a-265ecf883db5',
        sku: 'COCO-40',
        uom: 'unit',
        cost: 0,
        name: 'Chocolate Muffins 40x43g',
        sort: 0,
        unit: '',
        price: 80,
        weight: null,
        taxable: true,
        user_id: '68fcabfd-ae3e-4691-96ef-5e6baca4ff4e',
        visible: false,
        image_id: null,
        tax_type: 'excludes',
        lead_time: null,
        pack_size: 0,
        unit_size: 1,
        batch_code: '00000000',
        created_at: '2018-03-27T06:12:51.206+00:00',
        deleted_at: null,
        properties: {},
        sales_code: '200',
        updated_at: '2019-02-08T04:23:31.886+00:00',
        category_id: null,
        description: 'Chocolate Muffins 40x43g',
        external_id: null,
        promotional: false,
        supplier_id: '54BB8536-02E0-4D29-BB9E-3B8A520B0CA1',
        legacy_image: {
          url: '',
          etag: '985582674624cf67bbdb34659be50abb',
          tags: [],
          type: 'upload',
          bytes: 110473,
          width: 350,
          format: 'jpg',
          height: 350,
          version: 1522302593,
          public_id: 'dqr8cp37kerubedzjdr9',
          signature: 'baf57aa947e8f3c132eeeed2da5a949712f93fc4',
          created_at: '2018-03-29T05:49:53Z',
          secure_url: '',
          placeholder: false,
          resource_type: 'image',
          original_filename: '9100248204988b6a8cec893b7a8d973f',
        },
        min_quantity: 1,
        out_of_stock: false,
        universal_id: '',
        deleted_by_id: null,
        delivery_days: [
          'monday',
          'tuesday',
          'wednesday',
          'thursday',
          'friday',
          'saturday',
          'sunday',
        ],
        material_code: '',
        random_weight: false,
        updated_by_id: '68fcabfd-ae3e-4691-96ef-5e6baca4ff4e',
        deactivated_at: null,
      },
      taxable: true,
      order_id: '56ad8bda-415e-48fc-86c8-a1539c1ae727',
      quantity: 7,
      subtotal: 420,
      batch_code: '00000000',
      created_at: '2019-10-17T02:53:09.527+00:00',
      deleted_at: null,
      product_id: '3e128650-371e-407b-859a-265ecf883db5',
      properties: {},
      updated_at: '2019-10-17T02:53:09.527+00:00',
      adjusted_weight: null,
      serialized_product: {
        id: '3e128650-371e-407b-859a-265ecf883db5',
        SKU: 'COCO-40',
        uom: 'unit',
        cost: '0.0000',
        name: 'Chocolate Muffins 40x43g',
        unit: '',
        addTax: true,
        userId: '68fcabfd-ae3e-4691-96ef-5e6baca4ff4e',
        weight: null,
        imageId: null,
        taxType: 'excludes',
        user_id: '68fcabfd-ae3e-4691-96ef-5e6baca4ff4e',
        image_id: null,
        leadTime: null,
        packSize: 0,
        unitSize: 1,
        basePrice: '80.0000',
        batchCode: '00000000',
        createdAt: '2018-03-27T06:12:51.206Z',
        deletedAt: null,
        salesCode: '200',
        sortOrder: 0,
        updatedAt: '2019-02-08T04:23:31.886Z',
        categoryId: null,
        created_at: '2018-03-27T06:12:51.206Z',
        deleted_at: null,
        externalId: null,
        outOfStock: false,
        properties: {},
        supplierId: '54BB8536-02E0-4D29-BB9E-3B8A520B0CA1',
        updated_at: '2019-02-08T04:23:31.886Z',
        category_id: null,
        deletedById: null,
        description: 'Chocolate Muffins 40x43g',
        legacyImage: {
          url: '',
          etag: '985582674624cf67bbdb34659be50abb',
          tags: [],
          type: 'upload',
          bytes: 110473,
          width: 350,
          format: 'jpg',
          height: 350,
          version: 1522302593,
          public_id: 'dqr8cp37kerubedzjdr9',
          signature: 'baf57aa947e8f3c132eeeed2da5a949712f93fc4',
          created_at: '2018-03-29T05:49:53Z',
          secure_url: '',
          placeholder: false,
          resource_type: 'image',
          original_filename: '9100248204988b6a8cec893b7a8d973f',
        },
        minQuantity: 1,
        promotional: false,
        supplier_id: '54BB8536-02E0-4D29-BB9E-3B8A520B0CA1',
        universalId: '',
        updatedById: '68fcabfd-ae3e-4691-96ef-5e6baca4ff4e',
        deliveryDays: [
          'monday',
          'tuesday',
          'wednesday',
          'thursday',
          'friday',
          'saturday',
          'sunday',
        ],
        materialCode: '',
        randomWeight: false,
        deactivatedAt: null,
        deleted_by_id: null,
        updated_by_id: '68fcabfd-ae3e-4691-96ef-5e6baca4ff4e',
      },
    },
    {
      id: '3abf03c4-2ca9-4393-988c-f0b7ea9894e7',
      sku: 'CARA-40',
      tax: 48,
      name: 'Vanilla Muffins 40x43g',
      price: 60,
      total: 528,
      number: 3,
      weight: null,
      product: {
        id: '30dcfe7c-6ebc-4d20-a634-3d9c141f0481',
        sku: 'CARA-40',
        uom: 'unit',
        cost: 0,
        name: 'Vanilla Muffins 40x43g',
        sort: 0,
        unit: '',
        price: 80,
        weight: null,
        taxable: true,
        user_id: '68fcabfd-ae3e-4691-96ef-5e6baca4ff4e',
        visible: false,
        image_id: null,
        tax_type: 'excludes',
        lead_time: null,
        pack_size: 0,
        unit_size: 1,
        batch_code: '00000000',
        created_at: '2018-03-27T06:12:57.03+00:00',
        deleted_at: null,
        properties: {},
        sales_code: '200',
        updated_at: '2019-02-08T04:23:31.886+00:00',
        category_id: null,
        description: 'Vanilla Muffins 40x43g',
        external_id: null,
        promotional: false,
        supplier_id: '54BB8536-02E0-4D29-BB9E-3B8A520B0CA1',
        legacy_image: {
          url: '',
          etag: '4afba598f99d3afba9ce9a88fa1ff7cd',
          tags: [],
          type: 'upload',
          bytes: 119481,
          width: 350,
          format: 'jpg',
          height: 350,
          version: 1522302553,
          public_id: 'xksuixofu1britztqgzu',
          signature: '587e36cccdf0ddbb93549f2f5858d82efc686b4f',
          created_at: '2018-03-29T05:49:13Z',
          secure_url: '',
          placeholder: false,
          resource_type: 'image',
          original_filename: '4e479f4ae30aa63a65c76020d785d432',
        },
        min_quantity: 1,
        out_of_stock: false,
        universal_id: '',
        deleted_by_id: null,
        delivery_days: [
          'monday',
          'tuesday',
          'wednesday',
          'thursday',
          'friday',
          'saturday',
          'sunday',
        ],
        material_code: '',
        random_weight: false,
        updated_by_id: '68fcabfd-ae3e-4691-96ef-5e6baca4ff4e',
        deactivated_at: null,
      },
      taxable: true,
      order_id: '56ad8bda-415e-48fc-86c8-a1539c1ae727',
      quantity: 8,
      subtotal: 480,
      batch_code: '00000000',
      created_at: '2019-10-17T02:53:09.527+00:00',
      deleted_at: null,
      product_id: '30dcfe7c-6ebc-4d20-a634-3d9c141f0481',
      properties: {},
      updated_at: '2019-10-17T02:53:09.527+00:00',
      adjusted_weight: null,
      serialized_product: {
        id: '30dcfe7c-6ebc-4d20-a634-3d9c141f0481',
        SKU: 'CARA-40',
        uom: 'unit',
        cost: '0.0000',
        name: 'Vanilla Muffins 40x43g',
        unit: '',
        addTax: true,
        userId: '68fcabfd-ae3e-4691-96ef-5e6baca4ff4e',
        weight: null,
        imageId: null,
        taxType: 'excludes',
        user_id: '68fcabfd-ae3e-4691-96ef-5e6baca4ff4e',
        image_id: null,
        leadTime: null,
        packSize: 0,
        unitSize: 1,
        basePrice: '80.0000',
        batchCode: '00000000',
        createdAt: '2018-03-27T06:12:57.030Z',
        deletedAt: null,
        salesCode: '200',
        sortOrder: 0,
        updatedAt: '2019-02-08T04:23:31.886Z',
        categoryId: null,
        created_at: '2018-03-27T06:12:57.030Z',
        deleted_at: null,
        externalId: null,
        outOfStock: false,
        properties: {},
        supplierId: '54BB8536-02E0-4D29-BB9E-3B8A520B0CA1',
        updated_at: '2019-02-08T04:23:31.886Z',
        category_id: null,
        deletedById: null,
        description: 'Vanilla Muffins 40x43g',
        legacyImage: {
          url: '',
          etag: '4afba598f99d3afba9ce9a88fa1ff7cd',
          tags: [],
          type: 'upload',
          bytes: 119481,
          width: 350,
          format: 'jpg',
          height: 350,
          version: 1522302553,
          public_id: 'xksuixofu1britztqgzu',
          signature: '587e36cccdf0ddbb93549f2f5858d82efc686b4f',
          created_at: '2018-03-29T05:49:13Z',
          secure_url: '',
          placeholder: false,
          resource_type: 'image',
          original_filename: '4e479f4ae30aa63a65c76020d785d432',
        },
        minQuantity: 1,
        promotional: false,
        supplier_id: '54BB8536-02E0-4D29-BB9E-3B8A520B0CA1',
        universalId: '',
        updatedById: '68fcabfd-ae3e-4691-96ef-5e6baca4ff4e',
        deliveryDays: [
          'monday',
          'tuesday',
          'wednesday',
          'thursday',
          'friday',
          'saturday',
          'sunday',
        ],
        materialCode: '',
        randomWeight: false,
        deactivatedAt: null,
        deleted_by_id: null,
        updated_by_id: '68fcabfd-ae3e-4691-96ef-5e6baca4ff4e',
      },
    },
  ],
  orderNumber: 'OMO3108',
  retailerName: 'Dog Food',
  lineCount: 3,
  productCount: 23,
  tags: [
    {
      key: 'demo',
      value: false,
    },
    {
      key: 'trial',
      value: false,
    },
    {
      key: 'process',
      value: 'Dispatched',
    },
    {
      key: 'xero_error',
      value: '',
    },
    {
      key: 'xero_failed',
      value: '2019-10-18T04:12:47.268Z',
    },
    {
      key: 'xero_uploaded',
      value: '2019-10-17T02:53:22.501Z',
    },
    {
      key: 'placedByRetailer',
      value: true,
    },
  ],
  paymentMethod: 'card',
  paymentMethodType: 'card',
  defaultPaymentMethod: 'card',
  invoiceNumber: 'OMI3108',
  dueAt: '2019-10-17T02:53:09.533Z',
  chargeAt: '2019-10-17T02:53:09.533Z',
  invoiceReference: '3565: Ordermentum',
  paidAt: '2019-10-17T02:53:17.957Z',
  submittedAt: null,
};
