import { expect } from 'chai';
import pick from 'lodash.pick';
import { ViewModelBuilder } from '../src/index';
import orderFixture from './fixtures';
import { view } from '../src';
import { timer } from './utils';

const viewModelFunction = (m) => ({
  isPaid: !!m.paidAt,
  toJSON() {
    return { isPaid: this.isPaid, invoiceNumber: m.invoiceNumber };
  },
});
const viewModelGetter = (m) => ({
  get isPaid() {
    return !!m.paidAt;
  },
  toJSON() {
    return { isPaid: this.isPaid, invoiceNumber: m.invoiceNumber };
  },
});

class ViewModelClass extends view.Base<typeof orderFixture> {
  get isPaid() {
    return !!this.model.paidAt;
  }

  toJSON() {
    return { isPaid: this.isPaid, invoiceNumber: this.model.invoiceNumber };
  }
}
const ViewModel = view.decorate<typeof orderFixture, ViewModelClass>(
  ViewModelClass
);

const View1 = ViewModelBuilder<any>()(['number'], viewModelFunction);
const View2 = ViewModelBuilder<any>()(['number'], viewModelGetter);

describe('complex viewModelBuilder', () => {
  it('property', () => {
    const time = timer('property');
    for (let i = 0; i < 1000; i++) {
      const v = View1.build(orderFixture);
      // @ts-ignore
      expect(v.isPaid).to.equal(true);
      expect(JSON.stringify(v)).to.equal(
        '{"isPaid":true,"invoiceNumber":"OMI3108"}'
      );
    }
    time();
  });

  it('accessor', () => {
    const time = timer('accessor');
    for (let i = 0; i < 1000; i++) {
      const v = View2.build(orderFixture);
      // @ts-ignore
      expect(v.isPaid).to.equal(true);
      expect(JSON.stringify(v)).to.equal(
        '{"isPaid":true,"invoiceNumber":"OMI3108"}'
      );
    }
    time();
  });

  it('raw class', () => {
    const time = timer('raw-class');
    for (let i = 0; i < 1000; i++) {
      const c = new ViewModelClass(orderFixture);
      expect(c.isPaid).to.equal(true);
      expect(JSON.stringify(c)).to.equal(
        '{"isPaid":true,"invoiceNumber":"OMI3108"}'
      );
    }
    time();
  });

  it('class', () => {
    const time = timer('class');
    for (let i = 0; i < 1000; i++) {
      const v = ViewModel.build(orderFixture);
      expect(JSON.stringify(v)).to.equal(
        '{"isPaid":true,"invoiceNumber":"OMI3108"}'
      );
      expect(v.isPaid).to.equal(true);
    }
    time();
  });

  it('method builder', () => {
    const time = timer('class');
    for (let i = 0; i < 1000; i++) {
      const v = view.builder(ViewModelClass, orderFixture);
      expect(JSON.stringify(v)).to.equal(
        '{"isPaid":true,"invoiceNumber":"OMI3108"}'
      );
      expect(v.isPaid).to.equal(true);
    }
    time();
  });

  it('lodash', () => {
    const time = timer('lodash');
    for (let i = 0; i < 1000; i++) {
      const v = pick(orderFixture, ['invoiceNumber', 'orderNumber']);
      expect(v.invoiceNumber).to.equal('OMI3108');
      expect(JSON.stringify(v)).to.equal(
        '{"invoiceNumber":"OMI3108","orderNumber":"OMO3108"}'
      );
    }
    time();
  });

  it('array test', () => {
    const time = timer('array');
    const models = new Array(1000).fill(orderFixture);
    const views = ViewModel.build(models);
    const resp = views.map((v) => v.toJSON());
    JSON.stringify(resp);
    expect(resp.length).to.equal(1000);
    time();
  });
});
