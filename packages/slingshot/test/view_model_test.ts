import { expect } from 'chai';
import { build, ViewModel } from '../src/index';

class ProductViewModel extends ViewModel<any> {
  static delegates = ['subtotal', 'tax', 'id'];

  get total() {
    return this.model.subtotal + this.model.tax;
  }

  toJSON() {
    // @ts-ignore
    return { id: this.id };
  }
}

describe('ViewModel', () => {
  it('delegates fields', () => {
    const product = { id: '123', subtotal: 10, tax: 1 };
    const view = build(product, ProductViewModel);

    expect(ProductViewModel.delegates).to.not.be.equal([]);
    expect(view.tax).to.equal(product.tax);
    expect(view.subtotal).to.equal(product.subtotal);
    expect(view.toJSON()).to.deep.equal({ id: '123' });
    expect(view.total).to.equal(product.tax + product.subtotal);
  });

  it('builds', () => {
    const product = { id: '123', subtotal: 10, tax: 1 };
    const view = ProductViewModel.build(product);
    expect(view.tax).to.equal(product.tax);
  });

  it('throws', () => {
    const view = new ViewModel(null);
    expect(view.toJSON).to.throw(Error);
  });

  it('plain', () => {
    const object = { name: 'test' };
    const view = new ViewModel(object);
    expect(view.toPlain()).to.deep.equal(object);
  });

  it('plain (sequelize)', () => {
    const plain = { name: 'test' };
    const object = {
      get() {
        return plain;
      },
    };
    const view = new ViewModel(object);
    expect(view.toPlain()).to.deep.equal(plain);
  });

  it('allows fields to be overidden', () => {
    const category = {
      id: '234',
      name: 'test',

      toJSON() {
        return { id: this.id };
      },
    };

    class CategoryViewModel extends ViewModel {
      toJSON() {
        return this.model.toJSON();
      }
    }

    const view = build(category, CategoryViewModel);
    expect(view.toJSON()).to.deep.equal({ id: '234' });
  });
});
