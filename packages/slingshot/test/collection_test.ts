import { expect } from 'chai';
import { build, ViewModel } from '../src/index';

describe('Collection', () => {
  it('Accepts an Array', () => {
    const products = [
      {
        name: 'Sourdough',
        toJSON() {
          return { name: this.name };
        },
        subtotal: 1500,
        tax: 150,
      },
      {
        name: 'Soy and Linseed',
        toJSON() {
          return { name: this.name };
        },
        subtotal: 1500,
        tax: 150,
      },
    ];
    class ProductViewModel extends ViewModel {
      static delegates = ['name'];

      toJSON() {
        return {
          // @ts-ignore
          name: this.name,
        };
      }
    }
    const productViews = build(products, ProductViewModel);
    expect(productViews.toArray()).to.be.instanceof(Array);
    expect(productViews.toArray()[0].name).to.equal(products[0].name);
    expect(productViews.toJSON()).to.deep.equal([
      { name: 'Sourdough' },
      { name: 'Soy and Linseed' },
    ]);
  });

  it('returns an interator', () => {
    class ProductViewModel extends ViewModel {
      static delegates = ['name'];

      toJSON() {
        return {
          // @ts-ignore
          name: this.name,
        };
      }
    }

    const product = {
      name: 'Sourdough',
      toJSON() {
        return { name: this.name };
      },
      subtotal: 1500,
      tax: 150,
    };
    const views = ProductViewModel.build([product]);

    for (const view of views) {
      expect(view.name).to.not.equal(null);
    }
  });

  it.skip('accepts an interator', () => {
    class ProductViewModel extends ViewModel {
      static delegates = ['name'];

      toJSON() {
        return {
          // @ts-ignore
          name: this.name,
        };
      }
    }

    const product = {
      name: 'Sourdough',
      toJSON() {
        return { name: this.name };
      },
      subtotal: 1500,
      tax: 150,
    };
    const products = new Set([product]);
    const views = ProductViewModel.build(products);

    for (const view of views) {
      expect(view.name).to.not.equal(null);
    }
  });
});
