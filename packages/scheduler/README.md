# @ordermentum/scheduler

A unified scheduler library that works with both Sequelize and Prisma ORMs.

## Features

- Schedule and run jobs based on recurrence rules (iCal/RRULE format)
- Works with both Sequelize and Prisma via adapters
- Computes next run times for scheduled jobs
- Maintenance system to recover from failed or stuck jobs
- <PERSON>les timezone-aware scheduling
- Graceful termination

## Installation

```bash
# For Prisma users
npm install @ordermentum/scheduler @prisma/client

# For Sequelize users
npm install @ordermentum/scheduler sequelize
```

## Usage

### With Prisma

```typescript
import { PrismaClient } from '@prisma/client';
import { createLogger } from 'bunyan';
import { JobScheduler, PrismaAdapter } from '@ordermentum/scheduler';
import moment from 'moment-timezone';

// Create a Prisma client
const prismaClient = new PrismaClient();

// Create a logger (e.g., using bunyan, or any logger compatible with GenericLogger)
const logger = createLogger({ name: 'scheduler' });

// Define your tasks
const tasks = {
  'daily-report': async (data, context) => {
    console.log('Running daily report', data);
    // Your task implementation
    return Promise.resolve();
  },
  // Queue based task
  'weekly-cleanup': async (data, context) => {
    console.log('Running weekly cleanup', data);
    // Your task implementation
    return weeklyCleanupTask.publish(data);
  }
};

// Create a Prisma adapter
const adapter = new PrismaAdapter(prismaClient);

// Create and start the scheduler
const scheduler = new JobScheduler({
  logger,
  adapter,
  tasks,
  jobsSafeToRestart: ['daily-report'],
  jobsRiskyToRestart: ['weekly-cleanup'],
  jobsCustomRestart: {
    'monthly-task': moment.duration(30, 'minutes')
  },
  maxConcurrentJobs: 10,
  lagInMinutes: 10
});

// Start the scheduler
scheduler.runScheduledJobs();

// Graceful shutdown handler
process.on('SIGTERM', async () => {
  console.log('Shutting down scheduler...');
  await scheduler.terminate();
  process.exit(0);
});
```

### With Sequelize

```typescript
import { createLogger } from 'bunyan';
import { JobScheduler, SequelizeAdapter } from '@ordermentum/scheduler';
import moment from 'moment-timezone';

// Create a logger (e.g., using bunyan, or any logger compatible with GenericLogger)
const logger = createLogger({ name: 'scheduler' });

// Create a Sequelize adapter
const adapter = new SequelizeAdapter('postgres://user:password@localhost:5432/dbname');

// Define your tasks
const tasks = {
  'daily-report': async (data, context) => {
    console.log('Running daily report', data);
    // Your task implementation
    return Promise.resolve();
  },
  // Queue based task
  'weekly-cleanup': async (data, context) => {
    console.log('Running weekly cleanup', data);
    // Your task implementation
    return weeklyCleanupTask.publish(data);
  }
};

// Create and start the scheduler
const scheduler = new JobScheduler({
  logger,
  adapter,
  tasks,
  jobsSafeToRestart: ['daily-report'],
  jobsRiskyToRestart: ['weekly-cleanup'],
  jobsCustomRestart: {
    'monthly-task': moment.duration(30, 'minutes')
  },
  maxConcurrentJobs: 10,
  lagInMinutes: 10
});

// Start the scheduler
scheduler.runScheduledJobs();

// Graceful shutdown handler
process.on('SIGTERM', async () => {
  console.log('Shutting down scheduler...');
  await scheduler.terminate();
  process.exit(0);
});
```

## How JobScheduler Works

The `JobScheduler` class (defined in `src/scheduler.ts`) is the core engine responsible for running background tasks based on a schedule stored in a database via a `DatabaseAdapter`.

**Key Responsibilities:**

1.  **Core Loop:**
    *   Runs on a configurable interval (`defaultRunInterval`).
    *   Periodically queries the `DatabaseAdapter` (`fetchAndEnqueueJobs`) to find and atomically lock jobs that are due.
    *   Processes fetched jobs using `runJobs`.

2.  **Job Execution (`executeJob`):**
    *   Maps a job name to the corresponding task function provided in the `tasks` configuration.
    *   Notifies the adapter when a job starts (`updateStartTask`).
    *   Executes the task function with the job's data.
    *   Notifies the adapter upon success (`updateFinishTask`) or failure (`updateFailure`). The adapter handles retry logic based on configuration (`backOffMs`, `maxRestartsOnFailure`, restart policies).

3.  **Concurrency Control:**
    *   Limits the number of simultaneously running jobs using `maxConcurrentJobs`.
    *   Tracks active jobs (`activeJobCount`) and prevents fetching new jobs if capacity is reached.

    **Concurrency Flow Diagram:**

    ```mermaid
    sequenceDiagram
        participant SL as SchedulerLoop
        participant JF as JobFetcher
        participant CM as ConcurrencyManager
        participant JE as JobExecutor
        participant DB as Database

        Note over SL,DB: Scheduler Cycle Start
        
        SL->>CM: canRunMore()
        alt Can run more jobs
            SL->>JF: fetchAndEnqueue()
            JF->>DB: Query for due jobs (limit by maxConcurrentJobs)
            DB-->>JF: Return locked jobs
            JF-->>SL: Return JobSets
            
            loop For each job in JobSets
                SL->>CM: canRunMore()
                alt Still has capacity
                    SL->>JE: execute(job)
                    SL->>CM: startJob(jobId, jobPromise)
                    Note right of CM: Increment activeJobCount
                    Note right of CM: Track job in runningJobs Map
                    
                    JE->>DB: Update job status to "running"
                    JE-->>SL: Job execution started (async)
                    
                    Note over JE: Job executes asynchronously
                    JE->>DB: Update job completion status
                    JE-->>CM: endJob(jobId) via Promise.finally()
                    Note right of CM: Decrement activeJobCount
                    Note right of CM: Remove from runningJobs Map
                else At capacity
                    SL->>JE: defer(remainingJobs)
                    JE->>DB: Reset remaining jobs for next cycle
                    Note over SL: Break loop, wait for next cycle
                end
            end
        else At max capacity
            Note over SL: Skip fetch, wait for next cycle
        end
        
        Note over SL,DB: Scheduler Cycle End
        
        Note over SL,DB: Graceful Shutdown
        SL->>CM: waitForRunningJobs(timeoutMs)
        CM-->>SL: Wait for all tracked jobs (with timeout)
    ```

4.  **Lifecycle Management:**
    *   **Initialization (`init`):** Sets up the scheduler and starts the associated `Maintenance` process.
    *   **Pausing/Resuming (`pause`, `resume`):** Allows temporarily stopping the processing of *new* jobs.
    *   **Termination (`terminate`):** Provides graceful shutdown, stopping timers, waiting for running jobs (with a timeout), stopping maintenance, and disconnecting the adapter.

5.  **Health & Monitoring:**
    *   **Heartbeat (`beat`, `healthCheck`):** Tracks activity via a timestamp. `healthCheck` verifies if the scheduler is responsive within `healthCheckTimeout`.
    *   **Metrics:** Optionally records detailed operational metrics (durations, counts, errors, etc.) via a `MetricsClient`.
    *   **Logging:** Uses the provided `GenericLogger` for detailed operational logs.

6.  **Error Handling:**
    *   Uses custom error classes (`SchedulerError`, `JobExecutionError`) for better context.
    *   Catches errors during execution and scheduling cycles, logs them, and attempts appropriate status updates via the adapter.

**Interaction with Other Components:**

*   **`DatabaseAdapter`:** The scheduler is stateless regarding job details; it relies entirely on the adapter for fetching, locking, and updating job statuses in the database.
*   **`Maintenance` Process:** The scheduler initializes and stops a separate `Maintenance` instance. This instance runs independently to find and handle jobs that might be stuck (e.g., due to a crash) or require retrying based on configured policies (`jobsSafeToRestart`, `lagInMinutes`, etc.). The scheduler focuses on running *newly* due jobs, while maintenance handles recovery and cleanup.

## Working with Recurrence Rules

This library uses the rrule-rust library for handling recurrence rules in iCal format:

```typescript
import { computeNextRun, computeNextRuns } from '@ordermentum/scheduler';

// Compute the next run time for a daily task
const nextRun = computeNextRun('FREQ=DAILY;INTERVAL=1;BYHOUR=8;BYMINUTE=0', {
  timezone: 'America/New_York'
});
console.log(nextRun); // Next 8:00 AM in New York time as ISO string

// Compute the next 5 run times for a monthly task
const nextRuns = computeNextRuns('FREQ=MONTHLY;BYMONTHDAY=1;BYHOUR=0;BYMINUTE=0', {
  timezone: 'Europe/London',
  count: 5
});
console.log(nextRuns); // Next 5 months' 1st day at midnight London time as ISO strings
```

## Configuration Options (`JobSchedulerConfig`)

The `JobScheduler` constructor accepts a configuration object with the following options:

| Option                 | Type                                  | Description                                                                                                | Default            | Required |
| ---------------------- | ------------------------------------- | ---------------------------------------------------------------------------------------------------------- | ------------------ | -------- |
| `logger`               | `GenericLogger`                       | Logger instance (e.g., bunyan, pino).                                                                      | -                  | Yes      |
| `adapter`              | `DatabaseAdapter`                     | Database adapter instance (Prisma or Sequelize).                                                           | -                  | Yes      |
| `tasks`                | `Record<string, Function>`            | Map of job names to async handler functions `(data: any, context: { job: JobBase }) => Promise<void>`.       | -                  | Yes      |
| `service`              | `string`                              | Name of the service using the scheduler (used for metrics tagging).                                        | -                  | Yes      |
| `jobsSafeToRestart`    | `string[]`                            | List of job names that can be safely restarted by the maintenance process if stuck.                        | -                  | Yes      |
| `jobsRiskyToRestart`   | `string[]`                            | List of job names that require careful handling (e.g., manual intervention) if stuck.                      | -                  | Yes      |
| `jobsCustomRestart`    | `Record<string, Duration \| undefined>` | Custom restart timeouts (as `moment.duration`) for specific jobs handled by maintenance.                   | -                  | Yes      |
| `lagInMinutes`         | `number`                              | Minutes after starting before a job is considered "lagged" by maintenance.                                 | `6`                | No       |
| `blockedInMinutes`     | `number`                              | Minutes after starting before a job is considered "blocked" (potentially stuck) by maintenance.            | `10`               | No       |
| `defaultRunInterval`   | `number`                              | Default interval (ms) for the scheduler's main loop to check for new jobs.                                 | `5000` (5s)        | No       |
| `maxConcurrentJobs`    | `number`                              | Maximum number of jobs to process concurrently.                                                            | `4`                | No       |
| `backOffMs`            | `number`                              | Base backoff time (ms) used by the adapter for calculating retry delays on failure.                        | `60000` (1m)       | No       |
| `maxRestartsOnFailure` | `number`                              | Maximum number of automatic restarts allowed by the adapter on job failure before marking it as failed.    | `3`                | No       |
| `namespace`            | `string`                              | Optional namespace to isolate jobs in the database (for multi-tenant scenarios).                           | `undefined`        | No       |
| `events`               | `TypedEventEmitter<Events>`           | Optional event emitter for monitoring scheduler events.                                                    | `new EventEmitter()` | No       |
| `metrics`              | `MetricsClient`                       | Client for recording operational metrics (e.g., StatsD, Prometheus).                                       | -                  | Yes      |
| `healthCheckTimeout`   | `number`                              | Max time (ms) since last heartbeat before `healthCheck()` considers the scheduler unhealthy.               | `900000` (15m)     | No       |

## Events

The scheduler emits the following events:

- `lagged`: Emitted when jobs are detected as lagged (running longer than expected)
- `reset`: Emitted when a job is reset for its next run
- `duration`: Emitted with timing information when a job completes
- `pending`: Emitted with information about jobs that should be running but aren't

## License

Apache-2.0
