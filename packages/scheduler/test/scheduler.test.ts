import sinon from 'sinon';
import { EventEmitter } from 'events';
import moment from 'moment-timezone';
import type TypedEmitter from 'typed-emitter';
import { expect } from './setup';
import { SchedulerError } from '../src/errors/scheduler-errors';

// --- Class under test ---
import { JobScheduler } from '../src/scheduler';

// --- Components to Mock ---
import { ConcurrencyManager } from '../src/components/concurrency-manager';
import { JobExecutor } from '../src/components/job-executor';
import { JobFetcher } from '../src/components/job-fetcher';
import { HeartbeatManager } from '../src/components/heartbeat-manager';
import { LifecycleManager } from '../src/components/lifecycle-manager';
import { MaintenanceManager } from '../src/components/maintenance-manager';
import { SchedulerLoop } from '../src/components/scheduler-loop';
import type {
  JobSchedulerConfig,
  GenericLogger,
  MetricsClient,
  Events,
  JobBase,
  DatabaseAdapter,
} from '../src';
import type { JobSet } from '../src/types/job-set.type';
import type { PendingJobs } from '../src/types/pending-jobs.type';
import { TaskMap } from '../src/types/task.type';

// --- Component Modules for Stubbing ---
import * as ConcurrencyManagerModule from '../src/components/concurrency-manager';
import * as JobExecutorModule from '../src/components/job-executor';
import * as JobFetcherModule from '../src/components/job-fetcher';
import * as HeartbeatManagerModule from '../src/components/heartbeat-manager';
import * as LifecycleManagerModule from '../src/components/lifecycle-manager';
import * as MaintenanceManagerModule from '../src/components/maintenance-manager';
import * as SchedulerLoopModule from '../src/components/scheduler-loop';

// --- Dummy Class for Adapter Stubbing (Corrected Signatures) ---
class DummyAdapter implements DatabaseAdapter {
  fetchAndEnqueueJobs(
    _allJobs: string[],
    _maxConcurrentJobs: number,
    _namespace?: string | undefined
  ): Promise<JobSet[]> {
    throw new Error('Method not implemented.');
  }

  // Corrected signature based on error: (job: JobBase, nextRunAt: string) => Promise<void>
  resetJob(_job: JobBase, _nextRunAt: string): Promise<void> {
    throw new Error('Method not implemented.');
  }

  updateStartTask(
    _jobId: string,
    _namespace?: string | undefined
  ): Promise<void> {
    throw new Error('Method not implemented.');
  }

  // Corrected signature based on error: (jobId: string, namespace?: string | undefined) => Promise<void>
  updateFinishTask(
    _jobId: string,
    _namespace?: string | undefined
  ): Promise<void> {
    throw new Error('Method not implemented.');
  }

  updateFailure(
    _jobId: string,
    _backOffMs: number,
    _maxRestartsOnFailure: number,
    _jobsSafeToRestart: string[],
    _namespace?: string | undefined
  ): Promise<void> {
    throw new Error('Method not implemented.');
  }

  getPendingJobs(_allJobs: string[]): Promise<PendingJobs> {
    throw new Error('Method not implemented.');
  }

  getBlockedJobs(
    _jobNames: string[],
    _blockedInMinutes: number
  ): Promise<JobBase[]> {
    throw new Error('Method not implemented.');
  }

  getLaggedJobs(
    _jobNames: string[],
    _lagInMinutes: number
  ): Promise<JobBase[]> {
    throw new Error('Method not implemented.');
  }

  disconnect(): Promise<void> {
    throw new Error('Method not implemented.');
  }
}

// --- Mock Implementations ---
let mockConcurrencyManager: sinon.SinonStubbedInstance<ConcurrencyManager>;
let mockJobExecutor: sinon.SinonStubbedInstance<JobExecutor>;
let mockJobFetcher: sinon.SinonStubbedInstance<JobFetcher>;
let mockHeartbeatManager: sinon.SinonStubbedInstance<HeartbeatManager>;
let mockLifecycleManager: sinon.SinonStubbedInstance<LifecycleManager>;
let mockMaintenanceManager: sinon.SinonStubbedInstance<MaintenanceManager>;
let mockSchedulerLoop: sinon.SinonStubbedInstance<SchedulerLoop>;

// --- Stubs for Dependencies ---
let mockAdapter: sinon.SinonStubbedInstance<DatabaseAdapter>;
let mockLogger: sinon.SinonStubbedInstance<GenericLogger>;
let mockMetrics: sinon.SinonStubbedInstance<MetricsClient>;
let mockEvents: sinon.SinonStubbedInstance<TypedEmitter<Events>>;
let mockTasks: TaskMap;

// --- Test Suite ---
describe('JobScheduler (Integration)', () => {
  let sandbox: sinon.SinonSandbox;
  let scheduler: JobScheduler;
  let baseConfig: JobSchedulerConfig;

  beforeEach(() => {
    sandbox = sinon.createSandbox();

    // --- Mock Dependencies ---
    mockLogger = {
      info: sandbox.stub(),
      error: sandbox.stub(),
      warn: sandbox.stub(),
      debug: sandbox.stub(),
      child: sandbox.stub().returnsThis(),
    } as unknown as sinon.SinonStubbedInstance<GenericLogger>;
    mockMetrics = {
      increment: sandbox.stub(),
      gauge: sandbox.stub(),
      timing: sandbox.stub(),
    };
    mockEvents = sandbox.createStubInstance(EventEmitter) as any;

    // Use DummyAdapter for createStubInstance
    mockAdapter = sandbox.createStubInstance(DummyAdapter);
    mockAdapter.fetchAndEnqueueJobs.resolves([]);
    mockAdapter.resetJob.resolves();
    mockAdapter.updateStartTask.resolves();
    mockAdapter.updateFinishTask.resolves();
    mockAdapter.updateFailure.resolves();
    mockAdapter.getPendingJobs.resolves({});
    mockAdapter.getBlockedJobs.resolves([]);
    mockAdapter.getLaggedJobs.resolves([]);
    mockAdapter.disconnect.resolves();

    mockTasks = {
      'task-1': sandbox.stub().resolves(),
      'task-2': sandbox.stub().resolves(),
    };

    // --- Mock Components ---
    mockConcurrencyManager = sandbox.createStubInstance(ConcurrencyManager);
    mockJobExecutor = sandbox.createStubInstance(JobExecutor);
    mockJobFetcher = sandbox.createStubInstance(JobFetcher);
    mockHeartbeatManager = sandbox.createStubInstance(HeartbeatManager);
    mockLifecycleManager = sandbox.createStubInstance(LifecycleManager);
    mockMaintenanceManager = sandbox.createStubInstance(MaintenanceManager);
    mockSchedulerLoop = sandbox.createStubInstance(SchedulerLoop);

    // --- Stub Component Instantiation (using module stubbing) ---
    sandbox
      .stub(ConcurrencyManagerModule, 'ConcurrencyManager')
      .returns(mockConcurrencyManager as any); // Cast to any to satisfy constructor signature mismatch
    sandbox
      .stub(JobExecutorModule, 'JobExecutor')
      .returns(mockJobExecutor as any);
    sandbox.stub(JobFetcherModule, 'JobFetcher').returns(mockJobFetcher as any);
    sandbox
      .stub(HeartbeatManagerModule, 'HeartbeatManager')
      .returns(mockHeartbeatManager as any);
    sandbox
      .stub(LifecycleManagerModule, 'LifecycleManager')
      .returns(mockLifecycleManager as any);
    sandbox
      .stub(MaintenanceManagerModule, 'MaintenanceManager')
      .returns(mockMaintenanceManager as any);
    sandbox
      .stub(SchedulerLoopModule, 'SchedulerLoop')
      .returns(mockSchedulerLoop as any);

    // --- Base Config ---
    baseConfig = {
      logger: mockLogger,
      metrics: mockMetrics,
      events: mockEvents,
      adapter: mockAdapter, // This should now be type-compatible
      tasks: mockTasks,
      service: 'test-service',
      jobsRiskyToRestart: ['risky-1'],
      jobsSafeToRestart: ['safe-1'],
      jobsCustomRestart: { 'custom-1': moment.duration(10, 'minutes') },
      maxConcurrentJobs: 5,
      defaultRunInterval: 5000,
      namespace: 'test-ns',
      lagInMinutes: 6,
      blockedInMinutes: 10,
    };

    // --- Instantiate Scheduler ---
    scheduler = new JobScheduler(baseConfig);
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('Constructor', () => {
    it('should instantiate all components', () => {
      expect(scheduler).to.be.an.instanceOf(JobScheduler);
      expect((scheduler as any).concurrencyManager).to.equal(
        mockConcurrencyManager
      );
      expect((scheduler as any).jobExecutor).to.equal(mockJobExecutor);
      expect((scheduler as any).jobFetcher).to.equal(mockJobFetcher);
      expect((scheduler as any).heartbeatManager).to.equal(
        mockHeartbeatManager
      );
      expect((scheduler as any).lifecycleManager).to.equal(
        mockLifecycleManager
      );
      expect((scheduler as any).maintenanceManager).to.equal(
        mockMaintenanceManager
      );
      expect((scheduler as any).schedulerLoop).to.equal(mockSchedulerLoop);
    });

    it('should calculate allJobs correctly', () => {
      const configWithTasks = {
        ...baseConfig,
        tasks: { 'task-a': sandbox.stub(), 'task-b': sandbox.stub() },
        jobsSafeToRestart: ['safe-a', 'task-a'],
        jobsRiskyToRestart: ['risky-a'],
        jobsCustomRestart: { 'custom-a': moment.duration(1, 'm') },
      };
      const testScheduler = new JobScheduler(configWithTasks);
      const expectedJobs = [
        'task-a',
        'task-b',
        'custom-a',
        'safe-a',
        'risky-a',
      ];
      expect((testScheduler as any).allJobs).to.be.an('array');
      expect((testScheduler as any).allJobs).to.have.members(expectedJobs);
      expect((testScheduler as any).allJobs.length).to.equal(
        expectedJobs.length
      );
    });
  });

  describe('runScheduledJobs', () => {
    it('should initialize and start the scheduler loop if not running', async () => {
      mockLifecycleManager.getIsExiting.returns(false);
      mockSchedulerLoop.isRunning.returns(false);
      mockLifecycleManager.getIsInitialized.returns(false);

      await scheduler.runScheduledJobs();

      expect(mockMaintenanceManager.start.calledOnce).to.be.true;
      expect(mockLifecycleManager.setInitialized.calledOnceWith(true)).to.be
        .true;
      expect(mockSchedulerLoop.start.calledOnce).to.be.true;
      expect(mockSchedulerLoop.start.firstCall.args[0]).to.equal(
        baseConfig.defaultRunInterval
      );
    });

    it('should use provided waitTime for the scheduler loop', async () => {
      mockLifecycleManager.getIsExiting.returns(false);
      mockSchedulerLoop.isRunning.returns(false);
      mockLifecycleManager.getIsInitialized.returns(true);

      const customInterval = 10000;
      await scheduler.runScheduledJobs(customInterval);

      expect(mockSchedulerLoop.start.calledOnce).to.be.true;
      expect(mockSchedulerLoop.start.firstCall.args[0]).to.equal(
        customInterval
      );
    });

    it('should not start if already running', async () => {
      mockLifecycleManager.getIsExiting.returns(false);
      mockSchedulerLoop.isRunning.returns(true);

      await scheduler.runScheduledJobs();

      expect(mockMaintenanceManager.start.called).to.be.false;
      expect(mockLifecycleManager.setInitialized.called).to.be.false;
      expect(mockSchedulerLoop.start.called).to.be.false;
      expect(mockLogger.warn.calledWith(sinon.match(/loop is already running/)))
        .to.be.true;
    });

    it('should not start if exiting', async () => {
      mockLifecycleManager.getIsExiting.returns(true);

      await scheduler.runScheduledJobs();

      expect(mockSchedulerLoop.isRunning.called).to.be.false;
      expect(mockMaintenanceManager.start.called).to.be.false;
      expect(mockLifecycleManager.setInitialized.called).to.be.false;
      expect(mockSchedulerLoop.start.called).to.be.false;
      expect(
        mockLogger.warn.calledWith(sinon.match(/scheduler is terminating/))
      ).to.be.true;
    });

    it('should handle and throw initialization errors', async () => {
      mockLifecycleManager.getIsExiting.returns(false);
      mockSchedulerLoop.isRunning.returns(false);
      mockLifecycleManager.getIsInitialized.returns(false);

      const initError = new Error('Maintenance failed');
      mockMaintenanceManager.start.rejects(initError);

      await expect(scheduler.runScheduledJobs()).to.be.rejectedWith(
        SchedulerError,
        /Failed to start scheduler/
      );

      expect(mockLifecycleManager.setInitialized.calledOnce).to.be.true;
      expect(mockLifecycleManager.setInitialized.firstCall.args[0]).to.be.false;
      expect(mockSchedulerLoop.start.called).to.be.false;
      expect(
        mockLogger.error.calledWith(
          sinon.match.object,
          sinon.match(/Failed to initialize/)
        )
      ).to.be.true;
      expect(
        mockLogger.error.calledWith(
          sinon.match.object,
          sinon.match(/Failed to start scheduler/)
        )
      ).to.be.true;
    });
  });

  describe('pause', () => {
    it('should call lifecycleManager.pause()', async () => {
      await scheduler.pause();
      expect(mockLifecycleManager.pause.calledOnce).to.be.true;
    });
  });

  describe('resume', () => {
    it('should call lifecycleManager.resume()', async () => {
      await scheduler.resume();
      expect(mockLifecycleManager.resume.calledOnce).to.be.true;
    });
  });

  describe('healthCheck', () => {
    it('should return true and beat heartbeat if jobs are processing', async () => {
      mockConcurrencyManager.getActiveCount.returns(1);

      const result = await scheduler.healthCheck();

      expect(result).to.be.true;
      expect(mockHeartbeatManager.beat.calledOnce).to.be.true;
      expect(mockHeartbeatManager.checkHealth.called).to.be.false;
    });

    it('should call heartbeatManager.checkHealth() if no jobs are processing', async () => {
      mockConcurrencyManager.getActiveCount.returns(0);
      mockHeartbeatManager.checkHealth.resolves(true);

      const result = await scheduler.healthCheck();

      expect(result).to.be.true;
      expect(mockHeartbeatManager.beat.called).to.be.false;
      expect(mockHeartbeatManager.checkHealth.calledOnce).to.be.true;
    });

    it('should throw SchedulerError if heartbeatManager deems unhealthy', async () => {
      mockConcurrencyManager.getActiveCount.returns(0);
      const healthError = new SchedulerError('Heartbeat too old');
      mockHeartbeatManager.checkHealth.rejects(healthError);

      await expect(scheduler.healthCheck()).to.be.rejectedWith(healthError);

      expect(mockHeartbeatManager.checkHealth.calledOnce).to.be.true;
    });
  });

  describe('terminate', () => {
    beforeEach(() => {
      mockLifecycleManager.getIsExiting.returns(false);
      mockConcurrencyManager.waitForRunningJobs.resolves(true);
      mockMaintenanceManager.stop.resolves();
      mockAdapter.disconnect.resolves();
    });

    it('should orchestrate termination sequence correctly', async () => {
      await scheduler.terminate();

      const signalExitOrder = mockLifecycleManager.signalExit.calledBefore(
        mockSchedulerLoop.stop
      );
      const stopLoopOrder = mockSchedulerLoop.stop.calledBefore(
        mockMaintenanceManager.stop
      );
      const stopMaintOrder = mockMaintenanceManager.stop.calledBefore(
        mockConcurrencyManager.waitForRunningJobs
      );
      const waitJobsOrder =
        mockConcurrencyManager.waitForRunningJobs.calledBefore(
          mockConcurrencyManager.clearRunningJobs
        );
      const clearJobsOrder =
        mockConcurrencyManager.clearRunningJobs.calledBefore(
          mockAdapter.disconnect
        );
      const disconnectOrder = mockAdapter.disconnect.calledBefore(
        mockLifecycleManager.reset
      );
      const resetLifecycleOrder = mockLifecycleManager.reset.calledBefore(
        mockHeartbeatManager.reset
      );
      const resetHeartbeatOrder = mockHeartbeatManager.reset.calledAfter(
        mockAdapter.disconnect
      );

      expect(signalExitOrder, 'signalExit before stopLoop').to.be.true;
      expect(stopLoopOrder, 'stopLoop before stopMaintenance').to.be.true;
      expect(stopMaintOrder, 'stopMaintenance before waitForJobs').to.be.true;
      expect(waitJobsOrder, 'waitForJobs before clearJobs').to.be.true;
      expect(clearJobsOrder, 'clearJobs before disconnect').to.be.true;
      expect(disconnectOrder, 'disconnect before resetLifecycle').to.be.true;
      expect(resetLifecycleOrder, 'resetLifecycle before resetHeartbeat').to.be
        .true;
      expect(resetHeartbeatOrder, 'resetHeartbeat after disconnect').to.be.true;

      expect(mockLifecycleManager.setInitialized.calledWith(false)).to.be.true;
      expect(
        mockLogger.info.calledWith(
          sinon.match.object,
          sinon.match(/Termination complete/)
        )
      ).to.be.true;
    });

    it('should not run if already exiting', async () => {
      mockLifecycleManager.getIsExiting.returns(true);

      await scheduler.terminate();

      expect(mockLifecycleManager.signalExit.called).to.be.false;
      expect(mockSchedulerLoop.stop.called).to.be.false;
      expect(mockMaintenanceManager.stop.called).to.be.false;
      expect(mockConcurrencyManager.waitForRunningJobs.called).to.be.false;
      expect(mockAdapter.disconnect.called).to.be.false;
      expect(
        mockLogger.warn.calledWith(
          sinon.match(/Termination already in progress/)
        )
      ).to.be.true;
    });

    it('should log warning if waitForRunningJobs times out', async () => {
      mockConcurrencyManager.waitForRunningJobs.resolves(false);

      await scheduler.terminate();

      expect(mockConcurrencyManager.waitForRunningJobs.calledOnce).to.be.true;
      expect(
        mockLogger.warn.calledWith(sinon.match(/jobs may not have completed/))
      ).to.be.true;
      expect(mockAdapter.disconnect.calledOnce).to.be.true;
    });

    it('should handle adapter disconnect errors and continue termination', async () => {
      const disconnectError = new Error('DB connection failed');
      mockAdapter.disconnect.rejects(disconnectError);

      await scheduler.terminate();

      expect(mockAdapter.disconnect.calledOnce).to.be.true;
      expect(
        mockLogger.error.calledWith(
          sinon.match({ err: disconnectError }),
          sinon.match(/Error disconnecting database adapter/)
        )
      ).to.be.true;
      expect(mockLifecycleManager.reset.calledOnce).to.be.true;
      expect(mockHeartbeatManager.reset.calledOnce).to.be.true;
      expect(mockLifecycleManager.setInitialized.calledWith(false)).to.be.true;
      expect(
        mockLogger.info.calledWith(
          sinon.match.object,
          sinon.match(/Termination complete/)
        )
      ).to.be.true;
    });

    it('should handle errors during the termination sequence', async () => {
      const maintStopError = new Error('Cannot stop maintenance');
      mockMaintenanceManager.stop.rejects(maintStopError);

      await expect(scheduler.terminate()).to.be.rejectedWith(
        SchedulerError,
        /Error during scheduler termination process/
      );

      expect(
        mockLogger.error.calledWith(
          sinon.match.object,
          sinon.match(/Error during scheduler termination process/)
        )
      ).to.be.true;
      expect(mockMetrics.increment.calledWith('scheduler.termination.error', 1))
        .to.be.true;
      expect(mockLifecycleManager.signalExit.calledTwice).to.be.true;
      expect(mockLifecycleManager.setInitialized.calledWith(false)).to.be.true;
    });
  });
});
