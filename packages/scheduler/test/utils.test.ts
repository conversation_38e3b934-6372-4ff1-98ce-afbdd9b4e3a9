import sinon from 'sinon';
import moment from 'moment-timezone';
import { expect } from './setup';
import {
  isHealthy,
  computeNextRun,
  computeNextRuns,
  retryDelay,
} from '../src/utils';

describe('utils', () => {
  let sandbox;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('isHealthy', () => {
    it('should return true when heartbeat is within timeout', () => {
      const now = new Date().getTime();
      const heartbeat = now - 1000; // 1 second ago
      const timeout = 5000; // 5 seconds
      expect(isHealthy(heartbeat, timeout)).to.be.true;
    });

    it('should return false when heartbeat is outside timeout', () => {
      const now = new Date().getTime();
      const heartbeat = now - 10000; // 10 seconds ago
      const timeout = 5000; // 5 seconds
      expect(isHealthy(heartbeat, timeout)).to.be.false;
    });
  });

  describe('computeNextRuns', () => {
    beforeEach(() => {
      // Fix the current date for testing
      sandbox.useFakeTimers({
        now: new Date('2023-01-01T00:00:00Z'),
        toFake: ['Date'],
      });
    });

    it('should throw error when interval is empty', () => {
      expect(() => computeNextRuns('')).to.throw(
        'Need a valid interval to compute next runs'
      );
    });

    it('should compute the next run for a daily interval', () => {
      const interval = 'FREQ=DAILY';
      const result = computeNextRuns(interval);
      expect(result).to.be.an('array');
      expect(result).to.have.lengthOf(1);
      expect(moment(result[0]).format('YYYY-MM-DD')).to.equal('2023-01-01');
    });

    it('should compute multiple next runs when count is provided', () => {
      const interval = 'FREQ=DAILY';
      const result = computeNextRuns(interval, { count: 3 });
      expect(result).to.have.lengthOf(3);
      expect(moment(result[0]).format('YYYY-MM-DD')).to.equal('2023-01-01');
      expect(moment(result[1]).format('YYYY-MM-DD')).to.equal('2023-01-02');
      expect(moment(result[2]).format('YYYY-MM-DD')).to.equal('2023-01-03');
    });

    it('should respect the timezone parameter', () => {
      const interval = 'FREQ=DAILY';
      const result = computeNextRuns(interval, {
        timezone: 'America/New_York',
      });
      expect(result).to.have.lengthOf(1);
      // The result should be in UTC but calculated based on the NY timezone
      const resultInNY = moment(result[0]).tz('America/New_York');
      expect(resultInNY.format('HH:mm')).to.equal('19:00');
    });

    it('should respect the startDate parameter', () => {
      const interval = 'FREQ=DAILY';
      const startDate = '2023-02-15T00:00:00Z';
      const result = computeNextRuns(interval, { startDate });
      expect(result).to.have.lengthOf(1);
      expect(moment(result[0]).format('YYYY-MM-DD')).to.equal('2023-02-15');
    });

    it('should handle complete iCal rules including DTSTART', () => {
      const interval = 'DTSTART;TZID=UTC:20230115T000000\nRRULE:FREQ=DAILY';
      const result = computeNextRuns(interval);
      expect(result).to.have.lengthOf(1);
      expect(moment(result[0]).format('YYYY-MM-DD')).to.equal('2023-01-15');
    });

    it('should limit the number of results to 30 even if more are requested', () => {
      const interval = 'FREQ=DAILY';
      const result = computeNextRuns(interval, { count: 50 });
      expect(result).to.have.lengthOf(30);
    });
  });

  describe('computeNextRun', () => {
    beforeEach(() => {
      // Fix the current date for testing
      sandbox.useFakeTimers({
        now: new Date('2023-01-01T00:00:00Z'),
        toFake: ['Date'],
      });
    });

    it('should throw error when interval is empty', () => {
      expect(() => computeNextRun('')).to.throw(
        'Need a valid interval to compute next run'
      );
    });

    it('should compute the next run for a daily interval', () => {
      const interval = 'FREQ=DAILY';
      const result = computeNextRun(interval);
      expect(moment(result).format('YYYY-MM-DD')).to.equal('2023-01-01');
    });

    it('should respect the timezone parameter', () => {
      const interval = 'FREQ=DAILY';
      const result = computeNextRun(interval, { timezone: 'America/New_York' });
      const resultInNY = moment(result).tz('America/New_York');
      expect(resultInNY.format('HH:mm')).to.equal('19:00');
    });

    it('should respect the startDate parameter', () => {
      const interval = 'FREQ=DAILY';
      const startDate = '2023-02-15T00:00:00Z';
      const result = computeNextRun(interval, { startDate });
      expect(moment(result).format('YYYY-MM-DD')).to.equal('2023-02-15');
    });
  });

  describe('retryDelay', () => {
    beforeEach(() => {
      // Use a fixed random value for testability
      sandbox.stub(Math, 'random').returns(0.5);
    });

    it('should calculate exponential backoff without jitter', () => {
      expect(retryDelay(0, 1000, 2, false)).to.equal(1000);
      expect(retryDelay(1, 1000, 2, false)).to.equal(2000);
      expect(retryDelay(2, 1000, 2, false)).to.equal(4000);
      expect(retryDelay(3, 1000, 2, false)).to.equal(8000);
    });

    it('should apply jitter when enabled', () => {
      // With Math.random() stubbed to 0.5
      expect(retryDelay(0, 1000, 2, true)).to.equal(500);
      expect(retryDelay(1, 1000, 2, true)).to.equal(1000);
      expect(retryDelay(2, 1000, 2, true)).to.equal(2000);
    });

    it('should use default values when not provided', () => {
      // Default: backoff=1000, factor=2, jitter=true, Math.random()=0.5
      expect(retryDelay(0)).to.equal(500);
      expect(retryDelay(1)).to.equal(1000);
    });

    it('should allow custom factor values', () => {
      expect(retryDelay(0, 1000, 3, false)).to.equal(1000);
      expect(retryDelay(1, 1000, 3, false)).to.equal(3000);
      expect(retryDelay(2, 1000, 3, false)).to.equal(9000);
    });
  });
});
