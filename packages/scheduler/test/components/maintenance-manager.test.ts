import sinon from 'sinon';
import { EventEmitter } from 'events';
import type TypedEmitter from 'typed-emitter';
import moment from 'moment-timezone';
import { expect } from '../setup';
import { MaintenanceManager } from '../../src/components/maintenance-manager';
import { Maintenance } from '../../src/maintenance'; // Import the original class
import type {
  JobSchedulerConfig,
  Events,
  DatabaseAdapter,
  GenericLogger,
  MetricsClient,
} from '../../src';
import { SchedulerError } from '../../src/errors/scheduler-errors';

describe('MaintenanceManager', () => {
  let sandbox: sinon.SinonSandbox;
  let mockAdapter: sinon.SinonStubbedInstance<DatabaseAdapter>;
  let mockLogger: sinon.SinonStubbedInstance<GenericLogger>;
  let mockMetrics: sinon.SinonStubbedInstance<MetricsClient>;
  let mockEvents: TypedEmitter<Events>;
  let maintenanceManager: MaintenanceManager;
  let mockConfig: JobSchedulerConfig;
  let maintenanceInstance: Maintenance | undefined; // To hold the instance created by the manager

  // Define stubs for the full IAdapter interface
  let adapterStubs: {
    fetchAndEnqueueJobs: sinon.SinonStub;
    resetJob: sinon.SinonStub;
    updateStartTask: sinon.SinonStub;
    updateFinishTask: sinon.SinonStub;
    updateFailure: sinon.SinonStub;
    getPendingJobs: sinon.SinonStub;
    getBlockedJobs: sinon.SinonStub;
    getLaggedJobs: sinon.SinonStub;
    disconnect: sinon.SinonStub;
  };

  // Stubs for the Maintenance instance methods
  let maintenanceStartStub: sinon.SinonStub;
  let maintenanceStopStub: sinon.SinonStub;

  beforeEach(() => {
    sandbox = sinon.createSandbox();

    // Provide stubs for all IAdapter methods
    adapterStubs = {
      fetchAndEnqueueJobs: sandbox.stub().resolves([]),
      resetJob: sandbox.stub().resolves(),
      updateStartTask: sandbox.stub().resolves(),
      updateFinishTask: sandbox.stub().resolves(),
      updateFailure: sandbox.stub().resolves(),
      getPendingJobs: sandbox.stub().resolves({}),
      getBlockedJobs: sandbox.stub().resolves([]),
      getLaggedJobs: sandbox.stub().resolves([]),
      disconnect: sandbox.stub().resolves(),
    };
    mockAdapter = adapterStubs as sinon.SinonStubbedInstance<DatabaseAdapter>;

    mockLogger = {
      info: sandbox.stub(),
      error: sandbox.stub(),
      warn: sandbox.stub(),
      debug: sandbox.stub(),
    };
    mockMetrics = {
      increment: sandbox.stub(),
      timing: sandbox.stub(),
      gauge: sandbox.stub(),
    };
    mockEvents = new EventEmitter() as TypedEmitter<Events>;

    mockConfig = {
      logger: mockLogger,
      adapter: mockAdapter,
      events: mockEvents,
      metrics: mockMetrics,
      service: 'test-maintenance-service',
      jobsSafeToRestart: ['safe1', 'safe2'],
      jobsRiskyToRestart: ['risky1'],
      jobsCustomRestart: { custom1: moment.duration(10, 'minutes') },
      tasks: {},
      lagInMinutes: 7,
      blockedInMinutes: 12,
    };

    maintenanceManager = new MaintenanceManager(
      mockConfig,
      mockAdapter,
      mockLogger,
      mockMetrics,
      mockEvents
    );

    // Stub methods on the prototype *before* instance creation by the manager
    // This ensures the instance created inside `start()` gets the stubs.
    maintenanceStartStub = sandbox
      .stub(Maintenance.prototype, 'start')
      .resolves();
    maintenanceStopStub = sandbox.stub(Maintenance.prototype, 'stop'); // Assuming sync or async based on usage
  });

  afterEach(() => {
    sandbox.restore(); // This automatically restores prototype stubs
    maintenanceInstance = undefined; // Clear instance ref
  });

  describe('start', () => {
    it('should create a new Maintenance instance and call its start method', async () => {
      await maintenanceManager.start();

      // Verify the instance was created internally (indirect check)
      maintenanceInstance = (maintenanceManager as any).maintenance;
      expect(maintenanceInstance).to.be.instanceOf(Maintenance);

      // Verify start was called on the instance
      expect(maintenanceStartStub.calledOnce).to.be.true;

      // We can also check constructor args if needed by spying/stubbing the constructor itself,
      // but verifying the start call is often sufficient for the manager's responsibility.
    });

    // Test for config passing (less direct now, relies on Maintenance internal checks)
    // We trust the Maintenance constructor uses the args, verified by its own tests.

    it('should log a warning if start is called when already started', async () => {
      await maintenanceManager.start(); // First start
      mockLogger.warn.resetHistory();
      maintenanceStartStub.resetHistory();

      await maintenanceManager.start(); // Second start

      expect(maintenanceStartStub.called).to.be.false; // start not called again
      expect(
        mockLogger.warn.calledOnceWith(
          'MaintenanceManager: Start called but already started.'
        )
      ).to.be.true;
    });

    it('should throw SchedulerError and log if Maintenance.start() fails', async () => {
      const startError = new Error('Failed to init maintenance');
      maintenanceStartStub.rejects(startError); // Make the stubbed start fail

      try {
        await maintenanceManager.start();
        expect.fail('Should have thrown SchedulerError');
      } catch (error) {
        expect(error).to.be.instanceOf(SchedulerError);
        expect((error as SchedulerError).message).to.equal(
          'Failed to start maintenance task'
        );
        expect((error as SchedulerError).cause).to.equal(startError);
        expect(mockLogger.error.calledOnce).to.be.true;
        // Ensure internal maintenance instance is cleared on failure
        expect((maintenanceManager as any).maintenance).to.be.undefined;
      }
    });
  });

  describe('stop', () => {
    beforeEach(async () => {
      // Ensure maintenance is started before testing stop
      await maintenanceManager.start();
      mockLogger.info.resetHistory(); // Clear start logs
      maintenanceInstance = (maintenanceManager as any).maintenance; // Get ref to the instance
    });

    it('should call Maintenance.stop() on the instance if started', async () => {
      expect(maintenanceInstance).to.exist; // Make sure instance exists

      await maintenanceManager.stop();

      // Verify stop was called on the specific instance
      expect(maintenanceStopStub.calledOnce).to.be.true;
      // Check that the stub called belongs to the correct instance if needed (more complex)
      // expect(maintenanceStopStub.calledOn(maintenanceInstance)).to.be.true; // This syntax might vary

      expect((maintenanceManager as any).maintenance).to.be.undefined; // Should be cleared after stop
    });

    it('should log info and do nothing if stop is called when not started', async () => {
      await maintenanceManager.stop(); // Stop it first
      mockLogger.info.resetHistory();
      maintenanceStopStub.resetHistory();

      await maintenanceManager.stop(); // Call stop again

      expect(maintenanceStopStub.called).to.be.false; // stop not called again
    });

    it('should log error but not throw if Maintenance.stop() fails', async () => {
      const stopError = new Error('Failed to stop maintenance');
      maintenanceStopStub.rejects(stopError); // Make stop fail

      await maintenanceManager.stop(); // Should not throw

      expect(maintenanceStopStub.calledOnce).to.be.true;
      expect(mockLogger.error.calledOnce).to.be.true;
      expect((maintenanceManager as any).maintenance).to.be.undefined; // Should still be cleared on failure
    });
  });
});
