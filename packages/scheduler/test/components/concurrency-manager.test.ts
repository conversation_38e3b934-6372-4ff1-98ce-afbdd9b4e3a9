import sinon from 'sinon';
import { createLogger } from 'bunyan';
import { expect } from '../setup';
import { ConcurrencyManager } from '../../src/components/concurrency-manager';

describe('ConcurrencyManager', () => {
  let sandbox: sinon.SinonSandbox;
  let mockLogger: sinon.SinonStubbedInstance<ReturnType<typeof createLogger>>;
  let concurrencyManager: ConcurrencyManager;
  const maxConcurrentJobs = 3;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    // Use a simple stub for the logger for now
    mockLogger = {
      info: sandbox.stub(),
      error: sandbox.stub(),
      warn: sandbox.stub(),
      debug: sandbox.stub(),
    } as any; // Cast to avoid full logger type complexity in stub
    concurrencyManager = new ConcurrencyManager(maxConcurrentJobs, mockLogger);
  });

  afterEach(() => {
    sandbox.restore();
  });

  it('should initialize with correct max concurrent jobs', () => {
    expect((concurrencyManager as any).maxConcurrentJobs).to.equal(
      maxConcurrentJobs
    );
    expect(concurrencyManager.getActiveCount()).to.equal(0);
  });

  describe('canRunMore', () => {
    it('should return true when active count is less than max', () => {
      expect(concurrencyManager.canRunMore()).to.be.true;
    });

    it('should return false when active count reaches max', () => {
      for (let i = 0; i < maxConcurrentJobs; i += 1) {
        concurrencyManager.tryReserveSlot();
      }
      expect(concurrencyManager.canRunMore()).to.be.false;
    });
  });

  describe('tryReserveSlot', () => {
    it('should return true and increment active count when below max', () => {
      expect(concurrencyManager.getActiveCount()).to.equal(0);

      const result = concurrencyManager.tryReserveSlot();

      expect(result).to.be.true;
      expect(concurrencyManager.getActiveCount()).to.equal(1);
    });

    it('should return false and not increment when at max capacity', () => {
      // Fill to capacity using tryReserveSlot
      for (let i = 0; i < maxConcurrentJobs; i += 1) {
        const result = concurrencyManager.tryReserveSlot();
        expect(result).to.be.true;
      }
      expect(concurrencyManager.getActiveCount()).to.equal(maxConcurrentJobs);

      // Try to reserve one more
      const result = concurrencyManager.tryReserveSlot();

      expect(result).to.be.false;
      expect(concurrencyManager.getActiveCount()).to.equal(maxConcurrentJobs);
    });
  });

  describe('releaseReservedSlot', () => {
    it('should decrease the active job count', () => {
      // Reserve two slots
      concurrencyManager.tryReserveSlot();
      concurrencyManager.tryReserveSlot();
      expect(concurrencyManager.getActiveCount()).to.equal(2);

      // Release one slot
      concurrencyManager.releaseReservedSlot();
      expect(concurrencyManager.getActiveCount()).to.equal(1);

      // Release another slot
      concurrencyManager.releaseReservedSlot();
      expect(concurrencyManager.getActiveCount()).to.equal(0);
    });

    it('should not decrease below zero', () => {
      expect(concurrencyManager.getActiveCount()).to.equal(0);

      // Try to release when already at zero
      concurrencyManager.releaseReservedSlot();

      expect(concurrencyManager.getActiveCount()).to.equal(0);
    });
  });

  describe('startJob', () => {
    it('should track the job without changing active count', () => {
      concurrencyManager.tryReserveSlot();
      expect(concurrencyManager.getActiveCount()).to.equal(1);

      const jobPromise1 = Promise.resolve();
      concurrencyManager.startJob('job-1', jobPromise1);

      // Active count should remain the same (slot was already reserved)
      expect(concurrencyManager.getActiveCount()).to.equal(1);
      expect((concurrencyManager as any).runningJobs.has('job-1')).to.be.true;

      // Reserve another slot and start another job
      concurrencyManager.tryReserveSlot();
      expect(concurrencyManager.getActiveCount()).to.equal(2);

      const jobPromise2 = Promise.resolve();
      concurrencyManager.startJob('job-2', jobPromise2);

      expect(concurrencyManager.getActiveCount()).to.equal(2);
      expect((concurrencyManager as any).runningJobs.has('job-2')).to.be.true;
    });
  });

  describe('endJob', () => {
    it('should decrease the active job count and remove the job from tracking', () => {
      // First reserve slots
      concurrencyManager.tryReserveSlot();
      concurrencyManager.tryReserveSlot();
      expect(concurrencyManager.getActiveCount()).to.equal(2);

      // Then start jobs
      concurrencyManager.startJob('job-1', Promise.resolve());
      concurrencyManager.startJob('job-2', Promise.resolve());
      expect((concurrencyManager as any).runningJobs.has('job-1')).to.be.true;
      expect((concurrencyManager as any).runningJobs.has('job-2')).to.be.true;

      concurrencyManager.endJob('job-1');
      expect(concurrencyManager.getActiveCount()).to.equal(1);
      expect((concurrencyManager as any).runningJobs.has('job-1')).to.be.false;
      expect((concurrencyManager as any).runningJobs.has('job-2')).to.be.true;

      concurrencyManager.endJob('job-2');
      expect(concurrencyManager.getActiveCount()).to.equal(0);
      expect((concurrencyManager as any).runningJobs.has('job-2')).to.be.false;
    });

    it('should not decrease below zero and log warning if job not tracked', () => {
      // End a job that wasn't started
      concurrencyManager.endJob('job-nonexistent');
      expect(concurrencyManager.getActiveCount()).to.equal(0);
      expect(mockLogger.warn.calledOnce).to.be.true;
      expect(mockLogger.warn.firstCall.args[0]).to.deep.equal({
        jobId: 'job-nonexistent',
      });
      expect(mockLogger.warn.firstCall.args[1]).to.include(
        'Attempted to end a job not being tracked'
      );

      // Start and end a job, then try ending again
      concurrencyManager.startJob('job-1', Promise.resolve());
      concurrencyManager.endJob('job-1');
      expect(concurrencyManager.getActiveCount()).to.equal(0);

      mockLogger.warn.resetHistory(); // Reset mock for next check
      concurrencyManager.endJob('job-1'); // Try ending again
      expect(concurrencyManager.getActiveCount()).to.equal(0); // Still 0
      expect(mockLogger.warn.calledOnce).to.be.true; // Warning logged again
    });
  });

  describe('waitForRunningJobs', () => {
    let clock: sinon.SinonFakeTimers;

    beforeEach(() => {
      clock = sandbox.useFakeTimers();
    });

    afterEach(() => {
      clock.restore();
    });

    it('should resolve immediately if no jobs are running', async () => {
      const result = await concurrencyManager.waitForRunningJobs(1000);
      expect(result).to.be.true;
    });

    it('should resolve true when all running jobs complete within timeout', async () => {
      // Use promises that resolve to simulate job completion
      let resolveJob1: () => void;
      const jobPromise1 = new Promise<void>(resolve => {
        resolveJob1 = resolve;
      });
      let resolveJob2: () => void;
      const jobPromise2 = new Promise<void>(resolve => {
        resolveJob2 = resolve;
      });

      concurrencyManager.startJob('job-wait-1', jobPromise1);
      concurrencyManager.startJob('job-wait-2', jobPromise2);

      const waitPromise = concurrencyManager.waitForRunningJobs(1000);

      // Simulate jobs completing
      await clock.tickAsync(100);
      resolveJob1!(); // Complete job 1
      concurrencyManager.endJob('job-wait-1'); // Mark job 1 as ended

      await clock.tickAsync(100);
      resolveJob2!(); // Complete job 2
      concurrencyManager.endJob('job-wait-2'); // Mark job 2 as ended

      const result = await waitPromise;
      expect(result).to.be.true;
    });

    it('should resolve false if timeout occurs before all jobs complete', async () => {
      let resolveJob1: () => void;
      const jobPromise1 = new Promise<void>(resolve => {
        resolveJob1 = resolve;
      });
      const jobPromise2 = new Promise<void>(() => {}); // This one never resolves

      concurrencyManager.startJob('job-timeout-1', jobPromise1);
      concurrencyManager.startJob('job-timeout-2', jobPromise2); // This job will cause the timeout

      const waitPromise = concurrencyManager.waitForRunningJobs(500); // Shorter timeout

      // Simulate only one job completing
      await clock.tickAsync(100);
      resolveJob1!();
      concurrencyManager.endJob('job-timeout-1');

      // Advance time past the timeout
      await clock.tickAsync(500);

      const result = await waitPromise;
      expect(result).to.be.false;
      expect(mockLogger.warn.calledOnce).to.be.true;
      // Check the error message content more specifically if needed
      expect(mockLogger.warn.firstCall.args[1]).to.include(
        'Timeout or error waiting for jobs to complete'
      );
    });

    // Note: The periodic check is internal to Promise.race and setTimeout,
    // so we don't need a specific test for the check interval itself.
    // The timeout test implicitly covers the waiting mechanism.
    // Removing the 'should check periodically' test as it's less relevant now.
  });

  describe('clearRunningJobs', () => {
    it('should reset the active job count to zero and clear tracked jobs', () => {
      // First reserve slots
      concurrencyManager.tryReserveSlot();
      concurrencyManager.tryReserveSlot();
      expect(concurrencyManager.getActiveCount()).to.equal(2);

      // Then start jobs
      concurrencyManager.startJob('job-clear-1', Promise.resolve());
      concurrencyManager.startJob('job-clear-2', Promise.resolve());
      expect((concurrencyManager as any).runningJobs.size).to.equal(2);

      concurrencyManager.clearRunningJobs();
      expect(concurrencyManager.getActiveCount()).to.equal(0);
      expect((concurrencyManager as any).runningJobs.size).to.equal(0);
      // Note: The implementation of clearRunningJobs doesn't log warnings/info.
      // If logging is desired here, the source code needs adjustment.
      // expect(mockLogger.warn.calledOnce).to.be.true;
      // expect(mockLogger.warn.firstCall.args[0]).to.include('Clearing 2 tracked running jobs');
    });

    it('should do nothing if no jobs were running', () => {
      expect(concurrencyManager.getActiveCount()).to.equal(0);
      expect((concurrencyManager as any).runningJobs.size).to.equal(0);
      concurrencyManager.clearRunningJobs();
      expect(concurrencyManager.getActiveCount()).to.equal(0);
      expect((concurrencyManager as any).runningJobs.size).to.equal(0);
      // expect(mockLogger.info.calledOnce).to.be.true;
      // expect(mockLogger.info.firstCall.args[0]).to.include('No running jobs to clear');
    });
  });
});
