import sinon from 'sinon';
import { expect } from '../setup';
import { HeartbeatManager } from '../../src/components/heartbeat-manager';
import { SchedulerError } from '../../src/errors/scheduler-errors';
import type { GenericLogger, MetricsClient } from '../../src';

describe('HeartbeatManager', () => {
  let sandbox: sinon.SinonSandbox;
  let mockLogger: sinon.SinonStubbedInstance<GenericLogger>;
  let mockMetrics: sinon.SinonStubbedInstance<MetricsClient>;
  let heartbeatManager: HeartbeatManager;
  let clock: sinon.SinonFakeTimers;
  const healthCheckTimeout = 5000; // 5 seconds for testing
  const serviceName = 'test-heartbeat-service';

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    clock = sandbox.useFakeTimers();

    mockLogger = {
      info: sandbox.stub(),
      error: sandbox.stub(),
      warn: sandbox.stub(),
      debug: sandbox.stub(),
    };
    mockMetrics = {
      increment: sandbox.stub(),
      timing: sandbox.stub(),
      gauge: sandbox.stub(),
    };

    heartbeatManager = new HeartbeatManager(
      mockLogger,
      mockMetrics,
      healthCheckTimeout,
      serviceName
    );
  });

  afterEach(() => {
    clock.restore();
    sandbox.restore();
  });

  it('should initialize with the current time as the first heartbeat', () => {
    const initialHeartbeat = (heartbeatManager as any).lastHeartbeat;
    expect(initialHeartbeat).to.equal(clock.now);
  });

  describe('beat', () => {
    it('should update the last heartbeat timestamp to the current time', () => {
      const initialHeartbeat = (heartbeatManager as any).lastHeartbeat;
      clock.tick(1000); // Advance time by 1 second
      heartbeatManager.beat();
      const newHeartbeat = (heartbeatManager as any).lastHeartbeat;
      expect(newHeartbeat).to.be.greaterThan(initialHeartbeat);
      expect(newHeartbeat).to.equal(clock.now);
    });

    it('should emit a heartbeat metric', () => {
      heartbeatManager.beat();
      expect(
        mockMetrics.increment.calledOnceWith('scheduler.heartbeat', 1, {
          service: serviceName,
        })
      ).to.be.true;
    });
  });

  describe('checkHealth', () => {
    it('should return true if the last heartbeat is within the timeout period', () => {
      clock.tick(healthCheckTimeout - 100); // Advance time, but still within timeout
      expect(heartbeatManager.checkHealth()).to.be.true;
    });

    it('should throw SchedulerError if the last heartbeat is older than the timeout', () => {
      clock.tick(healthCheckTimeout + 100); // Advance time beyond the timeout
      try {
        heartbeatManager.checkHealth();
        expect.fail('checkHealth should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(SchedulerError);
        expect((error as SchedulerError).message).to.include(
          'Scheduler not healthy'
        );
        expect(mockLogger.error.calledOnce).to.be.true;
        expect(
          mockMetrics.increment.calledOnceWith('scheduler.health_check', 1, {
            service: serviceName,
            status: 'unhealthy',
          })
        ).to.be.true;
      }
    });

    it('should return true immediately after a beat, even if time advanced significantly before', () => {
      clock.tick(healthCheckTimeout * 2); // Advance time way beyond timeout
      try {
        heartbeatManager.checkHealth(); // This would throw if beat() wasn't called
        expect.fail('checkHealth should have thrown an error before beat()');
      } catch (error) {
        // Expected error
      }

      heartbeatManager.beat(); // Update heartbeat
      expect(heartbeatManager.checkHealth()).to.be.true; // Now it should be healthy
    });
  });

  describe('reset', () => {
    it('should reset the last heartbeat timestamp to 0', () => {
      heartbeatManager.reset();

      const newHeartbeat = (heartbeatManager as any).lastHeartbeat;
      expect(newHeartbeat).to.equal(0); // Reset should set heartbeat to 0
    });
  });
});
