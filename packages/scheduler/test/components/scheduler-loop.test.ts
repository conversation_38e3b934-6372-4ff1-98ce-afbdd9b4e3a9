import sinon from 'sinon';
import { expect } from '../setup';
import { SchedulerLoop } from '../../src/components/scheduler-loop';
import { JobFetcher } from '../../src/components/job-fetcher';
import { JobExecutor } from '../../src/components/job-executor';
import { ConcurrencyManager } from '../../src/components/concurrency-manager';
import { HeartbeatManager } from '../../src/components/heartbeat-manager';
import { LifecycleManager } from '../../src/components/lifecycle-manager';
import type { JobBase, GenericLogger, MetricsClient } from '../../src';
import type { JobSet } from '../../src/types/job-set.type';
import { TaskMap } from '../../src/types/task.type';

describe('SchedulerLoop', () => {
  let sandbox: sinon.SinonSandbox;
  let mockJobFetcher: sinon.SinonStubbedInstance<JobFetcher>;
  let mockJobExecutor: sinon.SinonStubbedInstance<JobExecutor>;
  let mockConcurrencyManager: sinon.SinonStubbedInstance<ConcurrencyManager>;
  let mockHeartbeatManager: sinon.SinonStubbedInstance<HeartbeatManager>;
  let mockLifecycleManager: sinon.SinonStubbedInstance<LifecycleManager>;
  let mockLogger: sinon.SinonStubbedInstance<GenericLogger>;
  let mockMetrics: sinon.SinonStubbedInstance<MetricsClient>;
  let mockTasks: TaskMap;
  let schedulerLoop: SchedulerLoop;
  let clock: sinon.SinonFakeTimers;

  const loopConfig = {
    defaultRunInterval: 5000, // 5 seconds
    service: 'test-loop-service',
  };

  const jobItem1: JobBase = { id: 'job-1', name: 'task-a', data: { p: 1 } };
  const jobItem2: JobBase = { id: 'job-2', name: 'task-b', data: { p: 2 } };
  const mockJobSets: JobSet[] = [
    { priority: 1, name: 'task-a', total: 1, items: [jobItem1] },
    { priority: 1, name: 'task-b', total: 1, items: [jobItem2] },
  ];

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    clock = sandbox.useFakeTimers();

    // Mock dependencies
    mockJobFetcher = sandbox.createStubInstance(JobFetcher);
    mockJobExecutor = sandbox.createStubInstance(JobExecutor);
    mockConcurrencyManager = sandbox.createStubInstance(ConcurrencyManager);
    mockHeartbeatManager = sandbox.createStubInstance(HeartbeatManager);
    mockLifecycleManager = sandbox.createStubInstance(LifecycleManager);

    mockLogger = {
      info: sandbox.stub(),
      error: sandbox.stub(),
      warn: sandbox.stub(),
      debug: sandbox.stub(),
      child: sandbox.stub().returnsThis(),
    } as unknown as sinon.SinonStubbedInstance<GenericLogger>;
    mockMetrics = {
      increment: sandbox.stub(),
      timing: sandbox.stub(),
      gauge: sandbox.stub(),
    };
    mockTasks = {
      'task-a': sandbox.stub().resolves(),
      'task-b': sandbox.stub().resolves(),
      'task-c': sandbox.stub().resolves(),
    };

    // Configure mocks
    mockJobFetcher.fetchAndEnqueue.resolves(mockJobSets);
    mockConcurrencyManager.canRunMore.returns(true); // Allow jobs initially
    mockLifecycleManager.getIsPaused.returns(false);
    mockLifecycleManager.getIsExiting.returns(false);

    schedulerLoop = new SchedulerLoop(
      mockJobFetcher,
      mockJobExecutor,
      mockConcurrencyManager,
      mockHeartbeatManager,
      mockLifecycleManager,
      mockTasks,
      mockLogger,
      mockMetrics,
      loopConfig
    );
  });

  afterEach(() => {
    // Ensure loop is stopped to clear timers
    schedulerLoop.stop();
    clock.restore();
    sandbox.restore();
  });

  describe('start', () => {
    it('should log info and schedule the first run', () => {
      schedulerLoop.start(loopConfig.defaultRunInterval);
      expect(mockLogger.info.called).to.be.true;
      expect((schedulerLoop as any).currentTimeout).to.exist;
      expect(schedulerLoop.isRunning()).to.be.true;
    });

    it('should not start if already running', () => {
      schedulerLoop.start(loopConfig.defaultRunInterval); // First start
      mockLogger.info.resetHistory();
      schedulerLoop.start(loopConfig.defaultRunInterval); // Second start
      expect(mockLogger.info.called).to.be.false; // No new start log
    });

    it('should trigger an immediate check if interval is 0 or less', () => {
      const runCycleStub = sandbox
        .stub(schedulerLoop as any, 'runCycle')
        .resolves();
      schedulerLoop.start(0);
      clock.tick(1); // Advance time slightly
      // Should run immediately, not just schedule
      expect(runCycleStub.calledOnce).to.be.true;
      // Should also schedule next run (default interval)
      expect((schedulerLoop as any).currentTimeout).to.exist;
    });
  });

  describe('stop', () => {
    it('should clear the timeout, set running flag to false, and log info', () => {
      schedulerLoop.start(loopConfig.defaultRunInterval); // Start it first
      const clearTimeoutSpy = sandbox.spy(clock, 'clearTimeout');

      schedulerLoop.stop();

      expect((schedulerLoop as any).currentTimeout).to.be.undefined;
      expect(schedulerLoop.isRunning()).to.be.false;
      expect(clearTimeoutSpy.calledOnce).to.be.true;
    });

    it('should do nothing if already stopped', () => {
      // Loop is stopped by default after beforeEach
      const clearTimeoutSpy = sandbox.spy(clock, 'clearTimeout');
      mockLogger.info.resetHistory();

      schedulerLoop.stop();

      expect((schedulerLoop as any).currentTimeout).to.be.undefined;
      expect(schedulerLoop.isRunning()).to.be.false;
      expect(clearTimeoutSpy.called).to.be.false;
      expect(mockLogger.info.called).to.be.false; // No new stop log
    });
  });

  describe('runCycle (private method, tested via start)', () => {
    it('should not run if paused', async () => {
      mockLifecycleManager.getIsPaused.returns(true); // Simulate paused state
      schedulerLoop.start(10); // Start with short interval
      await clock.tickAsync(20); // Advance past interval

      expect(mockJobFetcher.fetchAndEnqueue.called).to.be.false;
      // Should still schedule the next check
      expect((schedulerLoop as any).currentTimeout).to.exist;
    });

    it('should not run if exiting', async () => {
      mockLifecycleManager.getIsExiting.returns(true); // Simulate exiting state
      schedulerLoop.start(10);
      await clock.tickAsync(20);

      expect(mockJobFetcher.fetchAndEnqueue.called).to.be.false;
      // Should NOT schedule the next check when exiting
      expect((schedulerLoop as any).currentTimeout).to.be.undefined;
    });

    it('should fetch jobs', async () => {
      schedulerLoop.start(10);
      await clock.tickAsync(20);
      expect(mockJobFetcher.fetchAndEnqueue.called).to.be.true;
    });

    it('should execute fetched jobs if concurrency allows', async () => {
      mockJobExecutor.execute.resolves();
      mockConcurrencyManager.canRunMore.returns(true); // Ensure it can run
      mockConcurrencyManager.tryReserveSlot.returns(true); // Allow reserving slots

      schedulerLoop.start(10);
      await clock.tickAsync(10); // Let the first cycle run

      expect(mockJobExecutor.execute.calledTwice).to.be.true; // Called for job-1 and job-2
      expect(mockJobExecutor.execute.firstCall.args[0]).to.equal('task-a');
      expect(mockJobExecutor.execute.firstCall.args[1]).to.equal(jobItem1);
      expect(mockJobExecutor.execute.firstCall.args[2]).to.equal(
        mockTasks['task-a']
      );
      expect(mockJobExecutor.execute.secondCall.args[0]).to.equal('task-b');
      expect(mockJobExecutor.execute.secondCall.args[1]).to.equal(jobItem2);
      expect(mockJobExecutor.execute.secondCall.args[2]).to.equal(
        mockTasks['task-b']
      );

      // Verify concurrency manager interactions
      expect(mockConcurrencyManager.tryReserveSlot.calledTwice).to.be.true;
      expect(mockConcurrencyManager.startJob.calledTwice).to.be.true;
      // Note: endJob is called within the actual promise resolution of execute,
      // which we are not fully simulating here, just the call to execute.
      // Testing endJob interaction requires more complex promise handling or spying inside execute mock.
    });

    it('should not execute jobs if concurrency limit is reached', async () => {
      mockConcurrencyManager.canRunMore.returns(false); // Simulate limit reached
      schedulerLoop.start(10);
      await clock.tickAsync(10);
      expect(mockJobFetcher.fetchAndEnqueue.calledOnce).to.be.false;
      expect(mockJobExecutor.execute.called).to.be.false;
    });

    it('should handle errors during job fetching', async () => {
      const fetchError = new Error('Fetch failed');
      mockJobFetcher.fetchAndEnqueue.rejects(fetchError);
      schedulerLoop.start(10);

      // Need try/catch because runCycle re-throws fetch errors internally before logging
      try {
        await clock.tickAsync(20);
      } catch (e) {
        // Error expected during testing due to re-throw simulation
      }
      expect(mockJobExecutor.execute.called).to.be.false; // No execution on fetch error
    });

    it('should handle errors during job execution (logged by JobExecutor)', async () => {
      const execError = new Error('Exec failed');
      // Make the first job execution fail (JobExecutor handles logging)
      mockJobExecutor.execute.onFirstCall().callsFake(async () => {
        throw execError;
      });
      mockJobExecutor.execute.onSecondCall().resolves(); // Second job succeeds

      // Allow reserving slots
      mockConcurrencyManager.tryReserveSlot.returns(true);

      schedulerLoop.start(10);
      await clock.tickAsync(10);
      expect(mockJobExecutor.execute.calledTwice).to.be.true; // Both were attempted
      // We expect JobExecutor to log the error, not SchedulerLoop directly for execute errors
      expect(mockLogger.error.called).to.be.false; // Loop itself shouldn't log exec errors
    });

    it('should update heartbeat after processing jobs', async () => {
      mockJobExecutor.execute.resolves();
      schedulerLoop.start(10);
      await clock.tickAsync(10);
      expect(mockHeartbeatManager.beat.calledThrice).to.be.true;
    });

    it('should schedule the next run correctly', async () => {
      mockJobExecutor.execute.resolves();
      schedulerLoop.start(loopConfig.defaultRunInterval);
      const setTimeoutSpy = sandbox.spy(clock, 'setTimeout');
      await clock.tickAsync(loopConfig.defaultRunInterval * 2); // Trigger next cycle

      expect(
        setTimeoutSpy.calledWith(
          sinon.match.func,
          loopConfig.defaultRunInterval
        )
      ).to.be.true;
      // Verify it ran twice (initial schedule + schedule after first run)
      expect(mockJobFetcher.fetchAndEnqueue.calledTwice).to.be.true;
    });
  });

  describe('concurrency', () => {
    it('should not execute more jobs than allowed by concurrency limit', async () => {
      // Set up a job set with 3 jobs of the same type
      const jobItemA: JobBase = { id: 'job-A', name: 'task-a', data: {} };
      const jobItemB: JobBase = { id: 'job-B', name: 'task-a', data: {} };
      const jobItemC: JobBase = { id: 'job-C', name: 'task-a', data: {} };
      const jobSet: JobSet[] = [
        {
          priority: 1,
          name: 'task-a',
          total: 3,
          items: [jobItemA, jobItemB, jobItemC],
        },
      ];
      mockJobFetcher.fetchAndEnqueue.resolves(jobSet);

      // Only allow 1 job to run at a time using tryReserveSlot
      mockConcurrencyManager.tryReserveSlot.onFirstCall().returns(true);
      mockConcurrencyManager.tryReserveSlot.onSecondCall().returns(false);
      mockConcurrencyManager.canRunMore.returns(true); // For initial cycle check

      // Simulate jobExecutor.execute as a long-running promise
      const executeStub = sandbox.stub().callsFake(async () => {
        await new Promise(resolve => {
          setTimeout(resolve, 100);
        });
      });
      mockJobExecutor.execute.callsFake(executeStub);

      // Defer should be called for jobs that can't run due to concurrency
      mockJobExecutor.defer.resolves();

      schedulerLoop.start(10);
      await clock.tickAsync(20); // Let the first cycle run

      // Only one job should be started
      expect(mockJobExecutor.execute.calledOnce).to.be.true;

      // The other two jobs should be deferred via deferRemainingJobs
      // This should be called once with the remaining jobs array
      // The actual count might vary based on implementation details
      expect(mockJobExecutor.defer.called).to.be.true;

      // Verify the deferred jobs are the expected ones
      const deferredJobIds = mockJobExecutor.defer
        .getCalls()
        .map(call => call.args[1].id);
      expect(deferredJobIds).to.include('job-B');
      expect(deferredJobIds).to.include('job-C');

      // No more jobs should be started in this cycle
      expect(mockConcurrencyManager.startJob.calledOnce).to.be.true;
    });

    it('should release reserved slot when exiting', async () => {
      // Reset all stubs from previous tests
      mockConcurrencyManager.tryReserveSlot.reset();
      mockConcurrencyManager.releaseReservedSlot.reset();
      mockLifecycleManager.getIsExiting.reset();
      mockJobExecutor.execute.reset();

      // Set up a job set with 1 job
      const jobItem: JobBase = { id: 'job-exit', name: 'task-a', data: {} };
      const jobSet: JobSet[] = [
        {
          priority: 1,
          name: 'task-a',
          total: 1,
          items: [jobItem],
        },
      ];
      mockJobFetcher.fetchAndEnqueue.resolves(jobSet);

      // Allow reserving a slot
      mockConcurrencyManager.tryReserveSlot.returns(true);

      // But simulate exiting state during job processing
      // First allow the cycle to start normally
      mockLifecycleManager.getIsExiting.returns(false);
      // Then switch to exiting state during job processing
      mockLifecycleManager.getIsExiting.onFirstCall().returns(false); // Initial check in runCycle
      mockLifecycleManager.getIsExiting.onSecondCall().returns(true); // Check during job processing

      schedulerLoop.start(10);
      await clock.tickAsync(20);

      // Should have reserved a slot
      expect(mockConcurrencyManager.tryReserveSlot.called).to.be.true;
    });

    it('should execute some jobs and defer others when concurrency limit is reached mid-batch', async () => {
      // Reset stubs from previous tests
      mockConcurrencyManager.tryReserveSlot.reset();
      mockConcurrencyManager.canRunMore.reset();
      mockJobExecutor.execute.reset();
      mockJobExecutor.defer.reset();

      // Set up a job set with 5 jobs of the same type
      const jobItems: JobBase[] = Array.from({ length: 5 }, (_, i) => ({
        id: `job-mid-batch-${i}`,
        name: 'task-a',
        data: { index: i },
      }));

      const jobSet: JobSet[] = [
        {
          priority: 1,
          name: 'task-a',
          total: jobItems.length,
          items: jobItems,
        },
      ];
      mockJobFetcher.fetchAndEnqueue.resolves(jobSet);

      // Allow the first 3 jobs to run, then hit concurrency limit
      mockConcurrencyManager.canRunMore.returns(true);
      mockConcurrencyManager.tryReserveSlot
        .onCall(0)
        .returns(true)
        .onCall(1)
        .returns(true)
        .onCall(2)
        .returns(true)
        .onCall(3)
        .returns(false); // Concurrency limit reached at the 4th job

      // Simulate job execution
      mockJobExecutor.execute.resolves();
      mockJobExecutor.defer.resolves();

      schedulerLoop.start(10);
      await clock.tickAsync(20);

      // The number of executed jobs may vary based on implementation
      // The important part is that some jobs were executed and some were deferred
      expect(mockJobExecutor.execute.called).to.be.true;
      expect(mockJobExecutor.defer.called).to.be.true;

      // Verify the correct jobs were deferred
      const deferredJobIds = mockJobExecutor.defer
        .getCalls()
        .map(call => call.args[1].id);
      expect(deferredJobIds).to.include('job-mid-batch-3');
      expect(deferredJobIds).to.include('job-mid-batch-4');

      // Verify the correct jobs were executed
      const executedJobIds = mockJobExecutor.execute
        .getCalls()
        .map(call => call.args[1].id);
      expect(executedJobIds).to.include('job-mid-batch-0');
      expect(executedJobIds).to.include('job-mid-batch-1');
      expect(executedJobIds).to.include('job-mid-batch-2');
    });

    it('should process multiple batches with multiple items', async () => {
      // Reset stubs from previous tests
      mockConcurrencyManager.tryReserveSlot.reset();
      mockConcurrencyManager.canRunMore.reset();
      mockJobExecutor.execute.reset();
      mockJobFetcher.fetchAndEnqueue.reset();

      // Create multiple job sets with different priorities and job types
      const jobSetA: JobSet = {
        priority: 1, // Higher priority
        name: 'task-a',
        total: 2,
        items: [
          { id: 'high-prio-1', name: 'task-a', data: { value: 'high1' } },
          { id: 'high-prio-2', name: 'task-a', data: { value: 'high2' } },
        ],
      };

      const jobSetB: JobSet = {
        priority: 2, // Lower priority
        name: 'task-b',
        total: 3,
        items: [
          { id: 'low-prio-1', name: 'task-b', data: { value: 'low1' } },
          { id: 'low-prio-2', name: 'task-b', data: { value: 'low2' } },
          { id: 'low-prio-3', name: 'task-b', data: { value: 'low3' } },
        ],
      };

      mockJobFetcher.fetchAndEnqueue.resolves([jobSetA, jobSetB]);

      // Allow all jobs to run
      mockConcurrencyManager.canRunMore.returns(true);
      mockConcurrencyManager.tryReserveSlot.returns(true);
      mockJobExecutor.execute.resolves();

      schedulerLoop.start(10);
      await clock.tickAsync(20);

      // The exact number of executed jobs may vary based on implementation
      // The important part is that jobs were executed
      expect(mockJobExecutor.execute.called).to.be.true;

      // Verify jobs were executed in the correct order (by priority)
      const executedJobTypes = mockJobExecutor.execute
        .getCalls()
        .map(call => call.args[0]);

      // First two calls should be for task-a (higher priority)
      expect(executedJobTypes[0]).to.equal('task-a');
      expect(executedJobTypes[1]).to.equal('task-a');

      // Next three calls should be for task-b (lower priority)
      expect(executedJobTypes[2]).to.equal('task-b');
      expect(executedJobTypes[3]).to.equal('task-b');
      expect(executedJobTypes[4]).to.equal('task-b');

      // Verify the job IDs match the expected order
      const executedJobIds = mockJobExecutor.execute
        .getCalls()
        .map(call => call.args[1].id);

      expect(executedJobIds.slice(0, 2)).to.have.members([
        'high-prio-1',
        'high-prio-2',
      ]);
      expect(executedJobIds.slice(2, 5)).to.have.members([
        'low-prio-1',
        'low-prio-2',
        'low-prio-3',
      ]);
    });

    it('should handle empty job sets correctly', async () => {
      // Reset stubs from previous tests
      mockJobFetcher.fetchAndEnqueue.reset();
      mockJobExecutor.execute.reset();
      mockMetrics.increment.reset();

      // Return empty job sets
      mockJobFetcher.fetchAndEnqueue.resolves([]);

      schedulerLoop.start(10);
      await clock.tickAsync(20);

      // Should not execute any jobs
      expect(mockJobExecutor.execute.called).to.be.false;

      // Should emit the empty cycle metric
      expect(
        mockMetrics.increment.calledWith('scheduler.queue_cycle.empty', 1, {
          service: loopConfig.service,
        })
      ).to.be.true;
    });
  });

  describe('deferRemainingJobs', () => {
    it('should defer all remaining jobs in a batch', async () => {
      // Create a method to access the private method
      const deferRemainingJobs = (schedulerLoop as any).deferRemainingJobs.bind(
        schedulerLoop
      );

      const jobName = 'test-job';
      const jobs: JobBase[] = [
        { id: 'defer-1', name: jobName, data: {} },
        { id: 'defer-2', name: jobName, data: {} },
      ];

      mockJobExecutor.defer.resolves();

      await deferRemainingJobs(jobName, jobs);

      expect(mockJobExecutor.defer.calledTwice).to.be.true;
      expect(mockJobExecutor.defer.firstCall.args[0]).to.equal(jobName);
      expect(mockJobExecutor.defer.firstCall.args[1]).to.equal(jobs[0]);
      expect(mockJobExecutor.defer.secondCall.args[0]).to.equal(jobName);
      expect(mockJobExecutor.defer.secondCall.args[1]).to.equal(jobs[1]);
    });

    it('should handle errors when deferring jobs', async () => {
      const deferRemainingJobs = (schedulerLoop as any).deferRemainingJobs.bind(
        schedulerLoop
      );

      const jobName = 'test-job';
      const jobs: JobBase[] = [
        { id: 'defer-error-1', name: jobName, data: {} },
        { id: 'defer-error-2', name: jobName, data: {} },
      ];

      mockJobExecutor.defer.onFirstCall().resolves(false);
      mockJobExecutor.defer.onSecondCall().resolves(true);

      await deferRemainingJobs(jobName, jobs);

      // Should still try to defer second job
      expect(mockJobExecutor.defer.calledTwice).to.be.true;

      expect(mockLogger.info.calledTwice).to.be.true;
      expect(mockLogger.info.firstCall.args[0]).to.have.property(
        'jobId',
        'defer-error-1'
      );
      expect(mockLogger.info.secondCall.args[0]).to.have.property(
        'jobId',
        'defer-error-2'
      );
    });
  });

  describe('emitMemoryMetrics', () => {
    it('should emit metrics about concurrency and cycle state', () => {
      // Set up some state
      mockConcurrencyManager.getActiveCount.returns(2);
      mockConcurrencyManager.getRunningJobsCount.returns(3);
      mockConcurrencyManager.canRunMore.returns(true);

      // Set internal state flags
      (schedulerLoop as any).cycleInProgress = true;
      (schedulerLoop as any).fetchInProgress = false;

      // Call the private method
      const emitMemoryMetrics = (schedulerLoop as any).emitMemoryMetrics.bind(
        schedulerLoop
      );
      emitMemoryMetrics();

      // Verify metrics were emitted
      expect(mockMetrics.gauge.callCount).to.equal(4);

      // Check specific metrics
      expect(
        mockMetrics.gauge.calledWith('scheduler.memory.active_jobs', 2, {
          service: loopConfig.service,
        })
      ).to.be.true;

      expect(
        mockMetrics.gauge.calledWith('scheduler.memory.running_jobs', 3, {
          service: loopConfig.service,
        })
      ).to.be.true;

      expect(
        mockMetrics.gauge.calledWith('scheduler.memory.cycle_in_progress', 1, {
          service: loopConfig.service,
        })
      ).to.be.true;

      expect(
        mockMetrics.gauge.calledWith('scheduler.memory.fetch_in_progress', 0, {
          service: loopConfig.service,
        })
      ).to.be.true;

      // Verify debug logging
      expect(mockLogger.debug.calledOnce).to.be.true;
      expect((mockLogger.debug.firstCall.args[0] as any).memory_state).to.exist;
    });
  });

  describe('fetch in progress handling', () => {
    it('should skip fetch phase if fetch is already in progress', async () => {
      // Set internal state to indicate fetch in progress
      (schedulerLoop as any).fetchInProgress = true;

      schedulerLoop.start(10);
      await clock.tickAsync(20);

      // Should not call fetchAndEnqueue
      expect(mockJobFetcher.fetchAndEnqueue.called).to.be.false;

      // Should emit the appropriate metric
      expect(
        mockMetrics.increment.calledWith(
          'scheduler.cycle.skipped.fetch_in_progress',
          1,
          { service: loopConfig.service }
        )
      ).to.be.true;
    });

    it('should reset fetchInProgress flag after successful fetch', async () => {
      mockJobFetcher.fetchAndEnqueue.resolves([]);

      schedulerLoop.start(10);
      await clock.tickAsync(20);

      // Flag should be set to true during fetch and reset after
      expect((schedulerLoop as any).fetchInProgress).to.be.false;
    });

    it('should reset fetchInProgress flag after failed fetch', async () => {
      mockJobFetcher.fetchAndEnqueue.rejects(new Error('Fetch error'));

      schedulerLoop.start(10);
      try {
        await clock.tickAsync(20);
      } catch (e) {
        // Expected error
      }

      // Flag should be reset even after error
      expect((schedulerLoop as any).fetchInProgress).to.be.false;
    });

    it('should not fetch jobs when concurrency limit is reached', async () => {
      // Reset stubs
      mockJobFetcher.fetchAndEnqueue.reset();
      mockConcurrencyManager.canRunMore.reset();

      // Set up concurrency manager to NOT allow more jobs
      mockConcurrencyManager.canRunMore.returns(false);

      // Run the scheduler
      schedulerLoop.start(10);
      await clock.tickAsync(20);

      // Verify that canRunMore was called to check concurrency
      expect(mockConcurrencyManager.canRunMore.called).to.be.true;

      // Verify that fetchAndEnqueue was NOT called when concurrency limit reached
      expect(mockJobFetcher.fetchAndEnqueue.called).to.be.false;
    });
  });

  describe('error handling', () => {
    it('should handle missing task handlers', async () => {
      // Reset stubs
      mockJobFetcher.fetchAndEnqueue.reset();
      mockJobExecutor.execute.reset();
      mockMetrics.increment.reset();

      // Create a job set with a task that doesn't have a handler
      const jobSet: JobSet[] = [
        {
          priority: 1,
          name: 'non-existent-task', // This task doesn't exist in mockTasks
          total: 1,
          items: [
            { id: 'missing-handler-job', name: 'non-existent-task', data: {} },
          ],
        },
      ];

      mockJobFetcher.fetchAndEnqueue.resolves(jobSet);
      mockConcurrencyManager.canRunMore.returns(true);
      mockConcurrencyManager.tryReserveSlot.returns(true);

      schedulerLoop.start(10);
      await clock.tickAsync(20);

      // Should not attempt to execute the job
      expect(mockJobExecutor.execute.called).to.be.false;

      // Should log an error about the missing task handler
      expect(mockLogger.error.called).to.be.true;
      expect(
        mockLogger.error.calledWith(
          sinon.match({ jobName: 'non-existent-task' }),
          sinon.match.string
        )
      ).to.be.true;

      // Should emit a metric about the missing task
      expect(
        mockMetrics.increment.calledWith('scheduler.task.missing', 1, {
          job_type: 'non-existent-task',
          service: loopConfig.service,
        })
      ).to.be.true;
    });
  });

  describe('multiple cycles and job processing', () => {
    it('should process jobs across multiple cycles', async () => {
      // Reset stubs
      mockJobFetcher.fetchAndEnqueue.reset();
      mockJobExecutor.execute.reset();
      mockConcurrencyManager.tryReserveSlot.reset();

      // First cycle: 2 jobs
      const firstCycleJobs: JobSet[] = [
        {
          priority: 1,
          name: 'task-a',
          total: 2,
          items: [
            { id: 'cycle1-job1', name: 'task-a', data: {} },
            { id: 'cycle1-job2', name: 'task-a', data: {} },
          ],
        },
      ];

      // Second cycle: 3 jobs
      const secondCycleJobs: JobSet[] = [
        {
          priority: 1,
          name: 'task-b',
          total: 3,
          items: [
            { id: 'cycle2-job1', name: 'task-b', data: {} },
            { id: 'cycle2-job2', name: 'task-b', data: {} },
            { id: 'cycle2-job3', name: 'task-b', data: {} },
          ],
        },
      ];

      // Setup fetch to return different jobs on each call
      mockJobFetcher.fetchAndEnqueue.onFirstCall().resolves(firstCycleJobs);
      mockJobFetcher.fetchAndEnqueue.onSecondCall().resolves(secondCycleJobs);

      // Allow all jobs to run
      mockConcurrencyManager.canRunMore.returns(true);
      mockConcurrencyManager.tryReserveSlot.returns(true);
      mockJobExecutor.execute.resolves();

      // Start the scheduler
      schedulerLoop.start(100); // Use a longer interval to control timing

      // Let the first cycle run
      await clock.tickAsync(100);

      // Verify first cycle jobs were processed
      expect(mockJobFetcher.fetchAndEnqueue.calledOnce).to.be.true;
      expect(mockJobExecutor.execute.callCount).to.equal(2);

      // Let the second cycle run
      await clock.tickAsync(100);

      // Verify second cycle jobs were processed
      expect(mockJobFetcher.fetchAndEnqueue.calledTwice).to.be.true;
      expect(mockJobExecutor.execute.callCount).to.equal(5); // 2 from first + 3 from second

      // Verify the job IDs from both cycles
      const executedJobIds = mockJobExecutor.execute
        .getCalls()
        .map(call => call.args[1].id);

      // First cycle jobs
      expect(executedJobIds).to.include('cycle1-job1');
      expect(executedJobIds).to.include('cycle1-job2');

      // Second cycle jobs
      expect(executedJobIds).to.include('cycle2-job1');
      expect(executedJobIds).to.include('cycle2-job2');
      expect(executedJobIds).to.include('cycle2-job3');
    });

    it('should defer remaining batches when first batch reaches concurrency limit mid-cycle', async () => {
      // Reset stubs
      mockJobFetcher.fetchAndEnqueue.reset();
      mockJobExecutor.execute.reset();
      mockJobExecutor.defer.reset();
      mockConcurrencyManager.tryReserveSlot.reset();
      mockConcurrencyManager.canRunMore.reset();

      // Create multiple batches with different priorities
      const batch1: JobSet = {
        priority: 1, // Higher priority (processed first)
        name: 'task-a',
        total: 3,
        items: [
          { id: 'high-1', name: 'task-a', data: {} },
          { id: 'high-2', name: 'task-a', data: {} },
          { id: 'high-3', name: 'task-a', data: {} },
        ],
      };

      const batch2: JobSet = {
        priority: 2, // Medium priority (processed second)
        name: 'task-b',
        total: 2,
        items: [
          { id: 'medium-1', name: 'task-b', data: {} },
          { id: 'medium-2', name: 'task-b', data: {} },
        ],
      };

      const batch3: JobSet = {
        priority: 3, // Lower priority (processed last)
        name: 'task-c',
        total: 2,
        items: [
          { id: 'low-1', name: 'task-c', data: {} },
          { id: 'low-2', name: 'task-c', data: {} },
        ],
      };

      // Return all three batches in a single cycle
      mockJobFetcher.fetchAndEnqueue.resolves([batch1, batch2, batch3]);

      // Allow concurrency check to pass initially
      mockConcurrencyManager.canRunMore.returns(true);

      // Allow only the first 2 jobs to run, then hit concurrency limit
      mockConcurrencyManager.tryReserveSlot
        .onCall(0)
        .returns(true)
        .onCall(1)
        .returns(true)
        .onCall(2)
        .returns(false); // Concurrency limit reached after 2 jobs

      // Simulate job execution
      mockJobExecutor.execute.resolves();
      mockJobExecutor.defer.resolves();

      // Run the scheduler
      schedulerLoop.start(10);
      await clock.tickAsync(10);

      // Verify that only the first 2 jobs from the high priority batch were executed
      expect(mockJobExecutor.execute.callCount).to.equal(2);

      // Verify the executed job IDs (should be from high priority batch)
      const executedJobIds = mockJobExecutor.execute
        .getCalls()
        .map(call => call.args[1].id);
      expect(executedJobIds).to.include('high-1');
      expect(executedJobIds).to.include('high-2');

      // Verify that the remaining job from high priority batch was deferred
      expect(mockJobExecutor.defer.called).to.be.true;

      // Get all deferred job IDs
      const deferredJobIds = mockJobExecutor.defer
        .getCalls()
        .map(call => call.args[1].id);

      // Verify that the remaining job from high priority batch was deferred
      expect(deferredJobIds).to.include('high-3');

      // Verify that jobs from the medium priority batch were deferred (entire batch)
      expect(deferredJobIds).to.include('medium-1');
      expect(deferredJobIds).to.include('medium-2');

      // Verify that jobs from the low priority batch were deferred (entire batch)
      expect(deferredJobIds).to.include('low-1');
      expect(deferredJobIds).to.include('low-2');

      // Verify that all remaining batches were deferred
      const allProcessedIds = [...executedJobIds, ...deferredJobIds];
      expect(allProcessedIds.length).to.equal(7); // All 7 jobs should be either executed or deferred
    });

    it('should defer remaining batches when scheduler starts exiting mid-cycle', async () => {
      // Reset stubs
      mockJobFetcher.fetchAndEnqueue.reset();
      mockJobExecutor.execute.reset();
      mockJobExecutor.defer.reset();
      mockConcurrencyManager.tryReserveSlot.reset();
      mockConcurrencyManager.canRunMore.reset();
      mockLifecycleManager.getIsExiting.reset();

      // Create multiple batches with different priorities
      const highPriorityBatch: JobSet = {
        priority: 1,
        name: 'task-a',
        total: 2,
        items: [
          { id: 'exit-high-1', name: 'task-a', data: {} },
          { id: 'exit-high-2', name: 'task-a', data: {} },
        ],
      };

      const lowPriorityBatch: JobSet = {
        priority: 2,
        name: 'task-b',
        total: 3,
        items: [
          { id: 'exit-low-1', name: 'task-b', data: {} },
          { id: 'exit-low-2', name: 'task-b', data: {} },
          { id: 'exit-low-3', name: 'task-b', data: {} },
        ],
      };

      // Return both batches in a single cycle
      mockJobFetcher.fetchAndEnqueue.resolves([
        highPriorityBatch,
        lowPriorityBatch,
      ]);

      // Allow concurrency check to pass
      mockConcurrencyManager.canRunMore.returns(true);
      mockConcurrencyManager.tryReserveSlot.returns(true);

      // Set up lifecycle manager to indicate exiting after processing some jobs
      // First allow the cycle to start normally, process 1 job and then exit
      mockLifecycleManager.getIsExiting
        .onCall(0)
        .returns(false)
        .onCall(1)
        .returns(false)
        .onCall(2)
        .returns(false)
        .onCall(3)
        .returns(true)
        .onCall(4)
        .returns(true)
        .onCall(5)
        .returns(true);

      // Simulate job execution
      mockJobExecutor.execute.resolves();
      mockJobExecutor.defer.resolves();

      // Run the scheduler
      schedulerLoop.start(10);
      await clock.tickAsync(15);

      // The number of executed jobs may vary based on implementation details
      // The important part is that some jobs were executed
      expect(mockJobExecutor.execute.called).to.be.true;

      // Get the executed job IDs
      const executedJobIds = mockJobExecutor.execute
        .getCalls()
        .map(call => call.args[1].id);

      // Verify that jobs were deferred
      expect(mockJobExecutor.defer.called).to.be.true;
      // Verify that the first job ran
      expect(executedJobIds.length).to.eql(1);
      expect(executedJobIds).to.have.members(['exit-high-1']);

      // Get all deferred job IDs
      const deferredJobIds = mockJobExecutor.defer
        .getCalls()
        .map(call => call.args[1].id);
      expect(deferredJobIds.length).to.eql(4);
      expect(deferredJobIds).to.have.members([
        'exit-high-2',
        'exit-low-1',
        'exit-low-2',
        'exit-low-3',
      ]);

      // Verify that all jobs were either executed or deferred
      const allProcessedIds = [...executedJobIds, ...deferredJobIds];
      expect(allProcessedIds.length).to.equal(5); // All 5 jobs should be either executed or deferred
    });

    it('should handle job execution errors and continue processing', async () => {
      // Reset stubs
      mockJobFetcher.fetchAndEnqueue.reset();
      mockJobExecutor.execute.reset();
      mockConcurrencyManager.tryReserveSlot.reset();
      mockConcurrencyManager.startJob.reset();
      mockConcurrencyManager.endJob.reset();

      // Create a batch with multiple jobs
      const jobSet: JobSet[] = [
        {
          priority: 1,
          name: 'task-a',
          total: 3,
          items: [
            { id: 'error-job1', name: 'task-a', data: { shouldFail: false } },
            { id: 'error-job2', name: 'task-a', data: { shouldFail: true } },
            { id: 'error-job3', name: 'task-a', data: { shouldFail: false } },
          ],
        },
      ];

      mockJobFetcher.fetchAndEnqueue.resolves(jobSet);

      // Allow all jobs to run
      mockConcurrencyManager.canRunMore.returns(true);
      mockConcurrencyManager.tryReserveSlot.returns(true);

      // Make the second job fail, but others succeed
      mockJobExecutor.execute.callsFake(async (_jobName, job, _task) => {
        if (job.data.shouldFail) {
          throw new Error(`Job ${job.id} failed intentionally`);
        }
        // Return void as expected by the function signature
      });

      // Spy on the finally handler that should release the concurrency slot
      const endJobSpy = mockConcurrencyManager.endJob;

      schedulerLoop.start(100);
      await clock.tickAsync(100);

      // All jobs should be attempted
      expect(mockJobExecutor.execute.callCount).to.equal(3);

      // All jobs should have slots released, even the failing one
      expect(endJobSpy.callCount).to.equal(3);
      expect(endJobSpy.calledWith('error-job1')).to.be.true;
      expect(endJobSpy.calledWith('error-job2')).to.be.true;
      expect(endJobSpy.calledWith('error-job3')).to.be.true;

      // Verify startJob was called for all jobs
      expect(mockConcurrencyManager.startJob.callCount).to.equal(3);

      // The scheduler should continue running
      expect(schedulerLoop.isRunning()).to.be.true;
    });

    it('should handle concurrency changes between cycles', async () => {
      // Reset stubs
      mockJobFetcher.fetchAndEnqueue.reset();
      mockJobExecutor.execute.reset();
      mockConcurrencyManager.canRunMore.reset();
      mockConcurrencyManager.tryReserveSlot.reset();

      // Create identical job sets for both cycles
      const jobSet: JobSet[] = [
        {
          priority: 1,
          name: 'task-a',
          total: 3,
          items: [
            { id: 'concurrency-job1', name: 'task-a', data: {} },
            { id: 'concurrency-job2', name: 'task-a', data: {} },
            { id: 'concurrency-job3', name: 'task-a', data: {} },
          ],
        },
      ];

      // Return the same jobs for both cycles
      mockJobFetcher.fetchAndEnqueue.resolves(jobSet);

      // First cycle: Allow only 1 job to run
      mockConcurrencyManager.canRunMore.onFirstCall().returns(true);
      mockConcurrencyManager.tryReserveSlot.onFirstCall().returns(true);
      mockConcurrencyManager.tryReserveSlot.onSecondCall().returns(false);

      // Second cycle: Allow all jobs to run
      mockConcurrencyManager.canRunMore.onSecondCall().returns(true);
      mockConcurrencyManager.tryReserveSlot.onThirdCall().returns(true);
      mockConcurrencyManager.tryReserveSlot.onCall(3).returns(true);
      mockConcurrencyManager.tryReserveSlot.onCall(4).returns(true);

      mockJobExecutor.execute.resolves();
      mockJobExecutor.defer.resolves();

      // Start the scheduler
      schedulerLoop.start(100);

      // Let the first cycle run
      await clock.tickAsync(100);

      // Verify first cycle processed 1 job and deferred 2
      expect(mockJobExecutor.execute.callCount).to.equal(1);
      expect(mockJobExecutor.defer.callCount).to.equal(2);

      // Reset defer for second cycle
      mockJobExecutor.defer.reset();

      // Let the second cycle run
      await clock.tickAsync(100);

      // Verify second cycle processed jobs
      // The exact count may vary based on implementation
      expect(mockJobExecutor.execute.callCount).to.be.at.least(1);

      // Reset defer expectations for second cycle
      // We don't make assumptions about whether defer was called in the second cycle
    });
  });
});
