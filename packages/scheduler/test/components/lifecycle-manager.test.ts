import sinon from 'sinon';
import { expect } from '../setup';
import { LifecycleManager } from '../../src/components/lifecycle-manager';
import type { GenericLogger } from '../../src';

describe('LifecycleManager', () => {
  let sandbox: sinon.SinonSandbox;
  let mockLogger: sinon.SinonStubbedInstance<GenericLogger>;
  let lifecycleManager: LifecycleManager;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    mockLogger = {
      info: sandbox.stub(),
      error: sandbox.stub(),
      warn: sandbox.stub(),
      debug: sandbox.stub(),
    };
    lifecycleManager = new LifecycleManager(mockLogger);
  });

  afterEach(() => {
    sandbox.restore();
  });

  it('should initialize with default states (not initialized, not paused, not exiting)', () => {
    expect(lifecycleManager.getIsInitialized()).to.be.false;
    expect(lifecycleManager.getIsPaused()).to.be.false;
    expect(lifecycleManager.getIsExiting()).to.be.false;
  });

  describe('setInitialized', () => {
    it('should set the initialized state', () => {
      lifecycleManager.setInitialized(true);
      expect(lifecycleManager.getIsInitialized()).to.be.true;
      lifecycleManager.setInitialized(false);
      expect(lifecycleManager.getIsInitialized()).to.be.false;
    });
  });

  describe('pause', () => {
    it('should set the paused state to true and log info', () => {
      lifecycleManager.pause();
      expect(lifecycleManager.getIsPaused()).to.be.true;
    });
  });

  describe('resume', () => {
    it('should set the paused state to false and log info if previously paused', () => {
      lifecycleManager.pause(); // Ensure it's paused first
      mockLogger.info.resetHistory();
      lifecycleManager.resume();
      expect(lifecycleManager.getIsPaused()).to.be.false;
    });
  });

  describe('signalExit', () => {
    it('should set the exiting state to true, paused state to true, and log info', () => {
      lifecycleManager.signalExit();
      expect(lifecycleManager.getIsExiting()).to.be.true;
      expect(lifecycleManager.getIsPaused()).to.be.true; // Should also pause on exit
    });

    it('should only log once if called multiple times', () => {
      lifecycleManager.signalExit(); // First call
      mockLogger.info.resetHistory();
      lifecycleManager.signalExit(); // Second call
      expect(lifecycleManager.getIsExiting()).to.be.true;
      expect(lifecycleManager.getIsPaused()).to.be.true;
      expect(mockLogger.info.called).to.be.false; // No new log
    });
  });

  describe('reset', () => {
    it('should reset all state flags to their initial values and log info', () => {
      // Set some non-default states
      lifecycleManager.setInitialized(true);
      lifecycleManager.signalExit(); // Sets exiting and paused to true
      mockLogger.info.resetHistory();

      lifecycleManager.reset();

      expect(lifecycleManager.getIsInitialized()).to.be.false;
      expect(lifecycleManager.getIsPaused()).to.be.false;
      expect(lifecycleManager.getIsExiting()).to.be.false;
    });
  });
});
