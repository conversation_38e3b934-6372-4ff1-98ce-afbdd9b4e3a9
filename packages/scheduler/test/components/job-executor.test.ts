import sinon from 'sinon';
import { EventEmitter } from 'events';
import type TypedEmitter from 'typed-emitter';
import { expect } from '../setup';
import { JobExecutor } from '../../src/components/job-executor';
import type {
  Events,
  JobBase,
  JobContext,
  DatabaseAdapter,
  GenericLogger,
  MetricsClient,
} from '../../src';
import { JobExecutionError } from '../../src/errors/scheduler-errors';

describe('JobExecutor', () => {
  let sandbox: sinon.SinonSandbox;
  // Define more specific types for adapter stubs
  let mockAdapter: {
    fetchAndEnqueueJobs: sinon.SinonStub;
    resetJob: sinon.SinonStub;
    updateStartTask: sinon.SinonStub<
      [jobId: string, namespace?: string | undefined],
      Promise<void>
    >;
    updateFinishTask: sinon.SinonStub<
      [jobId: string, namespace?: string | undefined],
      Promise<void>
    >;
    updateFailure: sinon.SinonStub<
      [
        jobId: string,
        backOffMs: number,
        maxRestartsOnFailure: number,
        jobsSafeToRestart: string[],
        namespace?: string | undefined
      ],
      Promise<void>
    >;
    getPendingJobs: sinon.SinonStub;
    getBlockedJobs: sinon.SinonStub;
    getLaggedJobs: sinon.SinonStub;
    disconnect: sinon.SinonStub;
  };
  let mockLogger: sinon.SinonStubbedInstance<GenericLogger>;
  let mockMetrics: sinon.SinonStubbedInstance<MetricsClient>;
  let mockEvents: TypedEmitter<Events>;
  let jobExecutor: JobExecutor;
  let mockTasks: {
    [name: string]: sinon.SinonStub<
      [data: any, context?: JobContext],
      Promise<any>
    >;
  }; // Stub the task functions
  let clock: sinon.SinonFakeTimers;

  const jobExecutorConfig = {
    backOffMs: 1000,
    maxRestartsOnFailure: 3,
    jobsSafeToRestart: ['safe-job'],
    namespace: 'test-ns',
    service: 'test-executor-service',
  };

  const sampleJob: JobBase = {
    id: 'job-123',
    name: 'test-job',
    data: { key: 'value' },
    failures: 0,
  };

  const sampleSafeJob: JobBase = {
    id: 'job-456',
    name: 'safe-job',
    data: { safe: true },
    failures: 0,
  };

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    clock = sandbox.useFakeTimers();

    // Create stubs with specific signatures where needed
    mockAdapter = {
      fetchAndEnqueueJobs: sandbox.stub(),
      resetJob: sandbox.stub(),
      updateStartTask: sandbox
        .stub<[jobId: string, namespace?: string | undefined], Promise<void>>()
        .resolves(),
      updateFinishTask: sandbox
        .stub<[jobId: string, namespace?: string | undefined], Promise<void>>()
        .resolves(),
      updateFailure: sandbox
        .stub<
          [
            jobId: string,
            backOffMs: number,
            maxRestartsOnFailure: number,
            jobsSafeToRestart: string[],
            namespace?: string | undefined
          ],
          Promise<void>
        >()
        .resolves(),
      getPendingJobs: sandbox.stub(),
      getBlockedJobs: sandbox.stub(),
      getLaggedJobs: sandbox.stub(),
      disconnect: sandbox.stub(),
    };
    mockLogger = {
      info: sandbox.stub(),
      error: sandbox.stub(),
      warn: sandbox.stub(),
      debug: sandbox.stub(),
      child: sandbox.stub().returnsThis(),
    } as unknown as sinon.SinonStubbedInstance<GenericLogger>;
    mockMetrics = {
      increment: sandbox.stub(),
      timing: sandbox.stub(),
      gauge: sandbox.stub(),
    };
    mockEvents = new EventEmitter() as TypedEmitter<Events>;
    sandbox.spy(mockEvents, 'emit');

    // Ensure mockTasks contains stubs
    mockTasks = {
      'test-job': sandbox
        .stub<[data: any, context?: JobContext], Promise<any>>()
        .resolves({ result: 'success' }),
      'safe-job': sandbox
        .stub<[data: any, context?: JobContext], Promise<any>>()
        .resolves({ result: 'safe success' }),
    };

    jobExecutor = new JobExecutor(
      mockAdapter as DatabaseAdapter, // Cast needed because not all methods are fully stubbed
      mockLogger,
      mockMetrics,
      mockEvents,
      jobExecutorConfig
    );
  });

  afterEach(() => {
    clock.restore();
    sandbox.restore();
  });

  describe('execute', () => {
    // Renamed from executeJob
    it('should execute a defined task successfully', async () => {
      const taskToExecute = mockTasks[sampleJob.name];

      // Call execute with correct arguments
      await jobExecutor.execute(sampleJob.name, sampleJob, taskToExecute);

      // Assertions
      expect(
        mockAdapter.updateStartTask.calledOnceWith(
          sampleJob.id,
          jobExecutorConfig.namespace
        )
      ).to.be.true;
      // Assert on the stub directly
      expect(taskToExecute.calledOnceWith(sampleJob.data, { job: sampleJob }))
        .to.be.true;
      expect(
        mockAdapter.updateFinishTask.calledOnceWith(
          sampleJob.id,
          jobExecutorConfig.namespace
        )
      ).to.be.true;
      expect(mockMetrics.timing.calledOnce).to.be.true;
      expect(mockMetrics.timing.firstCall.args[0]).to.equal(
        'scheduler.job.duration'
      );
      // Updated metric tags based on source
      expect(mockMetrics.timing.firstCall.args[2]).to.deep.include({
        job_type: sampleJob.name,
        status: 'success',
      });
      expect(
        mockMetrics.increment.calledWith('scheduler.job.completed', 1, {
          job_type: sampleJob.name,
          status: 'success',
          service: jobExecutorConfig.service, // Added service tag
        })
      ).to.be.true;
      expect(
        (mockEvents.emit as sinon.SinonSpy).calledWith(
          'duration',
          sampleJob,
          sinon.match.number,
          true
        )
      ).to.be.true;
      expect(
        mockLogger.debug.calledWith(
          sinon.match.object,
          'JobExecutor: Job completed successfully'
        )
      ).to.be.true; // Check debug log
    });

    it('should handle task execution failure and call updateFailure', async () => {
      const failureError = new Error('Task failed!');
      const taskToExecute = mockTasks[sampleJob.name];

      taskToExecute.rejects(failureError); // Make the stub reject

      // Execute expects no error to be thrown upwards, it handles internally
      await jobExecutor.execute(sampleJob.name, sampleJob, taskToExecute);
      // Assertions
      expect(mockAdapter.updateStartTask.calledOnce).to.be.true;
      expect(taskToExecute.calledOnce).to.be.true; // Task was called
      expect(mockAdapter.updateFinishTask.called).to.be.false; // Should not finish on failure
      expect(
        mockAdapter.updateFailure.calledOnceWith(
          sampleJob.id,
          jobExecutorConfig.backOffMs,
          jobExecutorConfig.maxRestartsOnFailure,
          jobExecutorConfig.jobsSafeToRestart,
          jobExecutorConfig.namespace
        )
      ).to.be.true;
      expect(mockMetrics.timing.calledOnce).to.be.true; // Duration metric still recorded
      // Updated metric tags based on source
      expect(mockMetrics.timing.firstCall.args[2]).to.deep.include({
        job_type: sampleJob.name,
        status: 'failure',
      });
      expect(
        mockMetrics.increment.calledWith('scheduler.job.completed', 1, {
          job_type: sampleJob.name,
          status: 'failure',
          service: jobExecutorConfig.service, // Added service tag
        })
      ).to.be.true;
      expect(
        (mockEvents.emit as sinon.SinonSpy).calledWith(
          'duration',
          sampleJob,
          sinon.match.number,
          false
        )
      ).to.be.true;
    });

    it('should handle failure in updateStartTask', async () => {
      const startTaskError = new Error('Failed to update start task');
      mockAdapter.updateStartTask.rejects(startTaskError);
      const taskToExecute = mockTasks[sampleJob.name];

      // Execute expects no error to be thrown upwards
      await jobExecutor.execute(sampleJob.name, sampleJob, taskToExecute);

      // Assertions
      expect(mockAdapter.updateStartTask.calledOnce).to.be.true; // It was called
      expect(taskToExecute.called).to.be.false; // Task should not run
      expect(mockAdapter.updateFinishTask.called).to.be.false;
      expect(mockAdapter.updateFailure.called).to.be.true; // Should be called on start task failure
      expect(mockLogger.error.calledOnce).to.be.true; // Error is logged internally
    });

    it('should handle failure in updateFinishTask', async () => {
      const finishTaskError = new Error('Failed to update finish task');
      mockAdapter.updateFinishTask.rejects(finishTaskError);
      const taskToExecute = mockTasks[sampleJob.name];

      // Execute expects no error to be thrown upwards
      await jobExecutor.execute(sampleJob.name, sampleJob, taskToExecute);

      // Assertions
      expect(mockAdapter.updateStartTask.calledOnce).to.be.true;
      expect(taskToExecute.calledOnce).to.be.true; // Task did run
      expect(mockAdapter.updateFinishTask.calledOnce).to.be.true; // finishTask was called
      expect(mockAdapter.updateFailure.called).to.be.true; // Should be called on finish task failure
      expect(mockLogger.error.calledOnce).to.be.true; // Error is logged internally
      // Duration/success metrics might still be emitted depending on exact placement
      expect(mockMetrics.timing.calledOnce).to.be.true;
      expect((mockEvents.emit as sinon.SinonSpy).calledWith('duration')).to.be
        .true;
    });

    it('should handle failure in updateFailure', async () => {
      const taskFailureError = new Error('Task failed!');
      const updateFailureError = new Error('Failed to update failure');
      const taskToExecute = mockTasks[sampleJob.name];

      taskToExecute.rejects(taskFailureError);
      mockAdapter.updateFailure.rejects(updateFailureError);

      // Execute expects no error to be thrown upwards
      await jobExecutor.execute(sampleJob.name, sampleJob, taskToExecute);

      // Assertions
      expect(mockAdapter.updateStartTask.calledOnce).to.be.true;
      expect(taskToExecute.calledOnce).to.be.true; // Task was called
      expect(mockAdapter.updateFinishTask.called).to.be.false;
      expect(mockAdapter.updateFailure.calledOnce).to.be.true; // It was called
      expect(mockLogger.error.callCount).to.equal(2); // Once for task fail, once for updateFailure fail
      // Assert error properties directly, using type assertion
      // Check the first error log (task failure)
      const firstLogError = mockLogger.error.firstCall.args[0] as any;
      expect(firstLogError.err).to.be.instanceOf(Error);
      expect(firstLogError.jobId).to.equal(sampleJob.id);
      expect(firstLogError.jobName).to.equal(sampleJob.name);

      expect(mockLogger.error.firstCall.args[1]).to.include(
        `JobExecutor: Job execution failed: ${sampleJob.name}`
      );

      expect(mockLogger.error.secondCall.args[0]).to.deep.include({
        err: updateFailureError,
        jobId: sampleJob.id,
        jobName: sampleJob.name,
      });
      expect(mockLogger.error.secondCall.args[1]).to.include(
        'JobExecutor: Failed to update job failure status'
      );
      expect(
        mockMetrics.increment.calledWith(
          'scheduler.job.update_failure_error',
          1
        )
      ).to.be.true;
    });

    it('should correctly identify safe jobs for updateFailure', async () => {
      const failureError = new Error('Safe Task failed!');
      const taskToExecute = mockTasks[sampleSafeJob.name];

      taskToExecute.rejects(failureError);

      // Execute expects no error to be thrown upwards
      await jobExecutor.execute(
        sampleSafeJob.name,
        sampleSafeJob,
        taskToExecute
      );

      // Assertions
      expect(
        mockAdapter.updateFailure.calledOnceWith(
          sampleSafeJob.id,
          jobExecutorConfig.backOffMs,
          jobExecutorConfig.maxRestartsOnFailure,
          jobExecutorConfig.jobsSafeToRestart, // Should pass the safe list
          jobExecutorConfig.namespace
        )
      ).to.be.true;
      expect(mockLogger.error.calledOnce).to.be.true; // Error logged for task failure
    });

    it('should emit execution lag metric when nextRunAt is set', async () => {
      const now = Date.now();
      const pastTime = now - 5000; // 5 seconds ago
      const jobWithLag: JobBase = {
        ...sampleJob,
        id: 'job-lag-test',
        nextRunAt: new Date(pastTime),
      };
      const taskToExecute = mockTasks[jobWithLag.name];

      await jobExecutor.execute(jobWithLag.name, jobWithLag, taskToExecute);

      // Assertions
      expect(
        mockMetrics.timing.calledWith('scheduler.job.execution_lag_seconds')
      ).to.be.true;
      const lagCall = mockMetrics.timing
        .getCalls()
        .find(call => call.args[0] === 'scheduler.job.execution_lag_seconds');
      expect(lagCall).to.exist;
      expect(lagCall?.args[1]).to.equal(5);
      expect(lagCall?.args[2]).to.deep.include({
        job_type: jobWithLag.name,
        service: jobExecutorConfig.service,
      });

      // Ensure the duration metric is still called afterwards
      expect(mockMetrics.timing.calledWith('scheduler.job.duration')).to.be
        .true;
    });
  });

  describe('defer', () => {
    it('should call adapter.resetJob and emit metrics on successful deferral', async () => {
      const jobToDefer = sampleJob;
      mockAdapter.resetJob.resolves(); // Ensure resetJob resolves successfully

      await jobExecutor.defer(jobToDefer.name, jobToDefer);

      // Assertions
      expect(mockAdapter.resetJob.calledOnce).to.be.true;
      // Check that resetJob was called with the job and a valid ISO string timestamp
      expect(mockAdapter.resetJob.firstCall.args[0]).to.equal(jobToDefer);
      expect(mockAdapter.resetJob.firstCall.args[1]).to.match(
        /\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/
      ); // ISO String format
      expect(
        mockMetrics.increment.calledOnceWith('scheduler.job.deferred', 1, {
          job_type: jobToDefer.name,
          service: jobExecutorConfig.service,
        })
      ).to.be.true;
      expect(
        mockLogger.debug.calledOnceWith(
          { jobId: jobToDefer.id },
          'JobExecutor: Job deferred to next cycle'
        )
      ).to.be.true;
    });

    it('should return false, log error, and emit error metric if adapter.resetJob fails', async () => {
      const jobToDefer = sampleJob;
      const resetError = new Error('Database unavailable');
      (
        mockAdapter.resetJob as sinon.SinonStub<
          [JobBase, string],
          Promise<void>
        >
      ).rejects(resetError); // Make resetJob reject

      const result = await jobExecutor.defer(jobToDefer.name, jobToDefer);

      // Assertions
      expect(result).to.be.false; // Should return false, not throw

      expect(mockAdapter.resetJob.calledOnce).to.be.true; // It was called
      expect(mockMetrics.increment.calledWith('scheduler.job.deferred')).to.be
        .false; // Success metric should not be emitted
      expect(
        mockMetrics.increment.calledWith('scheduler.job.deferred.error', 1, {
          job_type: jobToDefer.name,
          service: jobExecutorConfig.service,
        })
      ).to.be.true; // Error metric should be emitted

      expect(mockLogger.error.calledOnce).to.be.true;
      // Check the logged error object structure
      // Use type assertion to satisfy TypeScript
      const logArg = mockLogger.error.firstCall.args[0] as {
        err: JobExecutionError; // Changed from 'error'
        jobId: string;
      };
      expect(logArg.err).to.be.instanceOf(Error);
      expect(logArg.err.message).to.equal('Database unavailable');
      expect(logArg.jobId).to.equal(jobToDefer.id);

      expect(mockLogger.error.firstCall.args[1]).to.equal(
        'JobExecutor: Failed to defer job'
      );
    });

    it('should successfully defer multiple jobs', async () => {
      const job1 = { ...sampleJob, id: 'job-1', name: 'job-1' };
      const job2 = { ...sampleJob, id: 'job-2', name: 'job-2' };
      const job3 = { ...sampleJob, id: 'job-3', name: 'job-3' };

      mockAdapter.resetJob.resolves();

      const results = await Promise.all([
        jobExecutor.defer(job1.name, job1),
        jobExecutor.defer(job2.name, job2),
        jobExecutor.defer(job3.name, job3),
      ]);

      // Assertions
      expect(results).to.deep.equal([true, true, true]);
      expect(mockAdapter.resetJob.callCount).to.equal(3);
      expect(mockMetrics.increment.callCount).to.equal(3);

      for (const [index, job] of [job1, job2, job3].entries()) {
        expect(mockAdapter.resetJob.getCall(index).args[0]).to.equal(job);
        expect(mockAdapter.resetJob.getCall(index).args[1]).to.match(
          /\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/
        );
        expect(
          mockMetrics.increment
            .getCall(index)
            .calledWith('scheduler.job.deferred', 1, {
              job_type: job.name,
              service: jobExecutorConfig.service,
            })
        ).to.be.true;
        expect(
          mockLogger.debug
            .getCall(index)
            .calledWith(
              { jobId: job.id },
              'JobExecutor: Job deferred to next cycle'
            )
        ).to.be.true;
      }
    });
  });
});
