/* eslint-disable @typescript-eslint/no-floating-promises */
import Logger, { createLogger } from 'bunyan';
import moment from 'moment-timezone';
import { SinonStub, createSandbox, SinonSandbox, SinonFakeTimers } from 'sinon';
import type TypedEmitter from 'typed-emitter';
import { EventEmitter } from 'events';
import { expect } from './setup';
import { Maintenance } from '../src/maintenance';
import type { DatabaseAdapter, Events, JobBase } from '../src';

describe('Maintenance task', () => {
  let maintenance: Maintenance;
  let sandbox: SinonSandbox;
  let clock: SinonFakeTimers;
  let eventsStub: SinonStub;
  let adapter: DatabaseAdapter;
  const jobsSafeToRestart = ['A', 'C', 'E', 'G', 'I', 'J'];
  const jobsRiskyToRestart = ['B', 'D', 'F', 'H'];
  const jobsCustomRestart: Record<string, moment.Duration> = {
    I: moment.duration(15, 'minutes'),
    J: moment.duration(15, 'minutes'),
  };
  let logger: Logger;
  let events: TypedEmitter<Events>;

  beforeEach(() => {
    sandbox = createSandbox();
    clock = sandbox.useFakeTimers();
    eventsStub = sandbox.stub();

    // Create mock adapter with all required methods
    adapter = {
      getPendingJobs: sandbox.stub().resolves({}),
      getBlockedJobs: sandbox.stub().resolves([]),
      getLaggedJobs: sandbox.stub().resolves([]),
      resetJob: sandbox.stub().resolves(),
      fetchAndEnqueueJobs: sandbox.stub().resolves(),
      updateStartTask: sandbox.stub().resolves(),
      updateFinishTask: sandbox.stub().resolves(),
      updateFailure: sandbox.stub().resolves(),
      disconnect: sandbox.stub().resolves(),
    };

    // Create a proper TypedEmitter mock
    events = new EventEmitter() as TypedEmitter<Events>;
    events.emit = eventsStub;

    logger = createLogger({ name: 'test' }) as unknown as Logger;

    maintenance = new Maintenance({
      adapter,
      logger,
      jobsSafeToRestart,
      jobsCustomRestart,
      jobsRiskyToRestart,
      events,
      service: 'test-service',
      metrics: {
        increment: sandbox.stub(),
        gauge: sandbox.stub(),
        timing: sandbox.stub(),
      },
    });
  });

  afterEach(() => {
    sandbox.restore();
  });

  it('Works', async () => {
    const pendingJobs = {
      A: 1,
      B: 1,
    };

    const blockedJobs = [
      {
        id: '2',
        name: 'C',
        repeatInterval: 'FREQ=HOURLY;INTERVAL=1',
        data: {},
      },
      {
        id: '5',
        name: 'D',
        repeatInterval: 'FREQ=HOURLY;INTERVAL=1',
        data: {},
      },
    ] as JobBase[];

    const laggedJobs = [
      {
        id: '3',
        name: 'I',
        repeatInterval: 'FREQ=HOURLY;INTERVAL=1',
        acceptedAt: new Date(),
        data: {},
      },
      {
        id: '4',
        name: 'J',
        repeatInterval: 'FREQ=MINUTELY;INTERVAL=15',
        acceptedAt: new Date(),
        data: {},
      },
    ] as JobBase[];

    (adapter.getPendingJobs as SinonStub).resolves(pendingJobs);

    (adapter.getBlockedJobs as SinonStub).resolves(blockedJobs);

    (adapter.getLaggedJobs as SinonStub).resolves(laggedJobs);

    await maintenance.start();

    // Fast-forward time to allow maintenance to run
    await clock.tickAsync(100);

    maintenance.stop();

    expect(eventsStub.callCount).to.equal(8);

    // partition the events into pending, reset, and lagged
    const pendingEvents = eventsStub.args.filter(
      args => args[0] === 'pending'
    ) as [
      'pending',
      {
        id: string;
        name: string;
        repeatInterval: string;
        data: {};
      }
    ][];

    const resetEvents = eventsStub.args.filter(args => args[0] === 'reset') as [
      'reset',
      {
        id: string;
        name: string;
        repeatInterval: string;
        data: {};
      }
    ][];

    const laggedEvents = eventsStub.args.filter(
      args => args[0] === 'lagged'
    ) as [
      'lagged',
      {
        id: string;
        name: string;
        repeatInterval: string;
        data: {};
      }[]
    ][];

    // Verify 'pending' event
    expect(pendingEvents.length).to.equal(1);
    expect(pendingEvents[0][0]).to.equal('pending');
    expect(pendingEvents[0][1]).to.deep.equal({ A: 1, B: 1 });

    // Verify reset events for blocked jobs
    // There are duplicate reset events, so we check the total count
    const blockedResetEvents = resetEvents.filter(
      event => event[1].id === '2' || event[1].id === '5'
    );
    expect(blockedResetEvents.length).to.equal(4);

    // Verify that blocked jobs C and D were reset
    const resetJobIds = resetEvents.map(event => event[1].id);
    expect(resetJobIds).to.include('2'); // Job C
    expect(resetJobIds).to.include('5'); // Job D

    // Verify reset events for lagged jobs
    const laggedResetEvents = resetEvents.filter(
      event => event[1].id === '3' || event[1].id === '4'
    );
    expect(laggedResetEvents.length).to.equal(2);

    // Verify that lagged jobs I and J were reset
    expect(resetJobIds).to.include('3'); // Job I
    expect(resetJobIds).to.include('4'); // Job J

    // Verify 'lagged' event
    expect(laggedEvents.length).to.equal(1);
    expect(laggedEvents[0][0]).to.equal('lagged');
    expect(laggedEvents[0][1].length).to.equal(2);
    expect(laggedEvents[0][1].map(job => job.name)).to.have.members(['I', 'J']);

    // Verify resetJob was called
    expect((adapter.resetJob as SinonStub).callCount).to.equal(6); // 4 for blocked jobs + 2 for lagged jobs
  });

  it('should handle maintenance loop with multiple iterations', async () => {
    maintenance.start();

    // Fast-forward time to trigger multiple maintenance cycles
    await clock.tickAsync(65 * 1000); // First cycle
    await clock.tickAsync(65 * 1000); // Second cycle

    maintenance.stop();

    // Should have run maintenance at least twice
    expect((adapter.getPendingJobs as SinonStub).callCount).to.be.at.least(2);
  });

  it('should handle empty job lists', async () => {
    maintenance.start();
    await clock.tickAsync(100);
    maintenance.stop();

    // Should still complete without errors
    expect(eventsStub.calledWith('pending', {})).to.be.true;
  });

  it('should handle errors during maintenance run', async () => {
    // Simulate an error during maintenance
    const testError = new Error('Test maintenance error');
    (adapter.getPendingJobs as SinonStub).rejects(testError);

    // Replace logger.warn with a stub that we can test more easily
    const warnStub = sandbox.stub();
    const originalWarn = logger.warn;
    logger.warn = warnStub;

    maintenance.start();
    await clock.tickAsync(100);

    // Restore the original warn method
    logger.warn = originalWarn;

    // Should have logged the error via logger.warn
    expect(warnStub.calledOnce).to.be.true;
    // Check the structure logged: { err: Error }
    expect(warnStub.firstCall.args[0]).to.have.property('err');
    expect(warnStub.firstCall.args[0].err).to.be.instanceOf(Error);
    expect(warnStub.firstCall.args[0].err.message).to.equal(
      'Test maintenance error'
    );
    expect(warnStub.firstCall.args[1]).to.equal('Maintenance failed');

    // Reset the adapter stub for the next iteration
    (adapter.getPendingJobs as SinonStub).reset();
    (adapter.getPendingJobs as SinonStub).resolves({});

    // Advance the clock to trigger the next scheduled maintenance
    await clock.tickAsync(60 * 1000);
    await clock.tickAsync(100); // Give a little extra time for the maintenance to run

    maintenance.stop();

    // Should try again
    expect((adapter.getPendingJobs as SinonStub).callCount).to.be.at.least(1);
  });

  it('should properly reset jobs with resetJob helper', async () => {
    // Create a job instance with a repeatInterval
    const jobInstance = {
      id: '1',
      name: 'test-job',
      repeatInterval: 'FREQ=DAILY;INTERVAL=1',
      timezone: 'UTC',
      data: {},
    } as JobBase;

    (adapter.getBlockedJobs as SinonStub).resolves([jobInstance]);

    maintenance.start();
    await clock.tickAsync(100);
    maintenance.stop();

    // Should have emitted a reset event
    const resetCallIndex = eventsStub.args.findIndex(
      args => args[0] === 'reset'
    );
    expect(resetCallIndex).to.be.greaterThan(-1);
    expect(eventsStub.args[resetCallIndex][1]).to.deep.include({ id: '1' });
  });

  it('should not reset jobs without repeatInterval', async () => {
    // Create a job instance without a repeatInterval
    const jobInstance = {
      id: '1',
      name: 'one-time-job',
      repeatInterval: null,
      data: {},
    } as JobBase;

    (adapter.getBlockedJobs as SinonStub).resolves([jobInstance]);

    const emitCallCountBefore = eventsStub.callCount;

    maintenance.start();
    await clock.tickAsync(100);
    maintenance.stop();

    // Should not have reset the job
    expect((adapter.resetJob as SinonStub).called).to.be.false;

    // There should be no extra 'reset' events
    const resetCalls = eventsStub.args
      .slice(emitCallCountBefore)
      .filter(args => args[0] === 'reset' && args[1].id === 1);
    expect(resetCalls.length).to.equal(0);
  });

  it('should adjust interval timing based on execution duration', async () => {
    // Create a slow execution to test interval adjustment
    (adapter.getPendingJobs as SinonStub).callsFake(async () => {
      await new Promise<void>(resolve => {
        setTimeout(() => resolve(), 200);
      });
      return {};
    });

    maintenance.start();

    // Fast-forward time to include the execution delay
    await clock.tickAsync(300);

    // The next scheduled interval should be adjusted to maintain proper timing
    await clock.tickAsync(60 * 1000 - 200); // Should trigger the next maintenance

    maintenance.stop();

    // Should have run maintenance twice
    expect((adapter.getPendingJobs as SinonStub).callCount).to.equal(2);
  });
});
