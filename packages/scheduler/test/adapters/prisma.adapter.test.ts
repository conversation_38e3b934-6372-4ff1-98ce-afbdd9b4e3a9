import sinon from 'sinon';
import moment from 'moment-timezone';
import type { PrismaClient } from '@prisma/client';
import { expect } from '../setup';
import { PrismaAdapter } from '../../src/adapters/prisma.adapter';
import * as utils from '../../src/utils'; // Import utils to stub
import type { JobBase } from '../../src';
import type { JobSet } from '../../src/types/job-set.type';
import type { PendingJobs } from '../../src/types/pending-jobs.type';

// Mock PrismaClient
// We only need to mock the methods actually used by the adapter
type MockPrismaJobDelegate = {
  findUnique: sinon.SinonStub;
  update: sinon.SinonStub;
  delete: sinon.SinonStub;
};

type MockPrismaClient = {
  $queryRaw: sinon.SinonStub;
  $disconnect: sinon.SinonStub;
  job: MockPrismaJobDelegate;
  // Add other delegates if needed by other adapter methods
};

describe('PrismaAdapter', () => {
  let sandbox: sinon.SinonSandbox;
  let mockPrismaClient: MockPrismaClient;
  let adapter: PrismaAdapter;
  let clock: sinon.SinonFakeTimers;
  let computeNextRunStub: sinon.SinonStub;
  let retryDelayStub: sinon.SinonStub;

  const testJob: JobBase = {
    id: 'job-test-123',
    name: 'test-job',
    data: { test: true },
    repeatInterval: 'FREQ=DAILY',
    timezone: 'UTC',
    failures: 0,
  };
  const testJobWithNamespace: JobBase = {
    ...testJob,
    id: 'job-ns-456',
    namespace: 'my-namespace',
  };

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    clock = sandbox.useFakeTimers(new Date()); // Fix time for consistency

    // Stub utility functions
    computeNextRunStub = sandbox
      .stub(utils, 'computeNextRun')
      .returns(moment().add(1, 'day').toISOString());
    retryDelayStub = sandbox.stub(utils, 'retryDelay').returns(5000); // 5 seconds backoff

    // Create mock Prisma client methods
    mockPrismaClient = {
      $queryRaw: sandbox.stub(),
      $disconnect: sandbox.stub().resolves(),
      job: {
        findUnique: sandbox.stub(),
        update: sandbox.stub(),
        delete: sandbox.stub(),
      },
    };

    // Instantiate the adapter with the mock client
    adapter = new PrismaAdapter(mockPrismaClient as unknown as PrismaClient);
  });

  afterEach(() => {
    clock.restore();
    sandbox.restore();
  });

  describe('fetchAndEnqueueJobs', () => {
    const mockJobSets: JobSet[] = [
      { priority: 1, name: 'test-job', total: 1, items: [testJob] },
    ];
    const allJobs = ['test-job', 'another-job'];
    const maxJobs = 5;

    it('should call $queryRaw with the correct SQL query', async () => {
      mockPrismaClient.$queryRaw.resolves(mockJobSets);
      const result = await adapter.fetchAndEnqueueJobs(allJobs, maxJobs);

      expect(result).to.deep.equal(mockJobSets);
      expect(mockPrismaClient.$queryRaw.calledOnce).to.be.true;
      const queryArg = mockPrismaClient.$queryRaw.firstCall.args[0];
      // Basic checks on the query structure - more detailed checks are complex
      expect(queryArg.sql.trim()).to.include('WITH updated as');
      expect(queryArg.values).to.include.members(allJobs);
      expect(queryArg.values).to.include.members([maxJobs]);
    });

    it('should include namespace in the query if provided', async () => {
      const namespace = 'my-ns';
      mockPrismaClient.$queryRaw.resolves([]);
      await adapter.fetchAndEnqueueJobs(allJobs, maxJobs, namespace);

      expect(mockPrismaClient.$queryRaw.calledOnce).to.be.true;
      const queryArg = mockPrismaClient.$queryRaw.firstCall.args[0];
      expect(queryArg.sql).to.include('AND namespace =');
      expect(queryArg.values).to.include(namespace);
    });

    it('should not include namespace in the query if not provided', async () => {
      mockPrismaClient.$queryRaw.resolves([]);
      await adapter.fetchAndEnqueueJobs(allJobs, maxJobs); // No namespace

      expect(mockPrismaClient.$queryRaw.calledOnce).to.be.true;
      const queryArg = mockPrismaClient.$queryRaw.firstCall.args[0];
      expect(queryArg.sql).to.not.include('AND namespace =');
    });
  });

  describe('resetJob', () => {
    const nextRun = moment().add(1, 'hour').toISOString();

    it('should call job.update with correct data', async () => {
      mockPrismaClient.job.update.resolves(testJob); // Simulate successful update
      await adapter.resetJob(testJob, nextRun);

      expect(mockPrismaClient.job.update.calledOnce).to.be.true;
      expect(mockPrismaClient.job.update.firstCall.args[0]).to.deep.equal({
        where: { id: testJob.id },
        data: { queued: false, nextRunAt: nextRun, failures: 0 },
      });
    });

    it('should use id_namespace where clause if namespace exists', async () => {
      mockPrismaClient.job.update.resolves(testJobWithNamespace);
      await adapter.resetJob(testJobWithNamespace, nextRun);

      expect(mockPrismaClient.job.update.calledOnce).to.be.true;
      expect(mockPrismaClient.job.update.firstCall.args[0]).to.deep.equal({
        where: {
          id_namespace: {
            id: testJobWithNamespace.id,
            namespace: testJobWithNamespace.namespace,
          },
        },
        data: { queued: false, nextRunAt: nextRun, failures: 0 },
      });
    });
  });

  describe('updateStartTask', () => {
    it('should call job.update with acceptedAt', async () => {
      mockPrismaClient.job.update.resolves(testJob);
      await adapter.updateStartTask(testJob.id);

      expect(mockPrismaClient.job.update.calledOnce).to.be.true;
      expect(mockPrismaClient.job.update.firstCall.args[0].where).to.deep.equal(
        { id: testJob.id }
      );
      expect(
        mockPrismaClient.job.update.firstCall.args[0].data.acceptedAt
      ).to.be.a('date');
    });

    it('should use id_namespace where clause if namespace exists', async () => {
      mockPrismaClient.job.update.resolves(testJobWithNamespace);
      await adapter.updateStartTask(
        testJobWithNamespace.id,
        testJobWithNamespace.namespace ?? undefined // Ensure undefined if null/undefined
      );

      expect(mockPrismaClient.job.update.calledOnce).to.be.true;
      expect(mockPrismaClient.job.update.firstCall.args[0].where).to.deep.equal(
        {
          id_namespace: {
            id: testJobWithNamespace.id,
            namespace: testJobWithNamespace.namespace,
          },
        }
      );
      expect(
        mockPrismaClient.job.update.firstCall.args[0].data.acceptedAt
      ).to.be.a('date');
    });
  });

  describe('updateFinishTask', () => {
    const calculatedNextRun = moment().add(1, 'day').toISOString();
    beforeEach(() => {
      computeNextRunStub.returns(calculatedNextRun);
    });

    it('should find the job, compute next run, and update', async () => {
      mockPrismaClient.job.findUnique.resolves(testJob); // Job has repeatInterval
      mockPrismaClient.job.update.resolves(testJob);

      await adapter.updateFinishTask(testJob.id);

      expect(
        mockPrismaClient.job.findUnique.calledOnceWith({
          where: { id: testJob.id },
        })
      ).to.be.true;
      expect(
        // Use nullish coalescing for optional timezone in assertion
        computeNextRunStub.calledOnceWith(testJob.repeatInterval, {
          timezone: testJob.timezone ?? 'UTC',
        })
      ).to.be.true;
      expect(mockPrismaClient.job.update.calledOnce).to.be.true;
      const updateArgs = mockPrismaClient.job.update.firstCall.args[0];
      expect(updateArgs.where).to.deep.equal({ id: testJob.id });
      expect(updateArgs.data.queued).to.be.false;
      expect(updateArgs.data.nextRunAt).to.equal(calculatedNextRun);
      expect(updateArgs.data.lastFinishedAt).to.be.a('date');
      expect(updateArgs.data.failures).to.equal(0);
      expect(updateArgs.data.failedAt).to.be.null;
      expect(mockPrismaClient.job.delete.called).to.be.false;
    });

    it('should use id_namespace for find and update if namespace exists', async () => {
      mockPrismaClient.job.findUnique.resolves(testJobWithNamespace);
      mockPrismaClient.job.update.resolves(testJobWithNamespace);

      await adapter.updateFinishTask(
        testJobWithNamespace.id,
        testJobWithNamespace.namespace ?? undefined // Ensure undefined if null/undefined
      );

      expect(
        mockPrismaClient.job.findUnique.calledOnceWith({
          where: {
            id_namespace: {
              id: testJobWithNamespace.id,
              namespace: testJobWithNamespace.namespace,
            },
          },
        })
      ).to.be.true;
      expect(computeNextRunStub.calledOnce).to.be.true;
      expect(mockPrismaClient.job.update.calledOnce).to.be.true;
      expect(mockPrismaClient.job.update.firstCall.args[0].where).to.deep.equal(
        {
          id_namespace: {
            id: testJobWithNamespace.id,
            namespace: testJobWithNamespace.namespace,
          },
        }
      );
      expect(mockPrismaClient.job.delete.called).to.be.false;
    });

    it('should delete the job if repeatInterval is null', async () => {
      const nonRepeatingJob = { ...testJob, repeatInterval: null };
      mockPrismaClient.job.findUnique.resolves(nonRepeatingJob);
      mockPrismaClient.job.delete.resolves(nonRepeatingJob); // Simulate successful delete

      await adapter.updateFinishTask(nonRepeatingJob.id);

      expect(mockPrismaClient.job.findUnique.calledOnce).to.be.true;
      expect(computeNextRunStub.called).to.be.false; // No next run calculation
      expect(mockPrismaClient.job.update.called).to.be.false; // No update
      expect(
        mockPrismaClient.job.delete.calledOnceWith({
          where: { id: nonRepeatingJob.id },
        })
      ).to.be.true;
    });

    it('should do nothing if job is not found', async () => {
      mockPrismaClient.job.findUnique.resolves(null); // Simulate job not found

      await adapter.updateFinishTask('not-found-id');

      expect(mockPrismaClient.job.findUnique.calledOnce).to.be.true;
      expect(computeNextRunStub.called).to.be.false;
      expect(mockPrismaClient.job.update.called).to.be.false;
      expect(mockPrismaClient.job.delete.called).to.be.false;
    });
  });

  describe('updateFailure', () => {
    const backOffMs = 10000;
    const maxRestarts = 3;
    const safeJobs: string[] = [testJob.name];
    const calculatedBackoffDelay = 5000; // From stub
    const calculatedNextRun = moment()
      .add(calculatedBackoffDelay, 'milliseconds')
      .toISOString();

    beforeEach(() => {
      retryDelayStub.returns(calculatedBackoffDelay);
    });

    it('should find job, update failure count/time, and schedule retry if applicable', async () => {
      const failingJob = { ...testJob, failures: 1 }; // Already failed once
      mockPrismaClient.job.findUnique.resolves(failingJob);
      mockPrismaClient.job.update.resolves(failingJob); // Simulate successful updates
      await adapter.updateFailure(
        failingJob.id,
        backOffMs,
        maxRestarts,
        safeJobs
      );

      expect(
        mockPrismaClient.job.findUnique.calledOnceWith({
          where: { id: failingJob.id },
        })
      ).to.be.true;

      // Check first update (failure count)
      expect(mockPrismaClient.job.update.firstCall.args[0].where).to.deep.equal(
        { id: failingJob.id }
      );
      expect(
        mockPrismaClient.job.update.firstCall.args[0].data.failures
      ).to.equal(failingJob.failures + 1);
      expect(
        mockPrismaClient.job.update.firstCall.args[0].data.failedAt
      ).to.be.a('date');

      // Check retry logic
      expect(retryDelayStub.calledOnceWith(failingJob.failures + 1, backOffMs))
        .to.be.true;

      // Check second update (reschedule)
      expect(
        mockPrismaClient.job.update.secondCall.args[0].where
      ).to.deep.equal({ id: failingJob.id });
      expect(mockPrismaClient.job.update.secondCall.args[0].data.queued).to.be
        .false;
      expect(
        moment(
          mockPrismaClient.job.update.secondCall.args[0].data.nextRunAt
        ).diff(moment(calculatedNextRun), 'seconds')
      ).to.be.lessThan(10);

      expect(mockPrismaClient.job.update.calledTwice).to.be.true; // Called for failure count and reschedule
    });

    it('should use id_namespace for find and updates if namespace exists', async () => {
      const failingJobNs = { ...testJobWithNamespace, failures: 0 };
      mockPrismaClient.job.findUnique.resolves(failingJobNs);
      mockPrismaClient.job.update.resolves(failingJobNs);

      await adapter.updateFailure(
        failingJobNs.id,
        backOffMs,
        maxRestarts,
        safeJobs,
        failingJobNs.namespace ?? undefined // Ensure undefined if null/undefined
      );

      const expectedWhere = {
        id_namespace: {
          id: failingJobNs.id,
          namespace: failingJobNs.namespace,
        },
      };
      expect(
        mockPrismaClient.job.findUnique.calledOnceWith({ where: expectedWhere })
      ).to.be.true;
      expect(mockPrismaClient.job.update.firstCall.args[0].where).to.deep.equal(
        expectedWhere
      );
      expect(
        mockPrismaClient.job.update.secondCall.args[0].where
      ).to.deep.equal(expectedWhere);
      expect(mockPrismaClient.job.update.calledTwice).to.be.true;
    });

    it('should only update failure count if max restarts reached', async () => {
      const failingJob = { ...testJob, failures: maxRestarts }; // Already at max failures
      mockPrismaClient.job.findUnique.resolves(failingJob);
      mockPrismaClient.job.update.resolves(failingJob);

      await adapter.updateFailure(
        failingJob.id,
        backOffMs,
        maxRestarts,
        safeJobs
      );

      expect(mockPrismaClient.job.findUnique.calledOnce).to.be.true;
      expect(mockPrismaClient.job.update.calledOnce).to.be.true; // Only called once for failure count
      expect(
        mockPrismaClient.job.update.firstCall.args[0].data.failures
      ).to.equal(failingJob.failures + 1);
      expect(retryDelayStub.called).to.be.false; // No retry calculation
    });

    it('should only update failure count if job is not safe to restart (and safe list is provided)', async () => {
      const failingJob = { ...testJob, name: 'unsafe-job', failures: 0 }; // Unsafe job name
      mockPrismaClient.job.findUnique.resolves(failingJob);
      mockPrismaClient.job.update.resolves(failingJob);

      await adapter.updateFailure(
        failingJob.id,
        backOffMs,
        maxRestarts,
        safeJobs
      ); // safeJobs list provided

      expect(mockPrismaClient.job.findUnique.calledOnce).to.be.true;
      expect(mockPrismaClient.job.update.calledOnce).to.be.true; // Only called once for failure count
      expect(retryDelayStub.called).to.be.false; // No retry calculation
    });

    it('should schedule retry if job is safe to restart', async () => {
      const failingSafeJob = { ...testJob, failures: 0 }; // Safe job name
      mockPrismaClient.job.findUnique.resolves(failingSafeJob);
      mockPrismaClient.job.update.resolves(failingSafeJob);

      await adapter.updateFailure(
        failingSafeJob.id,
        backOffMs,
        maxRestarts,
        safeJobs
      );

      expect(mockPrismaClient.job.update.calledTwice).to.be.true; // Failure count + reschedule
      expect(retryDelayStub.calledOnce).to.be.true;
    });

    it('should schedule retry if safeJobs list is empty', async () => {
      const failingJob = { ...testJob, name: 'any-job', failures: 0 };
      mockPrismaClient.job.findUnique.resolves(failingJob);
      mockPrismaClient.job.update.resolves(failingJob);

      await adapter.updateFailure(failingJob.id, backOffMs, maxRestarts, []); // Empty safeJobs list

      expect(mockPrismaClient.job.update.calledTwice).to.be.true; // Failure count + reschedule
      expect(retryDelayStub.calledOnce).to.be.true;
    });

    it('should do nothing if job is not found', async () => {
      mockPrismaClient.job.findUnique.resolves(null); // Simulate job not found

      await adapter.updateFailure(
        'not-found-id',
        backOffMs,
        maxRestarts,
        safeJobs
      );

      expect(mockPrismaClient.job.findUnique.calledOnce).to.be.true;
      expect(mockPrismaClient.job.update.called).to.be.false;
      expect(retryDelayStub.called).to.be.false;
    });
  });

  describe('getPendingJobs', () => {
    const mockPendingResult = [
      { name: 'job-a', count: '2' },
      { name: 'job-b', count: '1' },
    ];
    const expectedPendingJobs: PendingJobs = { 'job-a': 2, 'job-b': 1 };
    const allJobs = ['job-a', 'job-b', 'job-c'];

    it('should call $queryRaw and format the result', async () => {
      mockPrismaClient.$queryRaw.resolves(mockPendingResult);
      const result = await adapter.getPendingJobs(allJobs);

      expect(result).to.deep.equal(expectedPendingJobs);
      expect(mockPrismaClient.$queryRaw.calledOnce).to.be.true;
      const queryArg = mockPrismaClient.$queryRaw.firstCall.args[0];
      expect(queryArg.sql).to.include(
        'SELECT name, count(name)::int from jobs'
      );
      expect(queryArg.sql).to.include(
        'WHERE queued = false AND next_run_at < CURRENT_TIMESTAMP'
      );
    });

    it('should return empty object if no jobs are provided', async () => {
      const result = await adapter.getPendingJobs([]);
      expect(result).to.deep.equal({});
      expect(mockPrismaClient.$queryRaw.called).to.be.false;
    });

    it('should return empty object if query returns no results', async () => {
      mockPrismaClient.$queryRaw.resolves([]);
      const result = await adapter.getPendingJobs(allJobs);
      expect(result).to.deep.equal({});
      expect(mockPrismaClient.$queryRaw.calledOnce).to.be.true;
    });
  });

  describe('getBlockedJobs', () => {
    const jobNames = ['blocked-job'];
    const blockedMins = 10;
    const mockBlockedResult: JobBase[] = [{ ...testJob, name: 'blocked-job' }];

    it('should call $queryRaw with correct query and parameters', async () => {
      mockPrismaClient.$queryRaw.resolves(mockBlockedResult);
      const result = await adapter.getBlockedJobs(jobNames, blockedMins);

      expect(result).to.deep.equal(mockBlockedResult);
      expect(mockPrismaClient.$queryRaw.calledOnce).to.be.true;
      const queryArg = mockPrismaClient.$queryRaw.firstCall.args[0];
      expect(queryArg.sql).to.include('FROM jobs');
      expect(queryArg.sql).to.include('WHERE queued = true');
      expect(queryArg.sql).to.include(
        '(accepted_at < last_run_at OR accepted_at IS NULL)'
      );
      expect(queryArg.sql).to.include('last_run_at <= CURRENT_TIMESTAMP');
    });

    it('should return empty array if no job names provided', async () => {
      const result = await adapter.getBlockedJobs([], blockedMins);
      expect(result).to.deep.equal([]);
      expect(mockPrismaClient.$queryRaw.called).to.be.false;
    });
  });

  describe('getLaggedJobs', () => {
    const jobNames = ['lagged-job'];
    const laggedMins = 6;
    const mockLaggedResult: JobBase[] = [{ ...testJob, name: 'lagged-job' }];

    it('should call $queryRaw with correct query and parameters', async () => {
      mockPrismaClient.$queryRaw.resolves(mockLaggedResult);
      const result = await adapter.getLaggedJobs(jobNames, laggedMins);

      expect(result).to.deep.equal(mockLaggedResult);
      expect(mockPrismaClient.$queryRaw.calledOnce).to.be.true;
      const queryArg = mockPrismaClient.$queryRaw.firstCall.args[0];
      expect(queryArg.sql).to.include('FROM jobs');
      expect(queryArg.sql).to.include(
        'WHERE queued = true AND last_run_at < accepted_at'
      );
      expect(queryArg.sql).to.include('(last_finished_at < CURRENT_TIMESTAMP');
    });

    it('should return empty array if no job names provided', async () => {
      const result = await adapter.getLaggedJobs([], laggedMins);
      expect(result).to.deep.equal([]);
      expect(mockPrismaClient.$queryRaw.called).to.be.false;
    });
  });

  describe('disconnect', () => {
    it('should call prisma.$disconnect', async () => {
      await adapter.disconnect();
      expect(mockPrismaClient.$disconnect.calledOnce).to.be.true;
    });
  });
});
