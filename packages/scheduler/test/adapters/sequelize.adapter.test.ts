import sinon from 'sinon';
import moment from 'moment-timezone';
import Sequelize, { Model } from 'sequelize'; // Import Sequelize for types and Op
import { expect } from '../setup';
import { SequelizeAdapter } from '../../src/adapters/sequelize.adapter';
import * as utils from '../../src/utils'; // Import utils to stub
import type { JobBase } from '../../src';
import type { JobSet } from '../../src/types/job-set.type';
import type { PendingJobs } from '../../src/types/pending-jobs.type';

// --- Mocking Sequelize ---
// Mock the Sequelize instance methods we expect the adapter to call
const mockSequelizeInstance = {
  query: sinon.stub(),
  close: sinon.stub().resolves(),
  // Add other methods if needed
};

// Mock the Model static methods used by the adapter
const mockJobModelStatic = {
  init: sinon.stub(), // We won't test init details, just assume it works
  update: sinon.stub(),
  findOne: sinon.stub(),
  findAll: sinon.stub(),
};

// Mock the Model instance methods used by the adapter
const mockJobInstance = {
  update: sinon.stub(),
  destroy: sinon.stub(),
};

// Stub the Sequelize constructor to return our mock instance
// This is tricky; ideally use a library like proxyquire or jest.mock
// For now, we'll assume a way to inject/replace the Sequelize constructor or instance
// A common pattern is to stub the prototype or specific methods if direct constructor mocking is hard
// Let's stub the methods on the prototype *before* the adapter instance is created.

describe('SequelizeAdapter', () => {
  let sandbox: sinon.SinonSandbox;
  let adapter: SequelizeAdapter;
  let clock: sinon.SinonFakeTimers;
  let computeNextRunStub: sinon.SinonStub;
  let retryDelayStub: sinon.SinonStub;

  // Stubs for Sequelize methods that will be placed on prototypes or instance
  let queryStub: sinon.SinonStub;
  let closeStub: sinon.SinonStub;
  let modelInitStub: sinon.SinonStub;
  let modelUpdateStub: sinon.SinonStub;
  let modelFindOneStub: sinon.SinonStub;
  let modelFindAllStub: sinon.SinonStub;
  let instanceUpdateStub: sinon.SinonStub;
  let instanceDestroyStub: sinon.SinonStub;

  const testJob: JobBase = {
    id: 'job-seq-123',
    name: 'test-job',
    data: { test: true },
    repeatInterval: 'FREQ=DAILY',
    timezone: 'UTC',
    failures: 0,
  };
  const testJobWithNamespace: JobBase = {
    ...testJob,
    id: 'job-seq-ns-456',
    namespace: 'my-namespace',
  };

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    clock = sandbox.useFakeTimers(new Date());

    // Stub utility functions
    computeNextRunStub = sandbox
      .stub(utils, 'computeNextRun')
      .returns(moment().add(1, 'day').toISOString());
    retryDelayStub = sandbox.stub(utils, 'retryDelay').returns(5000); // 5 seconds backoff

    // --- Setup Sequelize Mocks ---
    // Stub methods expected to be called on the Sequelize instance
    queryStub = sandbox.stub(Sequelize.Sequelize.prototype, 'query');
    closeStub = sandbox.stub(Sequelize.Sequelize.prototype, 'close').resolves();

    // Stub static methods on the Model class (used by adapter's Job)
    // Note: This assumes the adapter uses methods directly on the class returned by init.
    // If it uses a specific instance variable like `this.Job`, this might need adjustment.
    modelInitStub = sandbox
      .stub(Model, 'init')
      .returns(mockJobModelStatic as any); // Return mock static methods
    modelUpdateStub = sandbox.stub(Model, 'update');
    modelFindOneStub = sandbox.stub(Model, 'findOne');
    modelFindAllStub = sandbox.stub(Model, 'findAll');

    // Stub instance methods on the Model prototype
    instanceUpdateStub = sandbox.stub(Model.prototype, 'update');
    instanceDestroyStub = sandbox.stub(Model.prototype, 'destroy');
    // --- End Sequelize Mocks ---

    // Instantiate the adapter - it will internally create a Sequelize instance
    // and initialize the model, which should now use our stubs.
    adapter = new SequelizeAdapter('postgres://test:test@localhost:5432/test');

    // Assign the static stubs to the adapter's Job property after init
    // This is crucial because init is called in the constructor
    (adapter as any).Job = {
      ...mockJobModelStatic, // Keep original static methods if any
      update: modelUpdateStub,
      findOne: modelFindOneStub,
      findAll: modelFindAllStub,
      // init is already stubbed on Model directly
    };
  });

  afterEach(() => {
    clock.restore();
    sandbox.restore(); // Restores prototype stubs
  });

  describe('fetchAndEnqueueJobs', () => {
    const mockJobSets: JobSet[] = [
      { priority: 1, name: 'test-job', total: 1, items: [testJob] },
    ];
    const allJobs = ['test-job', 'another-job'];
    const maxJobs = 5;

    it('should call sequelize.query with correct query and replacements', async () => {
      queryStub.resolves(mockJobSets); // Mock the query result
      const result = await adapter.fetchAndEnqueueJobs(allJobs, maxJobs);

      expect(result).to.deep.equal(mockJobSets);
      expect(queryStub.calledOnce).to.be.true;
      const queryOptions = queryStub.firstCall.args[1];
      expect(queryOptions.type).to.equal(Sequelize.QueryTypes.SELECT);
      expect(queryOptions.replacements).to.deep.equal({
        jobEnqueueLimit: maxJobs,
        jobs: allJobs,
        namespace: undefined, // No namespace passed
      });
      // Basic check on query string
      expect(queryStub.firstCall.args[0].trim()).to.include('WITH updated as');
    });

    it('should include namespace in replacements and query if provided', async () => {
      const namespace = 'my-ns';
      queryStub.resolves([]);
      // Use a custom query builder mock for this test to check namespace inclusion
      const customQueryFn = sinon
        .stub()
        .returns(`SELECT * FROM jobs WHERE namespace = :namespace`);
      const adapterWithCustomQuery = new SequelizeAdapter(
        'postgres://test',
        customQueryFn
      );
      // Re-assign stubs to the new instance
      (adapterWithCustomQuery as any).sequelize.query = queryStub;

      await adapterWithCustomQuery.fetchAndEnqueueJobs(
        allJobs,
        maxJobs,
        namespace
      );

      expect(customQueryFn.calledOnceWith(namespace)).to.be.true; // Check query builder was called
      expect(queryStub.calledOnce).to.be.true;
      const queryOptions = queryStub.firstCall.args[1];
      expect(queryOptions.replacements.namespace).to.equal(namespace);
      // Check if the generated query string (from the stubbed fn) includes namespace part
      expect(queryStub.firstCall.args[0].trim()).to.include(
        'namespace = :namespace'
      );
    });
  });

  describe('resetJob', () => {
    const nextRun = moment().add(1, 'hour').toISOString();
    const nextRunDate = moment(nextRun).toDate();

    it('should call Job.update with correct data and where clause', async () => {
      modelUpdateStub.resolves([1]); // Simulate 1 row updated
      await adapter.resetJob(testJob, nextRun);

      expect(modelUpdateStub.calledOnce).to.be.true;
      expect(modelUpdateStub.firstCall.args[0]).to.deep.equal({
        queued: false,
        nextRunAt: nextRunDate,
      });
      expect(modelUpdateStub.firstCall.args[1].where).to.deep.equal({
        id: testJob.id,
      });
    });

    it('should include namespace in where clause if provided', async () => {
      modelUpdateStub.resolves([1]);
      await adapter.resetJob(testJobWithNamespace, nextRun);

      expect(modelUpdateStub.calledOnce).to.be.true;
      expect(modelUpdateStub.firstCall.args[1].where).to.deep.equal({
        id: testJobWithNamespace.id,
        namespace: testJobWithNamespace.namespace,
      });
    });
  });

  describe('updateStartTask', () => {
    it('should call Job.update with acceptedAt', async () => {
      modelUpdateStub.resolves([1]);
      await adapter.updateStartTask(testJob.id);

      expect(modelUpdateStub.calledOnce).to.be.true;
      expect(modelUpdateStub.firstCall.args[0].acceptedAt).to.be.a('date');
      expect(modelUpdateStub.firstCall.args[1].where).to.deep.equal({
        id: testJob.id,
      });
    });

    it('should include namespace in where clause if provided', async () => {
      modelUpdateStub.resolves([1]);
      await adapter.updateStartTask(
        testJobWithNamespace.id,
        testJobWithNamespace.namespace ?? undefined // Ensure undefined if null/undefined
      );

      expect(modelUpdateStub.calledOnce).to.be.true;
      expect(modelUpdateStub.firstCall.args[1].where).to.deep.equal({
        id: testJobWithNamespace.id,
        namespace: testJobWithNamespace.namespace,
      });
    });
  });

  describe('updateFinishTask', () => {
    const calculatedNextRun = moment().add(1, 'day').toISOString();
    const calculatedNextRunDate = moment(calculatedNextRun).toDate();

    beforeEach(() => {
      computeNextRunStub.returns(calculatedNextRun);
      // Prepare a mock job instance to be returned by findOne
      mockJobInstance.update = instanceUpdateStub;
      mockJobInstance.destroy = instanceDestroyStub;
    });

    it('should find the job, compute next run, and call job.update', async () => {
      // Return a mock instance that has the repeatInterval and our stubbed methods
      const mockFoundJob = { ...testJob, ...mockJobInstance };
      modelFindOneStub.resolves(mockFoundJob);
      instanceUpdateStub.resolves(); // Simulate successful instance update

      await adapter.updateFinishTask(testJob.id);

      expect(modelFindOneStub.calledOnce).to.be.true;
      expect(modelFindOneStub.firstCall.args[0].where).to.deep.equal({
        id: testJob.id,
      });
      expect(
        computeNextRunStub.calledOnceWith(testJob.repeatInterval, {
          timezone: testJob.timezone ?? 'UTC',
        })
      ).to.be.true;
      expect(instanceUpdateStub.calledOnce).to.be.true;
      const updateArgs = instanceUpdateStub.firstCall.args[0];
      expect(updateArgs.queued).to.be.false;
      expect(updateArgs.nextRunAt).to.deep.equal(calculatedNextRunDate); // Compare dates
      expect(updateArgs.lastFinishedAt).to.be.a('date');
      expect(updateArgs.failures).to.equal(0);
      expect(updateArgs.failedAt).to.be.null;
      expect(instanceDestroyStub.called).to.be.false;
    });

    it('should use namespace in where clause for findOne if provided', async () => {
      const mockFoundJobNs = { ...testJobWithNamespace, ...mockJobInstance };
      modelFindOneStub.resolves(mockFoundJobNs);
      instanceUpdateStub.resolves();

      await adapter.updateFinishTask(
        testJobWithNamespace.id,
        testJobWithNamespace.namespace ?? undefined // Ensure undefined if null/undefined
      );

      expect(modelFindOneStub.calledOnce).to.be.true;
      expect(modelFindOneStub.firstCall.args[0].where).to.deep.equal({
        id: testJobWithNamespace.id,
        namespace: testJobWithNamespace.namespace,
      });
      expect(instanceUpdateStub.calledOnce).to.be.true; // Ensure update was still called
    });

    it('should call job.destroy if repeatInterval is null', async () => {
      const nonRepeatingJob = {
        ...testJob,
        repeatInterval: null,
        ...mockJobInstance,
      };
      modelFindOneStub.resolves(nonRepeatingJob);
      instanceDestroyStub.resolves(); // Simulate successful destroy

      await adapter.updateFinishTask(nonRepeatingJob.id);

      expect(modelFindOneStub.calledOnce).to.be.true;
      expect(computeNextRunStub.called).to.be.false;
      expect(instanceUpdateStub.called).to.be.false; // No update call
      expect(instanceDestroyStub.calledOnce).to.be.true; // Destroy called
    });

    it('should do nothing if job is not found', async () => {
      modelFindOneStub.resolves(null); // Simulate job not found

      await adapter.updateFinishTask('not-found-id');

      expect(modelFindOneStub.calledOnce).to.be.true;
      expect(computeNextRunStub.called).to.be.false;
      expect(instanceUpdateStub.called).to.be.false;
      expect(instanceDestroyStub.called).to.be.false;
    });
  });

  describe('updateFailure', () => {
    const backOffMs = 10000;
    const maxRestarts = 3;
    const safeJobs: string[] = [testJob.name];
    const calculatedBackoffDelay = 5000; // From stub
    const calculatedNextRun = moment()
      .add(calculatedBackoffDelay, 'milliseconds')
      .toISOString();

    beforeEach(() => {
      retryDelayStub.returns(calculatedBackoffDelay);
      // Prepare mock job instance
      mockJobInstance.update = instanceUpdateStub;
    });

    it('should find job, update failure count/time, and schedule retry via job.update', async () => {
      const failingJobData = { ...testJob, failures: 1 }; // Already failed once
      const mockFoundJob = { ...failingJobData, ...mockJobInstance };
      modelFindOneStub.resolves(mockFoundJob);
      instanceUpdateStub.resolves(); // Simulate successful updates

      await adapter.updateFailure(
        failingJobData.id,
        backOffMs,
        maxRestarts,
        safeJobs
      );

      expect(
        modelFindOneStub.calledWithMatch({ where: { id: failingJobData.id } })
      ).to.be.true;

      // Check first update (failure count) - called on instance
      expect(instanceUpdateStub.firstCall.args[0].failures).to.equal(
        failingJobData.failures + 1
      );
      expect(instanceUpdateStub.firstCall.args[0].failedAt).to.be.a('string'); // Should be ISO string

      // Check retry logic
      expect(retryDelayStub.calledOnceWith(failingJobData.failures, backOffMs))
        .to.be.true;

      // Check second update (reschedule) - called on instance
      expect(instanceUpdateStub.secondCall.args[0].queued).to.be.false;
      expect(
        moment(instanceUpdateStub.secondCall.args[0].nextRunAt).diff(
          moment(calculatedNextRun),
          'seconds'
        )
      ).to.be.lessThan(10);

      expect(instanceUpdateStub.calledTwice).to.be.true; // Called for failure count and reschedule
    });

    it('should use namespace in where clause for findOne if provided', async () => {
      const failingJobNsData = { ...testJobWithNamespace, failures: 0 };
      const mockFoundJobNs = { ...failingJobNsData, ...mockJobInstance };
      modelFindOneStub.resolves(mockFoundJobNs);
      instanceUpdateStub.resolves();

      await adapter.updateFailure(
        failingJobNsData.id,
        backOffMs,
        maxRestarts,
        safeJobs,
        failingJobNsData.namespace ?? undefined // Ensure undefined if null/undefined
      );

      expect(modelFindOneStub.calledOnce).to.be.true;
      expect(modelFindOneStub.firstCall.args[0].where).to.deep.equal({
        id: failingJobNsData.id,
        namespace: failingJobNsData.namespace,
      });
      expect(instanceUpdateStub.calledTwice).to.be.true; // Ensure updates were still called
    });

    it('should only update failure count if max restarts reached', async () => {
      const failingJobData = { ...testJob, failures: maxRestarts }; // At max failures
      const mockFoundJob = { ...failingJobData, ...mockJobInstance };
      modelFindOneStub.resolves(mockFoundJob);
      instanceUpdateStub.resolves();

      await adapter.updateFailure(
        failingJobData.id,
        backOffMs,
        maxRestarts,
        safeJobs
      );

      expect(modelFindOneStub.calledOnce).to.be.true;
      expect(instanceUpdateStub.calledOnce).to.be.true; // Only called once for failure count
      expect(instanceUpdateStub.firstCall.args[0].failures).to.equal(
        failingJobData.failures + 1
      );
      expect(retryDelayStub.called).to.be.false;
    });

    it('should only update failure count if job is not safe to restart (and safe list provided)', async () => {
      const failingJobData = { ...testJob, name: 'unsafe-job', failures: 0 };
      const mockFoundJob = { ...failingJobData, ...mockJobInstance };
      modelFindOneStub.resolves(mockFoundJob);
      instanceUpdateStub.resolves();

      await adapter.updateFailure(
        failingJobData.id,
        backOffMs,
        maxRestarts,
        safeJobs
      );

      expect(modelFindOneStub.calledOnce).to.be.true;
      expect(instanceUpdateStub.calledOnce).to.be.true; // Only failure count update
      expect(retryDelayStub.called).to.be.false;
    });

    it('should schedule retry if job is safe to restart', async () => {
      const failingSafeJobData = { ...testJob, failures: 0 };
      const mockFoundJob = { ...failingSafeJobData, ...mockJobInstance };
      modelFindOneStub.resolves(mockFoundJob);
      instanceUpdateStub.resolves();

      await adapter.updateFailure(
        failingSafeJobData.id,
        backOffMs,
        maxRestarts,
        safeJobs
      );

      expect(instanceUpdateStub.calledTwice).to.be.true; // Failure count + reschedule
      expect(retryDelayStub.calledOnce).to.be.true;
    });

    it('should schedule retry if safeJobs list is empty', async () => {
      const failingJobData = { ...testJob, name: 'any-job', failures: 0 };
      const mockFoundJob = { ...failingJobData, ...mockJobInstance };
      modelFindOneStub.resolves(mockFoundJob);
      instanceUpdateStub.resolves();

      await adapter.updateFailure(
        failingJobData.id,
        backOffMs,
        maxRestarts,
        []
      ); // Empty safeJobs list

      expect(instanceUpdateStub.calledTwice).to.be.true; // Failure count + reschedule
      expect(retryDelayStub.calledOnce).to.be.true;
    });

    it('should do nothing if job is not found', async () => {
      modelFindOneStub.resolves(null);

      await adapter.updateFailure(
        'not-found-id',
        backOffMs,
        maxRestarts,
        safeJobs
      );

      expect(modelFindOneStub.calledOnce).to.be.true;
      expect(instanceUpdateStub.called).to.be.false;
      expect(retryDelayStub.called).to.be.false;
    });
  });

  describe('getPendingJobs', () => {
    const mockPendingResult = [
      { name: 'job-a', count: '2' },
      { name: 'job-b', count: '1' },
    ];
    const expectedPendingJobs: PendingJobs = { 'job-a': 2, 'job-b': 1 };
    const allJobs = ['job-a', 'job-b', 'job-c'];

    it('should call sequelize.query and format the result', async () => {
      queryStub.resolves(mockPendingResult);
      const result = await adapter.getPendingJobs(allJobs);

      expect(result).to.deep.equal(expectedPendingJobs);
      expect(queryStub.calledOnce).to.be.true;
      const queryOptions = queryStub.firstCall.args[1];
      expect(queryOptions.type).to.equal(Sequelize.QueryTypes.SELECT);
      expect(queryOptions.replacements).to.deep.equal({ jobNames: allJobs });
      expect(queryStub.firstCall.args[0]).to.include(
        'SELECT name, count(name) as count'
      );
      expect(queryStub.firstCall.args[0]).to.include('WHERE queued = false');
      expect(queryStub.firstCall.args[0]).to.include('GROUP BY 1');
    });

    it('should return empty object if no jobs are provided', async () => {
      const result = await adapter.getPendingJobs([]);
      expect(result).to.deep.equal({});
      expect(queryStub.called).to.be.false;
    });

    it('should return empty object if query returns no results', async () => {
      queryStub.resolves([]);
      const result = await adapter.getPendingJobs(allJobs);
      expect(result).to.deep.equal({});
      expect(queryStub.calledOnce).to.be.true;
    });
  });

  describe('getBlockedJobs', () => {
    const jobNames = ['blocked-job'];
    const blockedMins = 10;
    const mockBlockedResult: JobBase[] = [{ ...testJob, name: 'blocked-job' }];

    it('should call Job.findAll with correct where clause', async () => {
      modelFindAllStub.resolves(mockBlockedResult);
      const result = await adapter.getBlockedJobs(jobNames, blockedMins);

      expect(result).to.deep.equal(mockBlockedResult);
      expect(modelFindAllStub.calledOnce).to.be.true;
      const whereClause = modelFindAllStub.firstCall.args[0].where;
      expect(whereClause.queued).to.be.true;
      expect(whereClause.deletedAt).to.be.null;
      expect(whereClause.name[Sequelize.Op.in]).to.deep.equal(jobNames);
      expect(whereClause[Sequelize.Op.or]).to.exist;
      expect(whereClause.lastRunAt[Sequelize.Op.lte]).to.exist;
    });

    it('should return empty array if no job names provided', async () => {
      const result = await adapter.getBlockedJobs([], blockedMins);
      expect(result).to.deep.equal([]);
      expect(modelFindAllStub.called).to.be.false;
    });
  });

  describe('getLaggedJobs', () => {
    const jobNames = ['lagged-job'];
    const laggedMins = 6;
    const mockLaggedResult: JobBase[] = [{ ...testJob, name: 'lagged-job' }];

    it('should call Job.findAll with correct where clause', async () => {
      modelFindAllStub.resolves(mockLaggedResult);
      const result = await adapter.getLaggedJobs(jobNames, laggedMins);

      expect(result).to.deep.equal(mockLaggedResult);
      expect(modelFindAllStub.calledOnce).to.be.true;
      const whereClause = modelFindAllStub.firstCall.args[0].where;
      expect(whereClause.queued).to.be.true;
      expect(whereClause.lastRunAt[Sequelize.Op.lt]).to.exist;
      expect(whereClause.name[Sequelize.Op.in]).to.deep.equal(jobNames);
      expect(whereClause[Sequelize.Op.or]).to.exist; // Checks for lastFinishedAt condition
      expect(whereClause.acceptedAt[Sequelize.Op.lte]).to.exist;
    });

    it('should return empty array if no job names provided', async () => {
      const result = await adapter.getLaggedJobs([], laggedMins);
      expect(result).to.deep.equal([]);
      expect(modelFindAllStub.called).to.be.false;
    });
  });

  describe('disconnect', () => {
    it('should call sequelize.close', async () => {
      await adapter.disconnect();
      expect(closeStub.calledOnce).to.be.true;
    });
  });
});
