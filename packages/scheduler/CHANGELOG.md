# @ordermentum/scheduler

## 0.1.8

### Patch Changes

- de9b8e0: Backpressure concurrency slots when fetching jobs to process

## 0.1.7

### Patch Changes

- fb7680d: Concurrency management is made atomic

## 0.1.6

### Patch Changes

- 63a83e6: Reduce logging

## 0.1.5

### Patch Changes

- c082ed3: Scheduler - Fix concurrency issues under high load

## 0.1.4

### Patch Changes

- 8ceb083: Refactor scheduler tasks type

## 0.1.3

### Patch Changes

- a346d16: Update prisma adapter to return camelcased column names when using raw queries

## 0.1.2

### Patch Changes

- 811702d: Add lag metric to scheduler

## 0.1.1

### Patch Changes

- 33bd915: Add ORMs as dependencies instead of peer dependencies (packages/scheduler)

## 0.1.0

### Minor Changes

- ef4832a: Add @ordermentum/scheduler database job runner
