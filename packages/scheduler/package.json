{"name": "@ordermentum/scheduler", "version": "0.1.8", "description": "A unified scheduler library that works with both Sequelize and Prisma", "main": "build/index.js", "types": "build/index.d.ts", "files": ["build/*"], "scripts": {"lint": "yarn eslint 'src/**/*.{ts,js}'", "build:coverage": "nyc check-coverage --statements 70 --branches 60 --functions 60 --lines 70", "test": "NODE_ENV=test nyc npm run spec", "report": "./node_modules/.bin/nyc report --reporter=html", "spec": "mocha 'test/**/*.test.ts'", "spec:runner": "mocha", "build": "yarn clean && yarn tsc", "prepublish": "yarn run build && yarn spec", "prepare": "yarn run prisma generate --schema=./src/adapters/schemas/prisma/schema.prisma && yarn run build", "clean": "rm -rf build", "reporter": "nyc --reporter=html yarn run test", "typecheck": "tsc --noEmit", "format": "eslint --fix '**/*.{ts,js}'"}, "repository": {"type": "git", "url": "https://github.com/ordermentum/libs.git"}, "keywords": ["postgres", "job", "queue", "sequelize", "prisma", "scheduler"], "license": "MIT", "dependencies": {"@prisma/client": "^5.22.0", "prisma": "^5.22.0", "cron-parser": "^3.5.0", "moment-timezone": "^0.6.0", "pg": "^8.6.0", "rrule-rust": "^1.2.0", "sequelize": "^6.6.2", "typed-emitter": "^1.3.1", "uuid": "^8.3.2"}, "devDependencies": {"@types/chai": "4.3.20", "@types/chai-as-promised": "^7.1.5", "@types/mocha": "8.2.3", "@types/moment-timezone": "0.5.30", "@types/sinon": "10.0.20", "@types/sinon-chai": "^4.0.0", "@types/uuid": "8.3.4", "chai": "4.5.0", "chai-as-promised": "^7.1.1", "cross-env": "7.0.3", "mocha": "9.2.2", "nyc": "15.1.0", "sinon": "11.1.2", "sinon-chai": "3.7.0", "ts-node": "10.9.2", "typescript": "5.1.3"}}