import moment from 'moment-timezone';
import { PrismaClient, Prisma } from '@prisma/client';
import { DatabaseAdapter } from '../types/database-adapter.interface';
import { JobBase } from '../types/job-base.interface';
import { JobSet } from '../types/job-set.type';
import { PendingJobs } from '../types/pending-jobs.type';
import { computeNextRun, retryDelay } from '../utils';

/**
 * Prisma implementation of the DatabaseAdapter interface
 * @example
 * const prisma = new PrismaClient();
 * const adapter = new PrismaAdapter(prisma);
 * const scheduler = new JobScheduler({
 *   adapter,
 *   // other configuration options
 * });
 */
export class PrismaAdapter implements DatabaseAdapter {
  private client: PrismaClient;

  constructor(client: PrismaClient) {
    this.client = client;
  }

  /**
   * Fetch and enqueue jobs that are due to run
   */
  public async fetchAndEnqueueJobs(
    allJobs: string[],
    maxConcurrentJobs: number,
    namespace?: string
  ): Promise<JobSet[]> {
    const query = this.buildEnqueueJobsQuery(
      allJobs,
      maxConcurrentJobs,
      namespace
    );
    return this.client.$queryRaw<JobSet[]>(query);
  }

  /**
   * Build the SQL query for fetching and enqueueing jobs
   */
  private buildEnqueueJobsQuery(
    jobs: string[],
    maxConcurrentJobs: number,
    namespace?: string
  ): Prisma.Sql {
    return Prisma.sql`
      WITH updated as (
          UPDATE
          jobs
      SET
          queued = TRUE,
          last_run_at = CURRENT_TIMESTAMP
      WHERE
          id in (
              SELECT
                  id
              FROM
                  jobs
              WHERE
                  queued = false
                  AND name in (${Prisma.join(jobs)})
                  AND next_run_at < CURRENT_TIMESTAMP
                  AND deleted_at is null
                  ${
                    namespace
                      ? Prisma.sql`AND namespace = ${namespace}`
                      : Prisma.sql``
                  }
                  AND pg_try_advisory_xact_lock(
                      ('x' || translate(id :: text, '-', '')) :: bit(64) :: bigint
                  )
              LIMIT
                ${maxConcurrentJobs}
              FOR UPDATE
          ) 
        RETURNING *
      )
      SELECT
          priority,
          name,
          count(*)::int AS total,
          jsonb_agg(
              jsonb_build_object(
                  'id',
                  id,
                  'name',
                  NAME,
                  'data',
                  data,
                  'repeatInterval',
                  repeat_interval,
                  'lastFinishedAt',
                  last_finished_at,
                  'lastRunAt',
                  last_run_at,
                  'timezone',
                  timezone,
                  'nextRunAt',
                  next_run_at,
                  'failures',
                  failures
                  ${
                    namespace
                      ? Prisma.sql`,'namespace',coalesce(namespace, null)`
                      : Prisma.sql``
                  }
              )
          ) AS items
      FROM
          updated
      GROUP BY
          1,
          2
      ORDER BY
          1 desc,
          2;
    `;
  }

  /**
   * Reset a job for its next scheduled run
   */
  public async resetJob(job: JobBase, nextRunAt: string): Promise<void> {
    if (job.namespace) {
      await this.client.job.update({
        // @ts-expect-error Namespace is optional
        where: { id_namespace: { id: job.id, namespace: job.namespace } },
        data: { queued: false, nextRunAt, failures: 0 },
      });
    } else {
      await this.client.job.update({
        where: { id: job.id },
        data: { queued: false, nextRunAt, failures: 0 },
      });
    }
  }

  /**
   * Update a job to indicate it has started
   */
  public async updateStartTask(
    jobId: string,
    namespace?: string
  ): Promise<void> {
    if (namespace) {
      await this.client.job.update({
        // @ts-expect-error Namespace is optional
        where: { id_namespace: { id: jobId, namespace } },
        data: { acceptedAt: new Date() },
      });
    } else {
      await this.client.job.update({
        where: { id: jobId },
        data: { acceptedAt: new Date() },
      });
    }
  }

  /**
   * Update a job to indicate it has finished
   */
  public async updateFinishTask(
    jobId: string,
    namespace?: string
  ): Promise<void> {
    let job;
    if (namespace) {
      job = await this.client.job.findUnique({
        // @ts-expect-error Namespace is optional
        where: { id_namespace: { id: jobId, namespace } },
      });
    } else {
      job = await this.client.job.findUnique({
        where: { id: jobId },
      });
    }

    if (!job) {
      return;
    }

    if (!job.repeatInterval) {
      await this.client.job.delete({
        where: { id: job.id },
      });
      return;
    }

    const nextRunAt = computeNextRun(job.repeatInterval, {
      timezone: job.timezone,
    });

    const updateData = {
      queued: false,
      nextRunAt,
      lastFinishedAt: new Date(),
      failures: 0,
      failedAt: null,
    };

    if (namespace) {
      await this.client.job.update({
        // @ts-expect-error Namespace is optional
        where: { id_namespace: { id: jobId, namespace } },
        data: updateData,
      });
    } else {
      await this.client.job.update({
        where: { id: jobId },
        data: updateData,
      });
    }
  }

  /**
   * Update a job to indicate it has failed
   */
  public async updateFailure(
    jobId: string,
    backOffMs: number,
    maxRestartsOnFailure: number,
    jobsSafeToRestart: string[],
    namespace?: string
  ): Promise<void> {
    const jobQuery = namespace
      ? { id_namespace: { id: jobId, namespace } }
      : { id: jobId };

    const job = await this.client.job.findUnique({
      // @ts-expect-error Namespace is optional
      where: jobQuery,
    });

    if (!job) {
      return;
    }

    const updatedFailureCount = job.failures + 1;
    const currentTime = new Date();

    await this.client.job.update({
      // @ts-expect-error Namespace is optional
      where: jobQuery,
      data: {
        failures: updatedFailureCount,
        failedAt: currentTime,
      },
    });

    const isRestartAllowed = updatedFailureCount <= maxRestartsOnFailure;
    const isJobSafeToRestart =
      !jobsSafeToRestart.length || jobsSafeToRestart.includes(job.name);

    if (!isRestartAllowed || !isJobSafeToRestart) {
      return;
    }

    const backoffDuration = retryDelay(updatedFailureCount, backOffMs);
    const nextRunAt = moment()
      .tz(job.timezone)
      .add(backoffDuration, 'milliseconds')
      .toISOString();

    await this.client.job.update({
      // @ts-expect-error Namespace is optional
      where: jobQuery,
      data: {
        queued: false,
        nextRunAt,
      },
    });
  }

  /**
   * Get pending jobs (jobs that should have run but haven't been queued)
   */
  public async getPendingJobs(allJobs: string[]): Promise<PendingJobs> {
    if (!allJobs.length) {
      return {};
    }
    const query = Prisma.sql`
      SELECT name, count(name)::int from jobs
      WHERE queued = false AND next_run_at < CURRENT_TIMESTAMP
      AND name IN (${Prisma.join(allJobs)})
      AND deleted_at is NULL
      GROUP BY 1
    `;
    const pendingJobs = await this.client.$queryRaw<
      { name: string; count: string }[]
    >(query);

    return Object.fromEntries(pendingJobs.map(job => [job.name, +job.count]));
  }

  /**
   * Get blocked jobs (queued jobs that haven't been accepted)
   */
  public async getBlockedJobs(
    jobNames: string[],
    blockedInMinutes: number
  ): Promise<JobBase[]> {
    if (!jobNames.length) {
      return [];
    }

    const query = Prisma.sql`
      SELECT
        id,
        created_at as "createdAt",
        updated_at as "updatedAt",
        deleted_at as "deletedAt",
        name,
        data,
        last_finished_at as "lastFinishedAt",
        last_run_at as "lastRunAt",
        next_run_at as "nextRunAt",
        accepted_at as "acceptedAt",
        repeat_interval as "repeatInterval",
        type,
        priority,
        fail_reason as "failReason",
        failed_at as "failedAt",
        queued,
        timezone,
        failures
      FROM jobs
      WHERE queued = true AND deleted_at is NULL AND
      (accepted_at < last_run_at OR accepted_at IS NULL)
      AND last_run_at <= CURRENT_TIMESTAMP - ${`${blockedInMinutes} minutes`}::TEXT::INTERVAL
      AND name IN (${Prisma.join(jobNames)})
    `;

    return this.client.$queryRaw<JobBase[]>(query);
  }

  /**
   * Get lagged jobs (jobs that started but haven't finished in expected time)
   */
  public async getLaggedJobs(
    jobNames: string[],
    lagInMinutes: number
  ): Promise<JobBase[]> {
    if (!jobNames.length) {
      return [];
    }

    const query = Prisma.sql`
      SELECT
        id,
        created_at as "createdAt",
        updated_at as "updatedAt",
        deleted_at as "deletedAt",
        name,
        data,
        last_finished_at as "lastFinishedAt",
        last_run_at as "lastRunAt",
        next_run_at as "nextRunAt",
        accepted_at as "acceptedAt",
        repeat_interval as "repeatInterval",
        type,
        priority,
        fail_reason as "failReason",
        failed_at as "failedAt",
        queued,
        timezone,
        failures
      FROM jobs
      WHERE queued = true AND last_run_at < accepted_at
      AND (last_finished_at < CURRENT_TIMESTAMP - ${`${lagInMinutes} minutes`}::TEXT::INTERVAL
      OR last_finished_at is NULL)
      AND accepted_at <= CURRENT_TIMESTAMP - ${`${lagInMinutes} minutes`}::TEXT::INTERVAL
      AND deleted_at is NULL
      AND name IN (${Prisma.join(jobNames)})
      `;
    return this.client.$queryRaw<JobBase[]>(query);
  }

  /**
   * Close the database connection
   */
  public async disconnect(): Promise<void> {
    await this.client.$disconnect();
  }
}
