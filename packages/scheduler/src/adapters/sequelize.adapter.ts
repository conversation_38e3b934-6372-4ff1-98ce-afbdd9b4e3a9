import moment from 'moment-timezone';
import { Sequelize, Op, QueryTypes, DataTypes } from 'sequelize';
import { DatabaseAdapter } from '../types/database-adapter.interface';
import { JobBase } from '../types/job-base.interface';
import { JobSet } from '../types/job-set.type';
import { PendingJobs } from '../types/pending-jobs.type';
import { JobModel } from '../types/job-model.class';
import { computeNextRun, retryDelay } from '../utils';

/**
 * Sequelize implementation of the DatabaseAdapter interface
 * @example
 * const adapter = new SequelizeAdapter('postgres://user:password@localhost:5432/dbname');
 * const scheduler = new JobScheduler({
 *   adapter,
 *   // other configuration options
 * });
 */
export class SequelizeAdapter implements DatabaseAdapter {
  private sequelize: Sequelize;

  private Job: typeof JobModel;

  private enqueueJobsQuery: (namespace?: string) => string;

  constructor(
    databaseUri: string,
    buildQueryFn?: (namespace?: string) => string
  ) {
    this.sequelize = new Sequelize(databaseUri, {
      dialect: 'postgres',
      logging: false,
    });

    this.Job = this.initJobModel();

    this.enqueueJobsQuery = buildQueryFn || this.defaultEnqueueJobsQuery;
  }

  /**
   * Initialize the Job model for Sequelize
   */
  private initJobModel() {
    const Job = JobModel.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
          allowNull: false,
        },
        name: { type: DataTypes.STRING, allowNull: false },
        data: { type: DataTypes.JSONB, defaultValue: {} },
        lastFinishedAt: { type: DataTypes.DATE, field: 'last_finished_at' },
        lastRunAt: { type: DataTypes.DATE, field: 'last_run_at' },
        lastModifiedBy: { type: DataTypes.STRING, field: 'last_modified_by' },
        nextRunAt: { type: DataTypes.DATE, field: 'next_run_at' },
        acceptedAt: {
          type: DataTypes.DATE,
          field: 'accepted_at',
          allowNull: true,
        },
        repeatInterval: { type: DataTypes.STRING, field: 'repeat_interval' },
        type: { type: DataTypes.STRING },
        priority: { type: DataTypes.INTEGER, defaultValue: 1 },
        failReason: {
          type: DataTypes.JSONB,
          field: 'fail_reason',
          defaultValue: {},
        },
        failedAt: { type: DataTypes.DATE, field: 'failed_at' },
        queued: { type: DataTypes.BOOLEAN, defaultValue: false },
        timezone: {
          type: DataTypes.STRING,
          defaultValue: 'UTC',
          allowNull: false,
        },
        failures: {
          defaultValue: 0,
          allowNull: false,
          type: DataTypes.INTEGER,
        },
        namespace: {
          type: DataTypes.STRING,
          allowNull: true,
          field: 'namespace',
        },
        createdAt: { type: DataTypes.DATE, field: 'created_at' },
        updatedAt: { type: DataTypes.DATE, field: 'updated_at' },
        deletedAt: { type: DataTypes.DATE, field: 'deleted_at' },
      },
      {
        sequelize: this.sequelize,
        modelName: 'Job',
        tableName: 'jobs',
        underscored: true,
        timestamps: true,
        paranoid: true,
      }
    );

    return Job;
  }

  /**
   * Default SQL query for fetching and enqueueing jobs
   */
  private defaultEnqueueJobsQuery = (namespace?: string) => `
    WITH updated as (
        UPDATE
          jobs
      SET
          queued = TRUE,
          last_run_at = CURRENT_TIMESTAMP
      WHERE
          id in (
              SELECT
                  id
              FROM
                  jobs
              WHERE
                  queued = false
                  AND name in (:jobs)
                  AND next_run_at < CURRENT_TIMESTAMP
                  AND deleted_at is null
                  ${namespace ? `AND namespace = :namespace` : ''}
                  AND pg_try_advisory_xact_lock(
                      ('x' || translate(id :: text, '-', '')) :: bit(64) :: bigint
                  )
              LIMIT
                :jobEnqueueLimit 
              FOR UPDATE
          ) 
        RETURNING *
    )
    SELECT
        priority,
        name,
        count(*)::int AS total,
        jsonb_agg(
            jsonb_build_object(
                'id',
                id,
                'name',
                NAME,
                'data',
                data,
                'repeatInterval',
                repeat_interval,
                'lastFinishedAt',
                last_finished_at,
                'lastRunAt',
                last_run_at,
                'timezone',
                timezone,
                'nextRunAt',
                next_run_at,
                'failures',
                failures
                ${namespace ? ",'namespace',namespace" : ''}
            )
        ) AS items
    FROM
        updated
    GROUP BY
        1,
        2
    ORDER BY
        1 desc,
        2;
  `;

  /**
   * Fetch and enqueue jobs that are due to run
   */
  public async fetchAndEnqueueJobs(
    allJobs: string[],
    maxConcurrentJobs: number,
    namespace?: string
  ): Promise<JobSet[]> {
    return this.sequelize.query(this.enqueueJobsQuery(namespace), {
      type: QueryTypes.SELECT,
      replacements: {
        jobEnqueueLimit: maxConcurrentJobs,
        jobs: allJobs,
        namespace,
      },
    });
  }

  /**
   * Reset a job for its next scheduled run
   */
  public async resetJob(job: JobBase, nextRunAt: string): Promise<void> {
    await this.Job.update(
      {
        queued: false,
        nextRunAt: moment(nextRunAt).toDate(),
      },
      {
        where: {
          id: job.id,
          ...(job.namespace ? { namespace: job.namespace } : {}),
        },
      }
    );
  }

  /**
   * Update a job to indicate it has started
   */
  public async updateStartTask(
    jobId: string,
    namespace?: string
  ): Promise<void> {
    await this.Job.update(
      {
        acceptedAt: moment().toDate(),
      },
      {
        where: {
          id: jobId,
          ...(namespace ? { namespace } : {}),
        },
      }
    );
  }

  /**
   * Update a job to indicate it has finished
   */
  public async updateFinishTask(
    jobId: string,
    namespace?: string
  ): Promise<void> {
    const job = await this.Job.findOne({
      where: {
        id: jobId,
        ...(namespace ? { namespace } : {}),
      },
      attributes: { exclude: ['namespace'] },
    });

    if (!job) {
      return;
    }

    if (!job.repeatInterval) {
      await job.destroy();
      return;
    }

    const nextRunAt = computeNextRun(job.repeatInterval, {
      timezone: job.timezone,
    });

    await job.update({
      queued: false,
      nextRunAt: moment(nextRunAt).toDate(),
      lastFinishedAt: moment().toDate(),
      failures: 0,
      failedAt: null,
    });
  }

  /**
   * Update a job to indicate it has failed
   */
  public async updateFailure(
    jobId: string,
    backOffMs: number,
    maxRestartsOnFailure: number,
    jobsSafeToRestart: string[],
    namespace?: string
  ): Promise<void> {
    const job = await this.Job.findOne({
      where: {
        id: jobId,
        ...(namespace ? { namespace } : {}),
      },
      attributes: { exclude: ['namespace'] },
    });

    if (!job) {
      return;
    }

    // Update failure count and timestamp
    await job.update({
      failures: job.failures + 1,
      failedAt: moment().toISOString(),
    });

    if (
      job.failures < maxRestartsOnFailure &&
      (!jobsSafeToRestart.length || jobsSafeToRestart.includes(job.name))
    ) {
      const backoff = retryDelay(job.failures, backOffMs);
      const nextRunAt = moment()
        .tz(job.timezone)
        .add(backoff, 'milliseconds')
        .toISOString();

      await job.update({
        queued: false,
        nextRunAt: moment(nextRunAt).toDate(),
      });
    }
  }

  /**
   * Get pending jobs (jobs that should have run but haven't been queued)
   */
  public async getPendingJobs(allJobs: string[]): Promise<PendingJobs> {
    if (!allJobs.length) {
      return {};
    }

    const result = await this.sequelize.query<{
      name: string;
      count: string;
    }>(
      `
      SELECT name, count(name) as count
      FROM jobs
      WHERE queued = false 
        AND next_run_at < CURRENT_TIMESTAMP
        AND name IN (:jobNames)
        AND deleted_at is NULL
      GROUP BY 1
      `,
      {
        type: QueryTypes.SELECT,
        replacements: {
          jobNames: allJobs,
        },
      }
    );

    const pendingJobs = {} as PendingJobs;

    for (const job of result) {
      pendingJobs[job.name] = Number(job.count);
    }

    return pendingJobs;
  }

  /**
   * Get blocked jobs (queued jobs that haven't been accepted)
   */
  public async getBlockedJobs(
    jobNames: string[],
    blockedInMinutes: number
  ): Promise<JobBase[]> {
    if (!jobNames.length) {
      return [];
    }

    return this.Job.findAll({
      where: {
        queued: true,
        deletedAt: null,
        [Op.or]: [
          { acceptedAt: null },
          { acceptedAt: { [Op.lt]: Sequelize.col('last_run_at') } },
        ],
        lastRunAt: {
          [Op.lte]: Sequelize.literal(
            `CURRENT_TIMESTAMP - INTERVAL '${blockedInMinutes} minutes'`
          ),
        },
        name: { [Op.in]: jobNames },
      },
      raw: true,
      attributes: { exclude: ['namespace'] },
    });
  }

  /**
   * Get lagged jobs (jobs that started but haven't finished in expected time)
   */
  public async getLaggedJobs(
    jobNames: string[],
    lagInMinutes: number
  ): Promise<JobBase[]> {
    if (!jobNames.length) {
      return [];
    }

    return this.Job.findAll({
      where: {
        queued: true,
        lastRunAt: { [Op.lt]: Sequelize.col('accepted_at') },
        [Op.or]: [
          { lastFinishedAt: null },
          {
            lastFinishedAt: {
              [Op.lte]: Sequelize.literal(
                `CURRENT_TIMESTAMP - INTERVAL '${lagInMinutes} minutes'`
              ),
            },
          },
        ],
        acceptedAt: {
          [Op.lte]: Sequelize.literal(
            `CURRENT_TIMESTAMP - INTERVAL '${lagInMinutes} minutes'`
          ),
        },
        name: { [Op.in]: jobNames },
      },
      raw: true,
      attributes: { exclude: ['namespace'] },
    });
  }

  /**
   * Close the database connection
   */
  public async disconnect(): Promise<void> {
    await this.sequelize.close();
  }
}
