import { EventEmitter } from 'events';
import TypedEmitter from 'typed-emitter';
import { SchedulerError } from './errors/scheduler-errors';
import { JobSchedulerConfig } from './types/job-scheduler-config.interface';
import {
  DEFAULT_RUN_INTERVAL,
  DEFAULT_BACKOFF,
  DEFAULT_MAX_RESTARTS_ON_FAILURE,
} from './types/constants';
import { GenericLogger } from './types/generic-logger.interface';
import { MetricsClient } from './types/metrics-client.interface';
import { DatabaseAdapter } from './types/database-adapter.interface';
import { Events } from './types/events.interface';
import { TaskMap } from './types/task.type';

// Import Components
import { ConcurrencyManager } from './components/concurrency-manager';
import { JobExecutor } from './components/job-executor';
import { JobFetcher } from './components/job-fetcher';
import { HeartbeatManager } from './components/heartbeat-manager';
import { LifecycleManager } from './components/lifecycle-manager';
import { MaintenanceManager } from './components/maintenance-manager';
import { SchedulerLoop } from './components/scheduler-loop';

/**
 * @example
 * import { JobScheduler, PrismaAdapter } from '@ordermentum/scheduler';
 * import { PrismaClient } from '@prisma/client';
 * import { createLogger } from 'bunyan';
 *
 * const prisma = new PrismaClient();
 * const adapter = new PrismaAdapter(prisma);
 * const logger = createLogger({ name: 'scheduler' });
 *
 * const scheduler = new JobScheduler({
 *   adapter,
 *   logger,
 *   service: 'my-service',
 *   tasks: {
 *     myTask: async (job, context) => {
 *       // Task implementation
 *     },
 *   },
 *   // Optional configurations
 *   runInterval: 60000,
 *   maxConcurrentJobs: 10,
 *   namespace: 'my-namespace',
 *   metricsClient: myMetricsClient,
 *   lag: 5000,
 *   blockedDuration: 300000,
 *   maxRestartsOnFailure: 3,
 *   backOffMs: 1000,
 *   jobsSafeToRestart: ['myTask'],
 *   healthCheckTimeout: 60000,
 * });
 *
 * await scheduler.runScheduledJobs();
 */
export class JobScheduler {
  private readonly logger: GenericLogger;

  private readonly metrics: MetricsClient;

  private readonly adapter: DatabaseAdapter;

  private readonly events: TypedEmitter<Events>;

  private readonly tasks: TaskMap;

  private readonly service: string;

  private readonly allJobs: string[];

  private readonly config: JobSchedulerConfig;

  // Components
  private readonly concurrencyManager: ConcurrencyManager;

  private readonly jobExecutor: JobExecutor;

  private readonly jobFetcher: JobFetcher;

  private readonly heartbeatManager: HeartbeatManager;

  private readonly lifecycleManager: LifecycleManager;

  private readonly maintenanceManager: MaintenanceManager;

  private readonly schedulerLoop: SchedulerLoop;

  constructor(config: JobSchedulerConfig) {
    this.config = config;
    this.logger = config.logger.child({
      module: 'JobScheduler',
    });
    this.metrics = config.metrics;
    this.adapter = config.adapter;
    this.events = config.events ?? (new EventEmitter() as TypedEmitter<Events>);
    this.tasks = config.tasks;
    this.service = config.service;

    const maxConcurrentJobs = config.maxConcurrentJobs ?? 4;
    const defaultRunInterval =
      config.defaultRunInterval ?? DEFAULT_RUN_INTERVAL;
    const healthCheckTimeout = 60 * 15 * 1000; // 15 minutes

    // Calculate all known job names
    this.allJobs = Array.from(
      new Set([
        ...Object.keys(config.tasks),
        ...Object.keys(config.jobsCustomRestart),
        ...config.jobsSafeToRestart,
        ...config.jobsRiskyToRestart,
      ])
    );

    // Instantiate components with dependencies
    this.lifecycleManager = new LifecycleManager(this.logger);
    this.concurrencyManager = new ConcurrencyManager(
      maxConcurrentJobs,
      this.logger
    );
    this.heartbeatManager = new HeartbeatManager(
      this.logger,
      this.metrics,
      healthCheckTimeout,
      this.service
    );

    this.jobExecutor = new JobExecutor(
      this.adapter,
      this.logger,
      this.metrics,
      this.events,
      {
        backOffMs: config.backOffMs ?? DEFAULT_BACKOFF,
        maxRestartsOnFailure:
          config.maxRestartsOnFailure ?? DEFAULT_MAX_RESTARTS_ON_FAILURE,
        jobsSafeToRestart: config.jobsSafeToRestart,
        namespace: config.namespace,
        service: this.service,
      }
    );

    this.jobFetcher = new JobFetcher(
      this.adapter,
      this.logger,
      this.metrics,
      this.concurrencyManager,
      {
        allJobs: this.allJobs,
        namespace: config.namespace,
        service: this.service,
      }
    );

    this.maintenanceManager = new MaintenanceManager(
      config,
      this.adapter,
      this.logger,
      this.metrics,
      this.events
    );

    this.schedulerLoop = new SchedulerLoop(
      this.jobFetcher,
      this.jobExecutor,
      this.concurrencyManager,
      this.heartbeatManager,
      this.lifecycleManager,
      this.tasks,
      this.logger,
      this.metrics,
      { defaultRunInterval, service: this.service }
    );

    this.logger.info('JobScheduler: Components instantiated.');
  }

  /**
   * Initializes the scheduler, starts maintenance tasks, but does not start the main loop.
   * @private Internal use, called by runScheduledJobs.
   * @throws {SchedulerError} if initialization fails.
   */
  private async init(): Promise<void> {
    if (this.lifecycleManager.getIsInitialized()) {
      this.logger.debug('JobScheduler: Already initialized.');
      return;
    }

    this.logger.info('JobScheduler: Initializing...');

    try {
      // Start maintenance task first
      await this.maintenanceManager.start();
      this.lifecycleManager.setInitialized(true);
      this.logger.info('JobScheduler: Initialization complete.');
    } catch (err) {
      this.lifecycleManager.setInitialized(false); // Ensure state is correct on failure
      const initError = new SchedulerError(
        'Failed to initialize scheduler',
        err instanceof Error ? err : new Error(String(err))
      );
      this.logger.error({ err, initError }, initError.message);
      throw initError; // Re-throw for the caller (runScheduledJobs)
    }
  }

  /**
   * Starts the main job processing loop.
   * Initializes the scheduler if not already done.
   * @param waitTime Optional interval in milliseconds for the scheduling loop. Defaults to `defaultRunInterval`.
   * @throws {SchedulerError} if starting fails (e.g., due to initialization error).
   */
  public async runScheduledJobs(waitTime?: number): Promise<void> {
    if (this.lifecycleManager.getIsExiting()) {
      this.logger.warn('JobScheduler: Cannot start, scheduler is terminating.');
      return;
    }

    if (this.schedulerLoop.isRunning()) {
      this.logger.warn(
        'JobScheduler: runScheduledJobs called but loop is already running.'
      );
      return;
    }

    try {
      await this.init(); // Ensure initialized before starting loop
      const interval =
        waitTime ?? this.config.defaultRunInterval ?? DEFAULT_RUN_INTERVAL;
      this.schedulerLoop.start(interval);
    } catch (err) {
      // Error during init is already logged, just reformat for start context
      const startError = new SchedulerError(
        'Failed to start scheduler due to initialization error',
        err instanceof Error ? err : new Error(String(err))
      );
      this.logger.error({ err, startError }, startError.message);
      throw startError;
    }
  }

  /**
   * Pauses the scheduler, preventing new job cycles from starting.
   * Does not stop jobs already in progress.
   */
  public async pause(): Promise<void> {
    this.lifecycleManager.pause();
    // No need to interact with the loop directly, it checks the flag
  }

  /**
   * Resumes the scheduler if it was paused.
   */
  public async resume(): Promise<void> {
    this.lifecycleManager.resume();
  }

  /**
   * Performs a health check on the scheduler.
   * @returns {Promise<boolean>} True if healthy.
   * @throws {SchedulerError} if the scheduler is deemed unhealthy.
   */
  public async healthCheck(): Promise<boolean> {
    // A simple check might just rely on the heartbeat
    // More complex checks could involve adapter health, etc.
    if (this.concurrencyManager.getActiveCount() > 0) {
      this.logger.debug(
        'HealthCheck: Considered healthy because jobs are processing.'
      );
      this.heartbeatManager.beat(); // Processing implies activity
      return true;
    }
    // Throws SchedulerError if unhealthy
    return this.heartbeatManager.checkHealth();
  }

  /**
   * Gracefully terminates the scheduler.
   * Stops the scheduling loop, stops maintenance, waits for running jobs (with timeout),
   * and disconnects the adapter.
   */
  public async terminate(): Promise<void> {
    if (this.lifecycleManager.getIsExiting()) {
      this.logger.warn('JobScheduler: Termination already in progress.');
      return;
    }

    this.logger.info('JobScheduler: Starting termination...');
    const startTime = Date.now();
    this.lifecycleManager.signalExit(); // Signal exit and pause

    try {
      // 1. Stop the main loop immediately to prevent new cycles
      this.schedulerLoop.stop();

      // 2. Stop maintenance task to prevent restarts/new maintenance jobs
      await this.maintenanceManager.stop();

      // 3. Wait for currently running jobs to complete (with timeout)
      const jobsCompleted = await this.concurrencyManager.waitForRunningJobs(
        30000 // 30s timeout
      );
      if (!jobsCompleted) {
        this.logger.warn(
          'JobScheduler: Some jobs may not have completed before termination timeout.'
        );
      }

      // 4. Clean up any remaining state tracked by concurrency manager
      this.concurrencyManager.clearRunningJobs();

      // 5. Disconnect the adapter
      try {
        await this.adapter.disconnect();
        this.logger.info('JobScheduler: Database adapter disconnected.');
      } catch (err) {
        this.logger.error(
          { err },
          'JobScheduler: Error disconnecting database adapter during termination.'
        );
        // Continue termination despite adapter error
      }

      // 6. Reset internal state managers
      this.lifecycleManager.reset();
      this.heartbeatManager.reset();
      // loop and maintenance are already stopped/cleared

      this.logger.info(
        { durationMs: Date.now() - startTime },
        'JobScheduler: Termination complete.'
      );
    } catch (err) {
      const terminationError = new SchedulerError(
        'Error during scheduler termination process',
        err instanceof Error ? err : new Error(String(err))
      );
      this.logger.error({ err }, terminationError.message);
      this.emitMetric('scheduler.termination.error', 1);
      throw terminationError; // Re-throw critical termination failure
    } finally {
      // Ensure state reflects termination even if errors occurred
      if (!this.lifecycleManager.getIsExiting()) {
        this.lifecycleManager.signalExit(); // Ensure exit state is set
      }
      this.lifecycleManager.setInitialized(false);
    }
  }

  // --- Helper to emit metrics ---
  private emitMetric(
    name: string,
    value: number,
    tags?: Record<string, string | number>
  ): void {
    this.metrics.increment(name, value, { ...tags, service: this.service });
  }
}

// Note: Type exports are handled by index.ts
