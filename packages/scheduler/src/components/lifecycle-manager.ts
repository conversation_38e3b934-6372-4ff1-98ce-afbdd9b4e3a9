import { GenericLogger } from '../types/generic-logger.interface';

/**
 * Manages the lifecycle state of the scheduler (paused, exiting, initialized). This class is responsible for managing the lifecycle state of the scheduler, including pausing, resuming, signaling exit, and resetting the state.
 * - Provides methods to pause, resume, signal exit, and reset the state.
 * - Tracks the state of the scheduler (paused, exiting, initialized).
 *
 */
export class LifecycleManager {
  private isPaused = false;

  private isExiting = false;

  private isInitialized = false;

  constructor(private readonly logger: GenericLogger) {
    // Constructor using parameter properties
  }

  public pause(): void {
    this.isPaused = true;
    this.logger.info('LifecycleManager: Scheduler paused.');
  }

  public resume(): void {
    this.isPaused = false;
    this.logger.info('LifecycleManager: Scheduler resumed.');
  }

  public signalExit(): void {
    if (this.isExiting) {
      return;
    }
    this.isExiting = true;
    this.isPaused = true; // Ensure no new work starts during termination
    this.logger.info('LifecycleManager: Termination signaled.');
  }

  public setInitialized(status: boolean): void {
    this.isInitialized = status;
    this.logger.info(
      `LifecycleManager: Scheduler ${
        status ? 'initialized' : 'de-initialized'
      }.`
    );
  }

  public reset(): void {
    this.isPaused = false;
    this.isExiting = false;
    this.isInitialized = false;
    this.logger.info('LifecycleManager: State reset.');
  }

  public getIsPaused(): boolean {
    return this.isPaused;
  }

  public getIsExiting(): boolean {
    return this.isExiting;
  }

  public getIsInitialized(): boolean {
    return this.isInitialized;
  }
}
