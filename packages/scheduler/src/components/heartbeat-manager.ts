import { SchedulerError } from '../errors/scheduler-errors';
import { GenericLogger } from '../types/generic-logger.interface';
import { MetricsClient } from '../types/metrics-client.interface';
import { isHealthy } from '../utils';

/**
 * Manages the scheduler's heartbeat and health status. This class is responsible for tracking the last heartbeat timestamp and checking the health status based on the last heartbeat.
 * - Tracks the last heartbeat timestamp.
 * - Provides methods to update the heartbeat and check the health status.
 */
export class HeartbeatManager {
  private lastHeartbeat: number;

  constructor(
    private readonly logger: GenericLogger,
    private readonly metrics: MetricsClient,
    private readonly healthCheckTimeoutMs: number,
    private readonly service: string
  ) {
    this.lastHeartbeat = Date.now();
  }

  /**
   * Updates the last heartbeat timestamp.
   */
  public beat(): void {
    this.lastHeartbeat = Math.max(this.lastHeartbeat, Date.now());
    this.emitMetric('scheduler.heartbeat', 1);
    this.logger.debug('HeartbeatManager: Beat updated.');
  }

  /**
   * Checks if the scheduler is healthy based on the last heartbeat.
   * @throws {SchedulerError} if the health check fails.
   */
  public checkHealth(): boolean {
    const healthy = isHealthy(this.lastHeartbeat, this.healthCheckTimeoutMs);
    const timeSinceHeartbeat = Date.now() - this.lastHeartbeat;

    this.emitMetric('scheduler.health_check', 1, {
      status: healthy ? 'healthy' : 'unhealthy',
    });
    this.emitGauge('scheduler.heartbeat.age', timeSinceHeartbeat);

    if (!healthy) {
      const error = new SchedulerError(
        `Scheduler not healthy (${this.healthCheckTimeoutMs}ms timeout exceeded)`
      );
      this.logger.error(
        {
          error,
          lastHeartbeat: new Date(this.lastHeartbeat).toISOString(),
          timeSinceHeartbeat,
        },
        'HeartbeatManager: Health check failed.'
      );
      throw error;
    }
    this.logger.debug('HeartbeatManager: Health check passed.');
    return true;
  }

  public getLastHeartbeat(): number {
    return this.lastHeartbeat;
  }

  public reset(): void {
    this.lastHeartbeat = 0;
    this.logger.debug('HeartbeatManager: Heartbeat reset.');
  }

  private emitMetric(
    name: string,
    value: number,
    tags?: Record<string, string | number>
  ): void {
    this.metrics.increment(name, value, { ...tags, service: this.service });
  }

  private emitGauge(
    name: string,
    value: number,
    tags?: Record<string, string | number>
  ): void {
    this.metrics.gauge(name, value, { ...tags, service: this.service });
  }
}
