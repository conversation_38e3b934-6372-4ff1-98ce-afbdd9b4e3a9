import TypedEmitter from 'typed-emitter';
import { Events } from '../types/events.interface';
import { DatabaseAdapter } from '../types/database-adapter.interface';
import { GenericLogger } from '../types/generic-logger.interface';
import { JobBase as Job } from '../types/job-base.interface';
import { MetricsClient } from '../types/metrics-client.interface';
import { JobExecutionError } from '../errors/scheduler-errors';
import { TaskHandler } from '../types/task.type';

/**
 * Handles the execution of a single job. This class is responsible for executing a job task, handling updates, metrics, and errors.
 * - Executes a job task.
 * - Handles updates to the job status.
 */
export class JobExecutor {
  constructor(
    private readonly adapter: DatabaseAdapter,
    private readonly logger: GenericLogger,
    private readonly metrics: MetricsClient,
    private readonly events: TypedEmitter<Events>,
    private readonly config: {
      backOffMs: number;
      maxRestartsOnFailure: number;
      jobsSafeToRestart: string[];
      namespace?: string;
      service: string;
    }
  ) {
    // Constructor using parameter properties
  }

  /**
   * Executes a job task, handling updates, metrics, and errors.
   */
  public async execute(
    jobName: string,
    item: Job,
    task: TaskHandler
  ): Promise<void> {
    const jobId = item.id;
    const startTime = Date.now();
    this.logger.debug(
      { jobId, jobName },
      'JobExecutor: Starting job execution'
    );

    try {
      if (item.nextRunAt) {
        const scheduledTime = new Date(item.nextRunAt).getTime();
        const currentTime = Date.now();
        // Calculate the lag between scheduled time and actual execution
        // Negative values = Good, Positive values = Bad
        // Negative values mean the job was picked up before it was scheduled to run
        // Positive values mean the job was picked up after it was scheduled to run
        const lagTime = (currentTime - scheduledTime) / 1000;

        this.emitTiming('scheduler.job.execution_lag_seconds', lagTime, {
          job_type: jobName,
        });

        this.logger.debug(
          { jobId, jobName, lagSeconds: lagTime },
          'JobExecutor: Job execution lag measured'
        );
      }
      await this.adapter.updateStartTask(jobId, this.config.namespace);
      this.emitMetric('scheduler.job.started', 1, { job_type: jobName });

      await task(item.data, { job: item });

      await this.adapter.updateFinishTask(jobId, this.config.namespace);
      const duration = Date.now() - startTime;
      this.emitTiming('scheduler.job.duration', duration, {
        job_type: jobName,
        status: 'success',
      });
      this.emitMetric('scheduler.job.completed', 1, {
        job_type: jobName,
        status: 'success',
      });
      this.events.emit('duration', item, duration / 1000, true);
      this.logger.debug(
        { jobId, jobName, durationMs: duration },
        'JobExecutor: Job completed successfully'
      );
    } catch (err) {
      const jobError = new JobExecutionError(
        `Task ${jobName} failed during execution`,
        jobName,
        jobId,
        err instanceof Error ? err : new Error(String(err))
      );
      this.logger.error(
        {
          err,
          jobError,
          jobId,
          jobName,
          data: item.data,
        },
        `JobExecutor: Job execution failed: ${jobName}`
      );

      const duration = Date.now() - startTime;
      this.emitTiming('scheduler.job.duration', duration, {
        job_type: jobName,
        status: 'failure',
      });
      this.emitMetric('scheduler.job.completed', 1, {
        job_type: jobName,
        status: 'failure',
      });

      try {
        await this.adapter.updateFailure(
          jobId,
          this.config.backOffMs,
          this.config.maxRestartsOnFailure,
          this.config.jobsSafeToRestart,
          this.config.namespace
        );
      } catch (updateError) {
        this.logger.error(
          { err: updateError, jobId, jobName },
          'JobExecutor: Failed to update job failure status'
        );
        this.emitMetric('scheduler.job.update_failure_error', 1);
      }

      this.events.emit('duration', item, duration / 1000, false);
      // Do not re-throw here, failure is handled via DB update and metrics/logging
    }
  }

  /**
   * Defers a job by resetting it to run in the next cycle.
   * Used when hitting concurrency limits to ensure the job gets retried.
   */
  public async defer(jobName: string, job: Job): Promise<boolean> {
    try {
      const now = new Date().toISOString();
      await this.adapter.resetJob(job, now);
      this.emitMetric('scheduler.job.deferred', 1, { job_type: jobName });
      this.logger.debug(
        { jobId: job.id },
        'JobExecutor: Job deferred to next cycle'
      );
      return true;
    } catch (err) {
      const jobError = new JobExecutionError(
        `Failed to defer job ${jobName}`,
        jobName,
        job.id,
        err instanceof Error ? err : new Error(String(err))
      );
      this.logger.error(
        { err, jobId: job.id, jobError, jobName },
        'JobExecutor: Failed to defer job'
      );
      this.emitMetric('scheduler.job.deferred.error', 1, { job_type: jobName });
      return false;
    }
  }

  private emitMetric(
    name: string,
    value: number,
    tags?: Record<string, string | number>
  ): void {
    this.metrics.increment(name, value, {
      ...tags,
      service: this.config.service,
    });
  }

  private emitTiming(
    name: string,
    value: number,
    tags?: Record<string, string | number>
  ): void {
    this.metrics.timing(name, value, {
      ...tags,
      service: this.config.service,
    });
  }
}
