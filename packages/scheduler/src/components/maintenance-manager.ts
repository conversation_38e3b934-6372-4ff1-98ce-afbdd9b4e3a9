import TypedEventEmitter from 'typed-emitter';
import { SchedulerError } from '../errors/scheduler-errors';
import { JobSchedulerConfig } from '../types/job-scheduler-config.interface';
import { DEFAULT_BLOCKED_DURATION, DEFAULT_LAG } from '../types/constants';
import { DatabaseAdapter } from '../types/database-adapter.interface';
import { GenericLogger } from '../types/generic-logger.interface';
import { MetricsClient } from '../types/metrics-client.interface';
import { Events } from '../types/events.interface';
import { Maintenance } from '../maintenance';

/**
 * Manages the creation and lifecycle of the Maintenance task. This class is responsible for starting and stopping the maintenance task.
 * - Implements the IMaintenance interface.
 * - Provides methods to start and stop the maintenance task.
 * - Tracks the state of the maintenance task.
 */
export class MaintenanceManager {
  private maintenance?: Maintenance;

  constructor(
    private readonly config: JobSchedulerConfig, // Pass full config for simplicity
    private readonly adapter: DatabaseAdapter,
    private readonly logger: GenericLogger,
    private readonly metrics: MetricsClient,
    private readonly events: TypedEventEmitter<Events>
  ) {
    // Constructor using parameter properties
  }

  public async start(): Promise<void> {
    if (this.maintenance) {
      this.logger.warn('MaintenanceManager: Start called but already started.');
      return;
    }
    this.logger.info('MaintenanceManager: Starting maintenance task...');
    this.maintenance = new Maintenance({
      adapter: this.adapter,
      logger: this.logger,
      jobsSafeToRestart: this.config.jobsSafeToRestart,
      jobsCustomRestart: this.config.jobsCustomRestart,
      jobsRiskyToRestart: this.config.jobsRiskyToRestart,
      events: this.events,
      blockedInMinutes:
        this.config.blockedInMinutes ?? DEFAULT_BLOCKED_DURATION,
      lagInMinutes: this.config.lagInMinutes ?? DEFAULT_LAG,
      metrics: this.metrics,
      service: this.config.service,
    });
    try {
      await this.maintenance.start();
      this.logger.info(
        'MaintenanceManager: Maintenance task started successfully.'
      );
    } catch (err) {
      this.maintenance = undefined; // Ensure it's cleared on failure
      const startError = new SchedulerError(
        'Failed to start maintenance task',
        err instanceof Error ? err : new Error(String(err))
      );
      this.logger.error({ err, startError }, startError.message);
      throw startError;
    }
  }

  public async stop(): Promise<void> {
    if (!this.maintenance) {
      this.logger.info('MaintenanceManager: Stop called but not running.');
      return;
    }

    this.logger.info('MaintenanceManager: Stopping maintenance task...');
    try {
      await this.maintenance.stop();
      this.logger.info(
        'MaintenanceManager: Maintenance task stopped successfully.'
      );
    } catch (err) {
      const stopError = new SchedulerError(
        'Failed to stop maintenance task',
        err instanceof Error ? err : new Error(String(err))
      );
      this.logger.error({ err, stopError }, stopError.message);
      // Don't rethrow, continue shutdown
    } finally {
      this.maintenance = undefined;
    }
  }
}
