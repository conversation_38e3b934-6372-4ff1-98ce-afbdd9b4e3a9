import { SchedulerError } from '../errors/scheduler-errors';
import { DatabaseAdapter } from '../types/database-adapter.interface';
import { GenericLogger } from '../types/generic-logger.interface';
import { MetricsClient } from '../types/metrics-client.interface';
import { JobSet } from '../types/job-set.type';
import { ConcurrencyManager } from './concurrency-manager';

/**
 * Fetches jobs from the database adapter. This class is responsible for fetching jobs from the database and enqueuing them for execution.
 * - Fetches jobs from the database.
 * - Enqueues jobs for execution.
 */
export class JobFetcher {
  constructor(
    private readonly adapter: DatabaseAdapter,
    private readonly logger: GenericLogger,
    private readonly metrics: MetricsClient,
    private readonly concurrencyManager: ConcurrencyManager,
    private readonly config: {
      allJobs: string[];
      namespace?: string;
      service: string;
    }
  ) {
    // Constructor using parameter properties
  }

  /**
   * Fetches and enqueues jobs ready to run.
   */
  public async fetchAndEnqueue(): Promise<JobSet[]> {
    const startTime = Date.now();
    this.logger.debug('JobFetcher: Fetching and enqueuing jobs...');

    try {
      // The adapter is responsible for limiting based on concurrency
      const jobs = await this.adapter.fetchAndEnqueueJobs(
        this.config.allJobs,
        this.concurrencyManager.getAvailableSlots(),
        this.config.namespace
      );

      const duration = Date.now() - startTime;
      const totalJobs = jobs.reduce((total, job) => total + job.total, 0);
      const jobIds = jobs.flatMap(job => job.items.map(j => j.id));
      this.emitTiming('scheduler.enqueue.duration', duration);
      this.emitMetric('scheduler.jobs.enqueued', totalJobs);

      if (totalJobs > 0) {
        this.logger.info(
          { count: totalJobs, durationMs: duration, jobIds },
          'JobFetcher: Jobs enqueued'
        );
      }

      return jobs;
    } catch (err) {
      const schedulerError = new SchedulerError(
        'Failed to enqueue scheduled jobs',
        err instanceof Error ? err : new Error(String(err))
      );
      this.logger.error({ err, schedulerError }, schedulerError.message);
      const duration = Date.now() - startTime;
      this.emitMetric('scheduler.enqueue.error', 1);
      this.emitTiming('scheduler.enqueue.error_duration', duration);
      throw schedulerError; // Re-throw for the cycle handler
    }
  }

  private emitMetric(
    name: string,
    value: number,
    tags?: Record<string, string | number>
  ): void {
    this.metrics.increment(name, value, {
      ...tags,
      service: this.config.service,
    });
  }

  private emitTiming(
    name: string,
    value: number,
    tags?: Record<string, string | number>
  ): void {
    this.metrics.timing(name, value, {
      ...tags,
      service: this.config.service,
    });
  }
}
