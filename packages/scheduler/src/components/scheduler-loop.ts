import { SchedulerError } from '../errors/scheduler-errors';
import { JobSet } from '../types/job-set.type';
import { GenericLogger } from '../types/generic-logger.interface';
import { MetricsClient } from '../types/metrics-client.interface';
import { JobFetcher } from './job-fetcher';
import { JobExecutor } from './job-executor';
import { ConcurrencyManager } from './concurrency-manager';
import { HeartbeatManager } from './heartbeat-manager';
import { LifecycleManager } from './lifecycle-manager';
import { TaskMap } from '../types/task.type';
import { JobBase } from '../types/job-base.interface';

/**
 * Manages the main scheduling loop and job processing cycle. This class is responsible for scheduling and executing jobs in a loop.
 * - Schedules and executes jobs in a loop.
 * - Tracks the state of the loop (running, paused, exiting).
 */
export class SchedulerLoop {
  private currentTimeout?: ReturnType<typeof setTimeout>;

  private cycleInProgress = false;

  private fetchInProgress = false; // Separate flag for fetching phase

  constructor(
    private readonly jobFetcher: JobFetcher,
    private readonly jobExecutor: JobExecutor,
    private readonly concurrencyManager: ConcurrencyManager,
    private readonly heartbeatManager: HeartbeatManager,
    private readonly lifecycleManager: LifecycleManager,
    private readonly tasks: TaskMap,
    private readonly logger: GenericLogger,
    private readonly metrics: MetricsClient,
    private readonly config: {
      defaultRunInterval: number;
      service: string;
    }
  ) {
    // Constructor using parameter properties
  }

  /**
   * Starts the scheduling loop.
   */
  public start(waitTime: number = this.config.defaultRunInterval): void {
    if (this.currentTimeout) {
      this.logger.warn('SchedulerLoop: Start called but already running.');
      return;
    }
    this.logger.info(
      { waitInterval: waitTime },
      'SchedulerLoop: Starting loop.'
    );
    this.scheduleNext(waitTime);
  }

  /**
   * Stops the scheduling loop.
   */
  public stop(): void {
    if (this.currentTimeout) {
      clearTimeout(this.currentTimeout);
      this.currentTimeout = undefined;
      this.logger.info('SchedulerLoop: Stopped loop.');
    }
    this.cycleInProgress = false; // Reset cycle flag on stop
    this.fetchInProgress = false;
  }

  /**
   * Checks if the scheduling loop is currently active (i.e., has a timeout scheduled).
   */
  public isRunning(): boolean {
    return this.currentTimeout !== undefined;
  }

  /**
   * Schedules the next execution cycle.
   */
  private scheduleNext(waitTime: number): void {
    if (this.currentTimeout) {
      // Clear any existing timer before setting a new one
      clearTimeout(this.currentTimeout);
    }

    if (this.lifecycleManager.getIsExiting()) {
      this.logger.info('SchedulerLoop: Exiting, not scheduling next cycle.');
      return;
    }

    this.logger.debug(
      { waitMs: waitTime },
      'SchedulerLoop: Scheduling next cycle.'
    );
    this.currentTimeout = setTimeout(() => {
      (async () => {
        if (this.cycleInProgress) {
          this.logger.warn(
            'SchedulerLoop: Cycle still in progress, skipping run.'
          );
          this.scheduleNext(waitTime); // Reschedule even if skipped
          return;
        }
        this.cycleInProgress = true;
        try {
          if (this.lifecycleManager.getIsPaused()) {
            this.logger.debug('SchedulerLoop: Paused, skipping cycle.');
          } else if (!this.concurrencyManager.canRunMore()) {
            this.logger.debug(
              'SchedulerLoop: Max concurrency reached, skipping cycle.'
            );
          } else {
            await this.runCycle();
          }
        } catch (err) {
          // Errors from runCycle should be caught within it, but catch here as a safeguard
          this.logger.error(
            { err },
            'SchedulerLoop: Uncaught error during cycle execution.'
          );
          this.emitMetric('scheduler.cycle.error', 1);
        } finally {
          this.cycleInProgress = false;
          // Always reschedule, regardless of outcome
          this.scheduleNext(waitTime);
        }
      })().catch(unhandledError => {
        // Catch errors from the async IIFE itself (should be rare)
        this.logger.error(
          { err: unhandledError },
          'SchedulerLoop: Unhandled promise rejection in scheduling loop.'
        );
        this.emitMetric('scheduler.unhandled.error', 1);
        this.cycleInProgress = false; // Ensure flag is reset
        this.scheduleNext(waitTime); // Reschedule even on unhandled errors
      });
    }, waitTime);
  }

  /**
   * Executes a single job processing cycle: fetch -> execute.
   */
  private async runCycle(): Promise<void> {
    this.heartbeatManager.beat(); // Update heartbeat at the start of a cycle attempt
    const startTime = Date.now();
    let jobsProcessedInCycle = false;
    this.logger.debug('SchedulerLoop: Starting new cycle.');

    this.emitMemoryMetrics();

    // Prevent concurrent fetches
    if (this.fetchInProgress) {
      this.logger.debug(
        'SchedulerLoop: Fetch already in progress, skipping fetch phase.'
      );
      this.emitMetric('scheduler.cycle.skipped.fetch_in_progress', 1);
      return; // Don't proceed if fetch is happening
    }

    try {
      this.fetchInProgress = true;
      const jobSets = await this.jobFetcher.fetchAndEnqueue();
      this.fetchInProgress = false; // Reset fetch flag after fetch completes

      if (jobSets.length === 0) {
        this.logger.debug('SchedulerLoop: No jobs fetched in this cycle.');
        this.emitMetric('scheduler.queue_cycle.empty', 1);
        // No jobs, cycle ends, metrics below will reflect this
      } else {
        jobsProcessedInCycle = await this.processJobSets(jobSets);
      }

      // Record cycle metrics
      const duration = Date.now() - startTime;
      this.emitTiming('scheduler.queue_cycle.duration', duration);
      this.emitMetric('scheduler.queue_cycle.count', 1);
      if (jobsProcessedInCycle) {
        this.emitMetric('scheduler.queue_cycle.processed', 1);
        this.emitGauge('scheduler.queue_cycle.last_success', Date.now());
      }
      // Emit lag metric regardless of jobs processed
      this.emitGauge(
        'scheduler.queue.lag',
        Math.max(0, Date.now() - this.heartbeatManager.getLastHeartbeat())
      );
      this.logger.debug(
        { durationMs: duration, processed: jobsProcessedInCycle },
        'SchedulerLoop: Cycle finished.'
      );
    } catch (err) {
      // Catch errors specifically from fetchAndEnqueue
      this.fetchInProgress = false; // Ensure flag is reset on error too
      const cycleError = new SchedulerError(
        'Job scheduling cycle failed during fetch',
        err instanceof Error ? err : new Error(String(err))
      );
      this.logger.error({ err, cycleError }, cycleError.message);
      this.emitMetric('scheduler.queue_cycle.error', 1);
      this.emitTiming(
        'scheduler.queue_cycle.error_duration',
        Date.now() - startTime
      );
      // Don't re-throw, allow loop to continue scheduling
    }
    // Note: Errors during job execution are handled within JobExecutor and processJobSets
  }

  /**
   * Processes the fetched job sets, executing tasks concurrently up to the limit.
   */
  private async processJobSets(jobSets: JobSet[]): Promise<boolean> {
    let jobsStartedThisBatch = 0;

    for (const batch of jobSets) {
      this.heartbeatManager.beat();
      const { name: jobName, items } = batch;
      const task = this.tasks[jobName];

      if (!task) {
        this.logger.error(
          { jobName },
          'SchedulerLoop: No task handler defined for job'
        );
        this.emitMetric('scheduler.task.missing', 1, { job_type: jobName });
        continue;
      }

      for (let i = 0; i < items.length; i += 1) {
        const item = items[i];

        // Atomically check and increment concurrency in one operation
        if (!this.concurrencyManager.tryReserveSlot()) {
          this.logger.warn(
            { active: this.concurrencyManager.getActiveCount() },
            'SchedulerLoop: Max concurrency reached during processing, deferring remaining jobs.'
          );

          // Defer remaining jobs in batch
          await this.deferRemainingJobs(jobName, items.slice(i));
          break;
        }

        if (this.lifecycleManager.getIsExiting()) {
          this.logger.info('SchedulerLoop: Exiting, stopping job processing.');
          // Release the slot we just reserved if we're exiting
          this.concurrencyManager.releaseReservedSlot();
          await this.deferRemainingJobs(jobName, items.slice(i));
          break;
        }

        const jobId = item.id;
        // Execute job and ensure slot is released when done
        const jobPromise = this.jobExecutor
          .execute(jobName, item, task)
          .finally(() => {
            this.concurrencyManager.endJob(jobId);
          });

        this.concurrencyManager.startJob(jobId, jobPromise);
        jobsStartedThisBatch += 1;
      }
    }

    this.logger.debug(
      { started: jobsStartedThisBatch },
      'SchedulerLoop: Finished iterating job sets for this cycle.'
    );

    return jobsStartedThisBatch > 0;
  }

  // Helper method to defer remaining jobs
  private async deferRemainingJobs(
    jobName: string,
    jobs: JobBase[]
  ): Promise<void> {
    for (const job of jobs) {
      try {
        await this.jobExecutor.defer(jobName, job);
        this.logger.info(
          { jobId: job.id },
          'SchedulerLoop: Deferred job to next cycle'
        );
      } catch (err) {
        this.logger.error(
          { err, jobId: job.id },
          'SchedulerLoop: Failed to defer job'
        );
      }
    }
  }

  private emitMetric(
    name: string,
    value: number,
    tags?: Record<string, string | number>
  ): void {
    this.metrics.increment(name, value, {
      ...tags,
      service: this.config.service,
    });
  }

  private emitTiming(
    name: string,
    value: number,
    tags?: Record<string, string | number>
  ): void {
    this.metrics.timing(name, value, {
      ...tags,
      service: this.config.service,
    });
  }

  private emitGauge(
    name: string,
    value: number,
    tags?: Record<string, string | number>
  ): void {
    this.metrics.gauge(name, value, {
      ...tags,
      service: this.config.service,
    });
  }

  /**
   * Emits metrics about the current memory state of the scheduler.
   * This helps diagnose issues with job execution and concurrency.
   */
  private emitMemoryMetrics(): void {
    // Track concurrency manager state
    this.emitGauge(
      'scheduler.memory.active_jobs',
      this.concurrencyManager.getActiveCount()
    );
    this.emitGauge(
      'scheduler.memory.running_jobs',
      this.concurrencyManager.getRunningJobsCount()
    );

    // Track cycle state
    this.emitGauge(
      'scheduler.memory.cycle_in_progress',
      this.cycleInProgress ? 1 : 0
    );
    this.emitGauge(
      'scheduler.memory.fetch_in_progress',
      this.fetchInProgress ? 1 : 0
    );

    // Log detailed state for debugging
    this.logger.debug(
      {
        memory_state: {
          concurrency: {
            activeCount: this.concurrencyManager.getActiveCount(),
            runningJobsCount: this.concurrencyManager.getRunningJobsCount(),
            canRunMore: this.concurrencyManager.canRunMore(),
          },
          cycle: {
            cycleInProgress: this.cycleInProgress,
            fetchInProgress: this.fetchInProgress,
          },
          lifecycle: {
            isPaused: this.lifecycleManager.getIsPaused(),
            isExiting: this.lifecycleManager.getIsExiting(),
          },
        },
      },
      'SchedulerLoop: Memory state metrics'
    );
  }
}
