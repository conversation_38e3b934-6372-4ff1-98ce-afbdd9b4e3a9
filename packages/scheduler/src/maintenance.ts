import moment from 'moment-timezone';
import TypedEmitter from 'typed-emitter';
import { JobBase } from './types/job-base.interface';
import { Events } from './types/events.interface';
import { DatabaseAdapter } from './types/database-adapter.interface';
import { MetricsClient } from './types/metrics-client.interface';
import { MaintenanceConfig } from './types/maintenance-config.interface';
import { GenericLogger } from './types/generic-logger.interface';
import { computeNextRun } from './utils';

// Default interval for maintenance check (1 minute)
const MAINTENANCE_INTERVAL = 60 * 1000;

class MaintenanceError extends Error {
  constructor(message: string, cause?: Error) {
    super(message, cause);
    this.name = 'MaintenanceError';
  }
}

/**
 * Maintenance handles the health and recovery of scheduled jobs in the system.
 * It runs periodic checks (default every 60 seconds) to identify and fix various job states:
 *
 * Job State Definitions:
 * ---------------------
 * Pending Jobs:
 * - A job is considered "pending" when its nextRunAt time is in the past
 * - The job's queued flag is false (not yet picked up for processing)
 * - These jobs should have started but haven't been queued for execution yet
 * - Usually indicates scheduling delays or system overload
 *
 * Blocked Jobs:
 * - A job is "blocked" when it's been queued (queued=true) but hasn't been accepted/started
 * - Either the acceptedAt is NULL (never started) or is older than the lastRunAt
 * - Detected after being stuck for 5+ minutes in this state
 * - Usually indicates worker crashes during job pickup or queue processing issues
 *
 * Lagged Jobs:
 * - A job is "lagged" when it started executing but hasn't finished in the expected time
 * - Has been accepted (has acceptedAt timestamp) but either:
 *   a) Never finished (lastFinishedAt is NULL) or
 *   b) Last finish time is too old (6+ minutes by default)
 * - Typically indicates hung processes, infinite loops, or worker crashes during execution
 *
 * Recovery Actions:
 * ---------------
 * 1. Pending Jobs Check:
 *    - Identifies jobs past their scheduled run time but haven't been queued
 *    - Groups them by job name and emits 'pending' events for monitoring
 *
 * 2. Blocked Jobs Check (after 5 minutes):
 *    - For Safe Jobs: Automatically resets jobs that are queued but haven't been accepted
 *    - For Risky Jobs: Identifies jobs stuck in queued state for monitoring
 *
 * 3. Lagged Jobs Check (after 6 minutes):
 *    - For Safe Jobs: Resets jobs that started but haven't finished in time
 *    - For Custom Restart Jobs: Checks against custom timeout configurations
 *    - For Other Jobs: Emits 'lagged' events for monitoring
 *
 * The scheduler respects different job categories:
 * - jobsSafeToRestart: Jobs that can be automatically restarted
 * - jobsRiskyToRestart: Jobs that need careful handling when stuck
 * - jobsCustomRestart: Jobs with custom timeout configurations
 */
export class Maintenance {
  private timeoutHandle: ReturnType<typeof setTimeout> | null = null;

  private isRunning = false;

  private readonly allJobs: string[];

  private readonly adapter: DatabaseAdapter;

  private readonly logger: GenericLogger;

  private readonly jobsSafeToRestart: string[];

  private readonly jobsCustomRestart: Record<
    string,
    moment.Duration | undefined
  >;

  private readonly jobsRiskyToRestart: string[];

  private readonly events: TypedEmitter<Events>;

  private readonly service: string;

  private readonly blockedInMinutes: number;

  private readonly lagInMinutes: number;

  private readonly metrics?: MetricsClient;

  constructor(config: MaintenanceConfig) {
    this.adapter = config.adapter;
    this.logger = config.logger;
    this.jobsSafeToRestart = config.jobsSafeToRestart;
    this.jobsCustomRestart = config.jobsCustomRestart;
    this.jobsRiskyToRestart = config.jobsRiskyToRestart;
    this.events = config.events;
    this.blockedInMinutes = config.blockedInMinutes ?? 5;
    this.lagInMinutes = config.lagInMinutes ?? 6;
    this.metrics = config.metrics;
    this.service = config.service;

    this.allJobs = [...this.jobsRiskyToRestart, ...this.jobsSafeToRestart];
  }

  private scheduleNext = (interval = MAINTENANCE_INTERVAL): void => {
    if (this.timeoutHandle) {
      clearTimeout(this.timeoutHandle);
    }
    this.logger.debug(
      {
        nextIntervalMs: interval,
        service: this.service,
      },
      'Scheduling next maintenance run'
    );

    this.timeoutHandle = setTimeout(() => {
      this.processMaintenanceIteration().catch(err => {
        const maintenanceError = new MaintenanceError(
          'Maintenance run failed',
          err instanceof Error ? err : new Error(String(err))
        );

        this.logger.error({ err, maintenanceError }, 'Maintenance run failed');

        if (this.metrics) {
          this.metrics.increment('scheduler.maintenance.error', 1, {
            service: this.service,
          });
        }
      });
    }, interval);
  };

  private async processMaintenanceIteration(): Promise<void> {
    if (this.isRunning) {
      this.logger.warn(
        'Maintenance check still running, skipping this iteration'
      );
      this.scheduleNext();
      return;
    }

    this.isRunning = true;
    const startTime = Date.now();
    this.logger.debug('Starting maintenance iteration');

    try {
      await this.runMaintenance();
      const duration = Date.now() - startTime;
      this.logger.debug(
        { durationMs: duration },
        'Maintenance iteration completed successfully'
      );

      if (this.metrics) {
        this.metrics.timing('scheduler.maintenance.duration', duration, {
          service: this.service,
        });
        this.metrics.increment('scheduler.maintenance.completed', 1, {
          service: this.service,
        });
      }
    } catch (err) {
      const duration = Date.now() - startTime;
      this.logger.error(
        { err, durationMs: duration },
        'Maintenance iteration failed'
      );

      if (this.metrics) {
        this.metrics.increment('scheduler.maintenance.error');
        this.metrics.timing('scheduler.maintenance.error_duration', duration);
      }

      throw err; // Will be caught by the handler in scheduleNext
    } finally {
      this.isRunning = false;
      const elapsed = Date.now() - startTime;
      const nextInterval = Math.max(0, MAINTENANCE_INTERVAL - elapsed);
      this.scheduleNext(nextInterval);
    }
  }

  private async processPendingJobs(): Promise<void> {
    if (this.allJobs.length > 0) {
      this.logger.debug(
        { jobCount: this.allJobs.length },
        'Checking for pending jobs'
      );
      const pendingJobsMap = await this.adapter.getPendingJobs(this.allJobs);

      const totalPending = Object.values(pendingJobsMap).reduce(
        (sum, count) => sum + count,
        0
      );

      if (totalPending > 0) {
        this.logger.info(
          { pendingJobs: pendingJobsMap, totalPending },
          'Found pending jobs that need processing'
        );
      } else {
        this.logger.debug('No pending jobs found');
      }

      this.events.emit('pending', pendingJobsMap);

      if (this.metrics) {
        // Count total pending jobs
        this.metrics.gauge('scheduler.jobs.pending.total', totalPending);
      }
    } else {
      this.events.emit('pending', {});
    }
  }

  private async resetJob(
    job: JobBase,
    reason: string,
    isSafe: boolean
  ): Promise<void> {
    try {
      if (job.repeatInterval && job.repeatInterval.includes('FREQ')) {
        const nextRunAt = computeNextRun(job.repeatInterval, {
          timezone: job.timezone || 'UTC',
        });

        this.logger.info(
          {
            jobId: job.id,
            jobName: job.name,
            reason,
            isSafe,
            nextRunAt,
            currentTime: new Date().toISOString(),
          },
          `Resetting ${reason} job: ${job.name}`
        );

        await this.adapter.resetJob(job, nextRunAt);
        this.events.emit('reset', job, nextRunAt);

        if (this.metrics) {
          this.metrics.increment('scheduler.job.reset', 1, {
            job_type: job.name,
            reason,
            safe: isSafe ? 'true' : 'false',
          });
        }
      }
    } catch (err) {
      const jobType = isSafe ? 'safe' : 'risky';
      const resetError = new MaintenanceError(
        `Failed to reset ${reason} ${jobType} job: ${job.name} (ID: ${job.id})`,
        err instanceof Error ? err : new Error(String(err))
      );

      this.logger.error(
        {
          err,
          resetError,
          jobId: job.id,
          jobName: job.name,
        },
        `Failed to reset ${reason} ${jobType} job`
      );

      if (this.metrics) {
        this.metrics.increment('scheduler.job.reset.error', 1, {
          job_type: job.name,
          reason,
          safe: isSafe ? 'true' : 'false',
        });
      }
    }
  }

  private async processBlockedJobs(
    jobNames: string[],
    isSafe: boolean
  ): Promise<void> {
    if (jobNames.length > 0) {
      this.logger.debug(
        {
          jobNames,
          isSafe,
          blockedInMinutes: this.blockedInMinutes,
        },
        `Checking for blocked ${isSafe ? 'safe' : 'risky'} jobs`
      );

      const blockedJobs = await this.adapter.getBlockedJobs(
        jobNames,
        this.blockedInMinutes
      );

      if (blockedJobs.length > 0) {
        this.logger.info(
          {
            blockedCount: blockedJobs.length,
            jobType: isSafe ? 'safe' : 'risky',
            jobNames: blockedJobs.map(job => job.name),
          },
          `Found ${blockedJobs.length} blocked ${
            isSafe ? 'safe' : 'risky'
          } jobs`
        );
      } else {
        this.logger.debug(`No blocked ${isSafe ? 'safe' : 'risky'} jobs found`);
      }

      if (this.metrics) {
        this.metrics.gauge(
          `scheduler.jobs.blocked.${isSafe ? 'safe' : 'risky'}`,
          blockedJobs.length
        );
      }

      for (const job of blockedJobs) {
        await this.resetJob(job, 'blocked', isSafe);
      }
    }
  }

  private async processLaggedSafeJobs(): Promise<void> {
    if (this.jobsSafeToRestart.length > 0) {
      this.logger.debug(
        {
          safeJobs: this.jobsSafeToRestart,
          lagInMinutes: this.lagInMinutes,
        },
        'Checking for lagged safe jobs'
      );

      const laggedSafeJobs = await this.adapter.getLaggedJobs(
        this.jobsSafeToRestart,
        this.lagInMinutes
      );

      if (laggedSafeJobs.length > 0) {
        this.logger.info(
          {
            laggedCount: laggedSafeJobs.length,
            jobNames: laggedSafeJobs.map(job => job.name),
          },
          `Found ${laggedSafeJobs.length} lagged safe jobs to reset`
        );
      } else {
        this.logger.debug('No lagged safe jobs found');
      }

      if (this.metrics) {
        this.metrics.gauge('scheduler.jobs.lagged.safe', laggedSafeJobs.length);
      }

      for (const job of laggedSafeJobs) {
        await this.resetJob(job, 'lagged', true);
      }
    }
  }

  private async processLaggedCustomAndRiskyJobs(): Promise<void> {
    this.logger.debug(
      {
        customJobs: Object.keys(this.jobsCustomRestart),
        riskyJobs: this.jobsRiskyToRestart,
        lagInMinutes: this.lagInMinutes,
      },
      'Checking for lagged custom and risky jobs'
    );

    const potentiallyLaggedJobs = await this.adapter.getLaggedJobs(
      [...Object.keys(this.jobsCustomRestart), ...this.jobsRiskyToRestart],
      this.lagInMinutes
    );

    if (potentiallyLaggedJobs.length > 0) {
      this.logger.info(
        {
          laggedCount: potentiallyLaggedJobs.length,
          jobNames: potentiallyLaggedJobs.map(job => job.name),
        },
        `Found ${potentiallyLaggedJobs.length} potentially lagged custom/risky jobs`
      );
    } else {
      this.logger.debug('No lagged custom or risky jobs found');
    }

    if (this.metrics) {
      this.metrics.gauge(
        'scheduler.jobs.lagged.custom_and_risky',
        potentiallyLaggedJobs.length
      );
    }

    if (potentiallyLaggedJobs.length > 0) {
      const resetJobs: JobBase[] = [];
      const notifyJobs: JobBase[] = [];

      for (const laggedJob of potentiallyLaggedJobs) {
        const restartAfter = this.jobsCustomRestart[laggedJob.name];
        if (restartAfter) {
          const acceptedAt = moment(laggedJob.acceptedAt);
          const restartTime = moment(acceptedAt).add(restartAfter);

          if (restartTime <= moment()) {
            resetJobs.push(laggedJob);
          } else {
            notifyJobs.push(laggedJob);
          }
        } else {
          notifyJobs.push(laggedJob);
        }
      }

      // Reset jobs with custom restart times
      for (const job of resetJobs) {
        await this.resetJob(job, 'lagged_custom', false);
      }

      // Notify about lagged jobs that won't be automatically restarted
      if (notifyJobs.length) {
        this.events.emit('lagged', notifyJobs);

        if (this.metrics) {
          this.metrics.gauge('scheduler.jobs.lagged.notify', notifyJobs.length);
        }
      }
    }
  }

  private async runMaintenance(): Promise<void> {
    try {
      // Process pending jobs
      await this.processPendingJobs();

      // Process blocked jobs for safe and risky jobs
      await this.processBlockedJobs(this.jobsSafeToRestart, true);
      await this.processBlockedJobs(this.jobsRiskyToRestart, false);

      // Process lagged jobs
      await this.processLaggedSafeJobs();
      await this.processLaggedCustomAndRiskyJobs();
    } catch (err) {
      const maintenanceError = new MaintenanceError(
        'Maintenance operation failed',
        err instanceof Error ? err : new Error(String(err))
      );

      this.logger.warn({ err, maintenanceError }, 'Maintenance failed');

      if (this.metrics) {
        this.metrics.increment('scheduler.maintenance.operation_error');
      }

      throw maintenanceError;
    }
  }

  async start(): Promise<void> {
    try {
      if (this.metrics) {
        this.metrics.increment('scheduler.maintenance.starting');
      }

      await this.processMaintenanceIteration();

      if (this.metrics) {
        this.metrics.increment('scheduler.maintenance.started');
      }

      this.logger.info('Maintenance started');
    } catch (err) {
      const startError = new MaintenanceError(
        'Initial maintenance run failed',
        err instanceof Error ? err : new Error(String(err))
      );

      this.logger.error({ err, startError }, 'Initial maintenance run failed');

      if (this.metrics) {
        this.metrics.increment('scheduler.maintenance.start_error');
      }

      throw startError;
    }
  }

  stop(): void {
    if (this.timeoutHandle) {
      clearTimeout(this.timeoutHandle);
      this.timeoutHandle = null;
      this.logger.info({ service: this.service }, 'Maintenance stopped');
    }
  }
}
