```mermaid
graph TD
    subgraph UserApplication["User Application"]
        direction LR
        Tasks(User-Defined Tasks)
        Config(Scheduler Config  <br/>  e.g., Logger, Metrics, Task Handlers)
    end

    subgraph SchedulerPackage["@ordermentum/scheduler Package"]
        direction TB
        JS[JobScheduler Core Engine] -- Initializes & Manages --> Maint(Maintenance Process)
        JS -- Uses --> DA(DatabaseAdapter Interface)
        JS -- Executes --> Tasks
        JS -- Uses --> <PERSON>gger(GenericLogger)
        JS -- Uses --> Metrics(MetricsClient)
        JS -- Emits --> Events(EventEmitter)
        JS -- Uses --> RRule(Recurrence Logic <br/>  using rrule-rust)

        Maint -- Uses --> DA

        subgraph Adapters ["Database Adapters"]
            direction LR
            DA --- PA(PrismaAdapter)
            DA --- SA(SequelizeAdapter)
        end
    end

    subgraph ExternalSystems["External Systems / Dependencies"]
        direction LR
        DB[(Database <br/> e.g., Postgres)]
        MetricsSystem(Metrics Backend <br/> e.g., StatsD, Prometheus)
        LogSystem(Logging Backend <br/> e.g., Console, File)
    end

    UserApplication -- Configures & Starts --> JS
    PA -- Interacts with --> DB
    SA -- Interacts with --> DB
    Metrics -- Sends data to --> MetricsSystem
    Logger -- Sends logs to --> LogSystem

    style SchedulerPackage fill:#f9f,stroke:#333,stroke-width:2px
    style UserApplication fill:#ccf,stroke:#333,stroke-width:2px
    style ExternalSystems fill:#cfc,stroke:#333,stroke-width:2px
    style Adapters fill:#eef,stroke:#333,stroke-width:1px,stroke-dasharray: 5 5
```