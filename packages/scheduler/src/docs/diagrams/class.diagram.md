```mermaid
classDiagram
    direction LR 

    class JobScheduler {
        +ILogger logger
        +IMetrics metrics
        +IAdapter adapter
        +IEventEmitter events
        +Tasks tasks
        +JobSchedulerConfig config
        -ConcurrencyManager concurrencyManager
        -JobExecutor jobExecutor
        -JobFetcher jobFetcher
        -HeartbeatManager heartbeatManager
        -LifecycleManager lifecycleManager
        -MaintenanceManager maintenanceManager
        -SchedulerLoop schedulerLoop
        +init() void
        +runScheduledJobs(waitTime?: number) void
        +pause() void
        +resume() void
        +healthCheck() boolean
        +terminate() void
    }

    class MaintenanceManager {
        +IAdapter adapter
        +ILogger logger
        +IMetrics metrics
        +IEventEmitter events
        -Maintenance maintenance
        +start() void
        +stop() void
    }

    class Maintenance {
        +DatabaseAdapter adapter
        +GenericLogger logger
        +MetricsClient metrics
        +TypedEmitter~Events~ events
        +MaintenanceConfig config
        -processMaintenanceIteration() void
        -runMaintenance() void
        -processPendingJobs() void
        -processBlockedJobs() void
        -processLaggedSafeJobs() void
        -processLaggedCustomAndRiskyJobs() void
        -resetJob() void
        +start() void
        +stop() void
    }

    class SchedulerLoop {
        +JobFetcher jobFetcher
        +JobExecutor jobExecutor
        +ConcurrencyManager concurrencyManager
        +HeartbeatManager heartbeatManager
        +LifecycleManager lifecycleManager
        +Tasks tasks
        +ILogger logger
        +IMetrics metrics
        +start(interval: number) void
        +stop() void
        +isRunning() boolean
    }

    class JobExecutor {
        +IAdapter adapter
        +ILogger logger
        +IMetrics metrics
        +IEventEmitter events
        +executeJob(job, taskCallback) void
    }

    class JobFetcher {
        +IAdapter adapter
        +ILogger logger
        +IMetrics metrics
        +fetchJobs() JobSet[]
    }

    class ConcurrencyManager {
        +ILogger logger
        +acquire() boolean
        +release() void
        +getActiveCount() number
        +waitForRunningJobs(timeout: number) boolean
    }

    class HeartbeatManager {
        +ILogger logger
        +IMetrics metrics
        +beat() void
        +checkHealth() boolean
        +reset() void
    }

    class LifecycleManager {
        +ILogger logger
        +signalExit() void
        +pause() void
        +resume() void
        +setInitialized(state: boolean) void
        +getIsInitialized() boolean
        +getIsExiting() boolean
        +reset() void
    }

    class DatabaseAdapter {
        <<Interface>>
        +fetchAndEnqueueJobs() JobSet[]
        +resetJob() void
        +updateStartTask() void
        +updateFinishTask() void
        +updateFailure() void
        +getPendingJobs() PendingJobs
        +getBlockedJobs() JobBase[]
        +getLaggedJobs() JobBase[]
        +disconnect() void
    }

    class MetricsClient {
        <<Interface>>
        +increment() void
        +timing() void
        +gauge() void
    }

    class GenericLogger {
        <<Interface>>
        +info() void
        +error() void
        +warn() void
        +debug() void
    }

    class Events {
        <<Interface>>
        +lagged(jobs: JobBase[]) void
        +reset(job: JobBase, nextRunAt: string) void
        +duration(job: JobBase, timeSecs: number, success: boolean) void
        +pending(data: PendingJobs) void
    }

    class utils {
        <<Module>>
        +computeNextRun() string
        +computeNextRuns() string[]
        +isHealthy() boolean
        +retryDelay() number
    }

    class index.ts {
        <<EntryPoint>>
        +Exports JobScheduler
        +Exports Adapters
        +Exports utils
        +Exports types
    }

    JobScheduler o--> ConcurrencyManager
    JobScheduler o--> JobExecutor
    JobScheduler o--> JobFetcher
    JobScheduler o--> HeartbeatManager
    JobScheduler o--> LifecycleManager
    JobScheduler o--> MaintenanceManager
    JobScheduler o--> SchedulerLoop
    JobScheduler ..> DatabaseAdapter : uses
    JobScheduler ..> MetricsClient : uses
    JobScheduler ..> GenericLogger : uses
    JobScheduler ..> Events : uses

    MaintenanceManager o--> Maintenance
    MaintenanceManager ..> DatabaseAdapter : uses
    MaintenanceManager ..> MetricsClient : uses
    MaintenanceManager ..> GenericLogger : uses
    MaintenanceManager ..> Events : uses

    Maintenance ..> DatabaseAdapter : uses
    Maintenance ..> MetricsClient : uses
    Maintenance ..> GenericLogger : uses
    Maintenance ..> Events : uses
    Maintenance ..> utils : uses computeNextRun

    SchedulerLoop ..> JobFetcher : uses
    SchedulerLoop ..> JobExecutor : uses
    SchedulerLoop ..> ConcurrencyManager : uses
    SchedulerLoop ..> HeartbeatManager : uses
    SchedulerLoop ..> LifecycleManager : uses
    SchedulerLoop ..> GenericLogger : uses
    SchedulerLoop ..> MetricsClient : uses

    JobExecutor ..> DatabaseAdapter : uses
    JobExecutor ..> MetricsClient : uses
    JobExecutor ..> GenericLogger : uses
    JobExecutor ..> Events : uses

    JobFetcher ..> DatabaseAdapter : uses
    JobFetcher ..> MetricsClient : uses
    JobFetcher ..> GenericLogger : uses

    HeartbeatManager ..> MetricsClient : uses
    HeartbeatManager ..> GenericLogger : uses
    HeartbeatManager ..> utils : uses isHealthy

    ConcurrencyManager ..> GenericLogger : uses
    LifecycleManager ..> GenericLogger : uses

    index.ts --> JobScheduler : exports
    index.ts --> DatabaseAdapter : exports (via adapters)
    index.ts --> utils : exports
    index.ts --> Events : exports (via types)
    index.ts --> GenericLogger : exports (via types)
    index.ts --> MetricsClient : exports (via types)
```