import moment from 'moment-timezone';
import { RRuleSet } from 'rrule-rust';

/** Maximum time range to look ahead when computing recurrence runs (6 months) */
const SIX_MONTHS_IN_MS = 15778476000;

/**
 * Checks if a service is healthy by comparing its last heartbeat against a timeout
 * @param heartbeat - Timestamp of the last heartbeat in milliseconds
 * @param timeout - Maximum allowed time between heartbeats in milliseconds
 * @returns true if the service is healthy, false otherwise
 * @example
 * const lastHeartbeat = Date.now() - 5000; // 5 seconds ago
 * const timeout = 10000; // 10 seconds
 * const isServiceHealthy = isHealthy(lastHeartbeat, timeout);
 * console.log(isServiceHealthy); // true
 */
export const isHealthy = (heartbeat: number, timeout: number): boolean =>
  new Date().getTime() - timeout < heartbeat;

/**
 * Converts a recurrence string into a valid iCal rule string with DTSTART
 * Handles both existing iCal rules and custom recurrence strings
 * @param recurrence - The recurrence rule string (iCal format or custom)
 * @param timezone - Optional timezone identifier (defaults to UTC)
 * @example
 * const rule = getValidRule('FREQ=DAILY;INTERVAL=1', 'America/New_York');
 * console.log(rule);
 * // Output: DTSTART;TZID=America/New_York:20230101T000000\nRRULE:FREQ=DAILY;INTERVAL=1
 */
const getValidRule = (recurrence: string, timezone?: string): string => {
  const isICalRule = recurrence.startsWith('DTSTART');
  if (isICalRule) return recurrence;

  let derivedTimezone = timezone ?? 'UTC';
  let dateStart = moment().toISOString();
  const rule = recurrence
    .split(';')
    .filter(b => {
      const [key, value] = b.split('=');
      if (key === 'TZID') {
        derivedTimezone = value;
      }
      if (key === 'DTSTART') {
        dateStart = value;
      }
      return key !== 'TZID' && key !== 'DTSTART';
    })
    .join(';');

  const timeISO8601 = moment(dateStart)
    .tz(derivedTimezone)
    .format('YYYYMMDDTHHmmss');
  return `DTSTART;TZID=${derivedTimezone}:${timeISO8601}\nRRULE:${rule}`;
};

/**
 * Computes the next N occurrences of a recurring schedule
 * @param interval - The recurrence rule string
 * @param options - Configuration options
 * @param options.timezone - Timezone to compute runs in (defaults to UTC)
 * @param options.startDate - Start date for computing runs (defaults to now)
 * @param options.count - Number of runs to compute (max 30, defaults to 1)
 * @returns Array of ISO timestamp strings for the next runs
 * @throws If interval is empty
 * @example
 * const nextRuns = computeNextRuns('FREQ=DAILY;INTERVAL=2', { count: 3 });
 * console.log(nextRuns);
 * // Output: ['2023-01-01T00:00:00.000Z', '2023-01-03T00:00:00.000Z', '2023-01-05T00:00:00.000Z']
 */
export const computeNextRuns = (
  interval: string,
  { timezone = 'UTC', startDate = moment().toISOString(), count = 1 } = {}
): string[] => {
  if (!interval) {
    throw new Error('Need a valid interval to compute next runs');
  }

  const rule = getValidRule(interval, timezone);
  const rrule = RRuleSet.parse(rule);
  const runCount = Math.min(count, 30);

  const start = moment(startDate).valueOf();
  const end = moment(start).add(SIX_MONTHS_IN_MS, 'ms').valueOf();
  return rrule
    .between(start, end, true)
    .slice(0, runCount)
    .map(run => new Date(run).toISOString());
};

/**
 * Gets the next single occurrence of a recurring schedule
 * Convenience wrapper around computeNextRuns
 * @param interval - The recurrence rule string
 * @param options - Configuration options (see computeNextRuns)
 * @returns ISO timestamp string for the next run
 * @throws If interval is empty
 * @example
 * const nextRun = computeNextRun('FREQ=WEEKLY;BYDAY=MO', { timezone: 'Europe/London' });
 * console.log(nextRun); // '2023-01-02T00:00:00.000Z' (assuming today is not Monday)
 */
export const computeNextRun = (
  interval: string,
  { timezone = 'UTC', startDate = moment().toISOString() } = {}
): string => {
  if (!interval) {
    throw new Error('Need a valid interval to compute next run');
  }

  const [nextRun] = computeNextRuns(interval, {
    timezone,
    startDate,
    count: 1,
  });
  return nextRun;
};

/**
 * Calculates exponential backoff delay for retry attempts
 * @param attempt - The current retry attempt number
 * @param backoff - Base delay in milliseconds (defaults to 1000)
 * @param factor - Exponential factor for backoff (defaults to 2)
 * @param jitter - Whether to add randomization to prevent thundering herd (defaults to true)
 * @returns Delay in milliseconds before next retry
 * @example
 * const delay1 = retryDelay(0); // First attempt
 * console.log(delay1); // Random value between 0 and 1000
 *
 * const delay2 = retryDelay(1); // Second attempt
 * console.log(delay2); // Random value between 0 and 2000
 *
 * const delay3 = retryDelay(2, 500, 3, false); // Third attempt, custom settings
 * console.log(delay3); // 4500
 */
export const retryDelay = (
  attempt: number,
  backoff = 1000,
  factor = 2,
  jitter = true
): number =>
  Math.round((jitter ? Math.random() : 1) * backoff * factor ** attempt);
