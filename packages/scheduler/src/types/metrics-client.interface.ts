/**
 * StatsD metrics client interface for instrumentation
 */
export interface MetricsClient {
  /**
   * Increment a counter metric
   * @param name Metric name
   * @param value Value to increment by (default: 1)
   * @param tags Optional tags to add to the metric
   */
  increment(name: string, value?: number, tags?: Record<string, string>): void;

  /**
   * Record a timing/duration metric
   * @param name Metric name
   * @param value Duration in milliseconds
   * @param tags Optional tags to add to the metric
   */
  timing(name: string, value: number, tags?: Record<string, string>): void;

  /**
   * Record a gauge metric
   * @param name Metric name
   * @param value Gauge value
   * @param tags Optional tags to add to the metric
   */
  gauge(name: string, value: number, tags?: Record<string, string>): void;
}
