import { JobBase } from './job-base.interface';

/**
 * Core types for the scheduler task system. This file consolidates all task-related types and interfaces.
 */

/**
 * Context object passed to task handlers containing job metadata
 */
export type JobContext = {
  job: JobBase;
};

/**
 * Represents the function signature for a task handler.
 * @template TData The type of the data payload expected by the task.
 * @template TResult The type of the result returned by the handler's promise.
 */
export type TaskHandler<TData = unknown, TResult = unknown> = (
  data: TData,
  context: JobContext
) => Promise<TResult>;

/**
 * Defines the expected data payload type for each known task.
 * Library consumers should extend this interface via declaration merging
 * in their own projects to register task names and their payload types.
 */
export interface TaskPayloads {
  // This interface is intentionally left empty.
  // It's designed to be extended by consumers via declaration merging.
}

/**
 * Represents the map of registered task names to their corresponding handlers.
 * This type ensures that each handler's `data` parameter type correctly
 * matches the payload type defined for that task name in the `TaskPayloads` interface.
 */
export type TaskMap = {
  [TName in keyof TaskPayloads]: TaskHandler<TaskPayloads[TName]>;
};
