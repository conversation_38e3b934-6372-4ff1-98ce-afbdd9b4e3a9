import TypedEmitter from 'typed-emitter';
import { Duration } from 'moment-timezone';
import { GenericLogger } from './generic-logger.interface';
import { Events } from './events.interface';
import { TaskMap } from './task.type';
import { DatabaseAdapter } from './database-adapter.interface';
import { MetricsClient } from './metrics-client.interface';

export interface JobSchedulerConfig {
  logger: GenericLogger;

  /**
   * @description After how many minutes should the job be considered laggy. A laggy job is one that has been running for longer than expected but hasn't completed yet.
   * @default [DEFAULT_LAG=6]
   */
  lagInMinutes?: number;

  /**
   * @description After how many minutes, should the job be considered blocked. A blocked job is one that has been running for longer than expected but hasn't been accepted yet.
   * @default [DEFAULT_BLOCKED_DURATION=10]
   */
  blockedInMinutes?: number;

  /**
   * @description Default run interval time in seconds. The scheduler will check for new jobs every `defaultRunInterval` seconds.
   * @default [DEFAULT_RUN_INTERVAL=5000]
   */
  defaultRunInterval?: number;

  /**
   * @description Jobs that are risky to restart, these are restarted according to their runtime rules.
   * @example ['payments']
   */
  jobsRiskyToRestart: string[];

  /**
   * @description Jobs that are safe to restart without implications.
   * @example ['abandoned-carts-task', 'purge-carts-task']
   */
  jobsSafeToRestart: string[];

  /**
   * @description Custom duration (restart after) setting for jobs.
   * @example {
   *   'order-schedule-reminder': moment.duration(30, 'minutes') //Start after 30 minutes of being laggy
   * }
   */
  jobsCustomRestart: {
    [name: string]: Duration | undefined;
  };

  /**
   * @description Event emitter for events such as jobs that are laggy, blocked, restarted, etc.
   */
  events?: TypedEmitter<Events>;

  /**
   * @description the name of the job against the function to call.
   * @example
   * {
   *   'abandoned-carts-task': abandonedCartsTask,
   *   'purge-carts-task': purgeCartsTask
   * }
   */
  tasks: TaskMap;

  /**
   * @description Maximum number of jobs to process concurrently and to enqueue at a time.
   * @default 4
   */
  maxConcurrentJobs?: number;

  /**
   * @description Backoff timeout for retry.
   * @default 60000
   */
  backOffMs?: number;

  /**
   * @description Number of restarts on failure before the job is not retried again.
   * @default 3
   */
  maxRestartsOnFailure?: number;

  /**
   * @description Namespace to select jobs (column - namespace).
   * By default jobs are not selected by namespace.
   */
  namespace?: string;

  /**
   * @description Database adapter to use for interacting with the database.
   */
  adapter: DatabaseAdapter;

  /**
   * @description StatsD client for collecting metrics.
   */
  metrics: MetricsClient;

  /**
   * @description Service name to tag metrics with.
   * @required
   */
  service: string;
}
