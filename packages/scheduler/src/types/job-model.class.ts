import { Model } from 'sequelize';
import { JobBase } from './job-base.interface';

export class JobModel extends Model<JobBase> {
  public id!: string;

  public name!: string;

  public data!: object;

  public lastFinishedAt!: Date | null;

  public lastRunAt!: Date | null;

  public lastModifiedBy!: string | null;

  public nextRunAt!: Date | null;

  public acceptedAt!: Date | null;

  public repeatInterval!: string | null;

  public type!: string | null;

  public priority!: number;

  public failReason!: object | null;

  public failedAt!: Date | null;

  public queued!: boolean;

  public timezone!: string;

  public failures!: number;

  public namespace?: string | null;

  // Timestamps
  public createdAt!: Date;

  public updatedAt!: Date;
}
