import { JobBase } from './job-base.interface';
import { JobSet } from './job-set.type';
import { PendingJobs } from './pending-jobs.type';

export interface DatabaseAdapter {
  fetchAndEnqueueJobs(
    allJobs: string[],
    maxConcurrentJobs: number,
    namespace?: string
  ): Promise<JobSet[]>;
  resetJob(job: JobBase, nextRunAt: string): Promise<void>;
  updateStartTask(jobId: string, namespace?: string): Promise<void>;
  updateFinishTask(jobId: string, namespace?: string): Promise<void>;
  updateFailure(
    jobId: string,
    backOffMs: number,
    maxRestartsOnFailure: number,
    jobsSafeToRestart: string[],
    namespace?: string
  ): Promise<void>;
  getPendingJobs(allJobs: string[]): Promise<PendingJobs>;
  getBlockedJobs(
    jobNames: string[],
    blockedInMinutes: number
  ): Promise<JobBase[]>;
  getLaggedJobs(jobNames: string[], lagInMinutes: number): Promise<JobBase[]>;
  disconnect(): Promise<void>;
}
