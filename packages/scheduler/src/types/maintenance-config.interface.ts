import TypedEmitter from 'typed-emitter';
import { Duration } from 'moment-timezone';
import { DatabaseAdapter } from './database-adapter.interface';
import { GenericLogger } from './generic-logger.interface';
import { Events } from './events.interface';
import { MetricsClient } from './metrics-client.interface';

export interface MaintenanceConfig {
  /**
   * @description Database adapter to use for interacting with the database
   */
  adapter: DatabaseAdapter;

  /**
   * @description Logger instance for logging events
   */
  logger: GenericLogger;

  /**
   * @description Jobs that are safe to restart without implications
   * @example ['abandoned-carts-task', 'purge-carts-task']
   */
  jobsSafeToRestart: string[];

  /**
   * @description Custom duration (restart after) setting for jobs
   * @example {
   *   'order-schedule-reminder': moment.duration(30, 'minutes') //Start after 30 minutes of being laggy
   * }
   */
  jobsCustomRestart: Record<string, Duration | undefined>;

  /**
   * @description Jobs that are risky to restart, these are restarted according to their runtime rules
   * @example ['payments']
   */
  jobsRiskyToRestart: string[];

  /**
   * @description Event emitter for events such as jobs that are laggy, blocked, restarted, etc.
   */
  events: TypedEmitter<Events>;

  /**
   * @description After how many minutes, should the job be considered blocked
   * @default 5
   */
  blockedInMinutes?: number;

  /**
   * @description After how many minutes should the job be considered laggy
   * @default 6
   */
  lagInMinutes?: number;

  /**
   * @description StatsD client for collecting metrics
   */
  metrics: MetricsClient;

  /**
   * @description Service name to tag metrics with
   * @required
   */
  service: string;
}
