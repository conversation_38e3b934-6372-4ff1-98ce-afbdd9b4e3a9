import { JobBase } from './job-base.interface';
import { PendingJobs } from './pending-jobs.type';

export interface Events {
  /**
   * @description A list of jobs that have lagged and have not been restarted
   */
  lagged: (jobs: JobBase[]) => void;

  /**
   * @description a job that has been updated to run at the time provided
   */
  reset: (job: JobBase, nextRunAt: string) => void;

  /**
   * @description Job duration (only available when wrapped with the timestamp helper)
   */
  duration: (job: JobBase, timeSecs: number, success: boolean) => void;

  /**
   * @description An object of job names against pending jobs to run
   */
  pending: (data: PendingJobs) => void;
}
