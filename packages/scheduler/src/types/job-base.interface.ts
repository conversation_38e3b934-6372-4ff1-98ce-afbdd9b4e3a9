export interface JobBase {
  id: string;
  name: string;
  data: Record<string, any>;
  lastFinishedAt?: Date | null;
  lastRunAt?: Date | null;
  lastModifiedBy?: string | null;
  nextRunAt?: Date | null;
  acceptedAt?: Date | null;
  repeatInterval?: string | null;
  type?: string | null;
  failReason?: Record<string, any> | null;
  failedAt?: string | Date | null;
  priority?: number;
  failures?: number;
  queued?: boolean;
  timezone?: string;
  createdAt?: Date | null;
  updatedAt?: Date | null;
  deletedAt?: Date | null;
  namespace?: string | null;
}
