/**
 * @ordermentum/scheduler. A unified job scheduler for both Sequelize and Prisma databases.
 *
 * This library provides a robust scheduling system for background tasks and jobs.
 * It supports both Sequelize and Prisma ORMs, allowing seamless integration with
 * different database setups. Key features include:
 * - Scheduling jobs using iCal/RRULE format
 * - Automatic computation of next run times
 * - Maintenance system for recovering failed or stuck jobs
 * - Timezone-aware scheduling
 * - Graceful termination and concurrency management
 */

export { JobScheduler } from './scheduler';

export { SequelizeAdapter } from './adapters/sequelize.adapter';
export { PrismaAdapter } from './adapters/prisma.adapter';

export {
  computeNextRun,
  computeNextRuns,
  isHealthy,
  retryDelay,
} from './utils';

// Core Config and Dependency Types
export { JobSchedulerConfig } from './types/job-scheduler-config.interface';
export { DatabaseAdapter } from './types/database-adapter.interface';
export { GenericLogger } from './types/generic-logger.interface';
export { MetricsClient } from './types/metrics-client.interface';
// Job & Event related types needed by consumers
export { JobBase } from './types/job-base.interface';
export { JobContext, TaskHandler } from './types/task.type';
export { Events } from './types/events.interface';
// Default constants for configuration
export {
  DEFAULT_LAG,
  DEFAULT_BLOCKED_DURATION,
  DEFAULT_RUN_INTERVAL,
  DEFAULT_MAX_RESTARTS_ON_FAILURE,
  DEFAULT_BACKOFF,
} from './types/constants';
