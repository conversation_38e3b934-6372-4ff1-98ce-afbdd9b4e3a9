export class SchedulerError extends Error {
  constructor(message: string, public readonly cause?: Error) {
    super(message);
    this.name = 'SchedulerError';
  }
}

export class JobExecutionError extends SchedulerError {
  constructor(
    message: string,
    public readonly jobName: string,
    public readonly jobId: string,
    cause?: Error
  ) {
    super(message, cause);
    this.name = 'JobExecutionError';
  }
}
