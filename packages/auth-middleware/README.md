# Authentication middleware for express

This provides re-usable middlware for Ordermentum external and service-to-service auth

### Usage

*Requires req.token to be set beforehand - recommended to use express-bearer-token to set this from bearer/session cookie*

```typescript
import createAuth from '@ordermentum/auth-middleware';

const options = {
    JWTSecret: 'supersecrethushhush',
}

const userJWTAuth = createAuth(options);

// use userJWTAuth as middleware for routes that should support user JWT access
```

Optional custom check:

```typescript
import createAuth from '@ordermentum/auth-middleware';

const secretToken = 'pleasestopusingthismethodforauth';

const options = {
    JWTSecret: 'supersecrethushhush',
    customCheck: (req) => {
        return req.headers.seckret == secretToken
    }
}

const userJWTAuth = createAuth(options);

// Add userJWTAuth as middleware to routes that should support access via user auth or a "sekret" header
```

Optional fetch user from auth call:

```typescript
import createAuth from '@ordermentum/auth-middleware';

const authUrl = 'https://user:<EMAIL>';

const options = {
    JWTSecret: 'supersecrethushhush',
    fetchUser: true,
    authUrl,
}

const userJWTAuth = createAuth(options);

// On successful call req.user is now the full auth user data, including context etc
```

### Additional request data

On successful decoding the middleware adds a user property onto the request object containing the user id from the JWT:

```typescript
const req = {
    user: {
        id: 'some-uuid-will-be-here',
    }
}
```

## TODO:
* Needs OM linting setup
