{"name": "@ordermentum/auth-middleware", "version": "4.0.0", "description": "Authentication middleware for OM", "main": "build/src/index.js", "types": "build/src/index.d.ts", "scripts": {"spec": "mocha -r ts-node/register 'test/**/*.ts'", "prepublish": "npm run build", "build": "yarn run tsc", "lint": "eslint src/** test/**"}, "repository": {"type": "git", "url": "git+https://github.com/ordermentum/auth-middleware.git"}, "files": ["build"], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/ordermentum/auth-middleware/issues"}, "homepage": "https://github.com/ordermentum/auth-middleware#readme", "devDependencies": {"@types/chai": "^4.3.3", "@types/hapi-auth-bearer-token": "^6.1.3", "@types/mocha": "^9.1.1", "@types/node": "^18.7.15", "@types/sinon": "^10.0.13", "bunyan": "1.8.15", "chai": "^3.5.0", "eslint": "^8.57.0", "istanbul": "^0.4.4", "mocha": "^2.5.3", "nyc": "^10.3.0", "ts-node": "^10.9.1", "typescript": "5.1.6"}, "dependencies": {"@hapi/hapi": "20.3.0", "@ordermentum/auth-driver": "^7.2.0", "@ordermentum/hapi-asap": "^0.1.3", "hapi-auth-bearer-token": "8.0.0", "http-errors": "^1.8.0", "jsonwebtoken": "^8.5.1"}}