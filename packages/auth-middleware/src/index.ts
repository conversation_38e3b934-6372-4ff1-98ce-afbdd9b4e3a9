import httpErrors from 'http-errors';
import jwt from 'jsonwebtoken';
import createClient, { createOauthClient } from './auth-client';
import {
  plugin as hapiAuthPlugin,
  AuthArtifacts,
  HapiAuthPluginOptions,
} from './hapi-plugin';

export type OAuthOptions = {
  clientId: string;
  clientSecret: string;
  authURL: string;
};

export type JWTOptions = {
  // Shared JWT secret
  JWTSecret: string;

  // if we accept expired tokens
  enforceExpiration?: boolean;

  // Auth url with user/pass encoded in it e.g. https://user:<EMAIL>
  authURL?: string;
};

export type middlewareOptions = {
  // Function for alternative auth path, takes precedence over JWT
  customCheck?: (request) => boolean;
  // Fetch user document after successfully resolving JWT?
  fetchUser?: boolean;
};

/**
 * Create a middleware function that checks req.token is a valid JWT
 * Optionally provide a function to do a custom check (e.g. magic header) that will take precedence
 * @param opts
 */
function createAuth(opts: middlewareOptions & (OAuthOptions | JWTOptions)) {
  let authClient;
  if ('clientId' in opts) {
    authClient = createOauthClient(
      opts.authURL,
      opts.clientId,
      opts.clientSecret
    );
  } else if (opts.fetchUser && opts.authURL) {
    authClient = createClient(opts.authURL);
  }

  return async (req, _, next) => {
    if (opts.customCheck) {
      if (opts.customCheck(req)) {
        return next();
      }
    }

    const { token } = req;
    if (token) {
      let tokenData;
      try {
        if ('clientId' in opts) {
          tokenData = await authClient.userInfo(token);
        } else {
          tokenData = jwt.verify(token, opts.JWTSecret, {
            ignoreExpiration: !opts.enforceExpiration,
          });
        }
      } catch (e) {
        const error = new httpErrors.Unauthorized('Unauthorized');
        return next(error);
      }

      if (tokenData?.id) {
        if (opts.fetchUser && authClient) {
          try {
            req.user =
              'clientId' in opts
                ? tokenData
                : await authClient.get(tokenData.id);
          } catch (fetchError) {
            return next(
              new httpErrors.Unauthorized((fetchError as Error).message)
            );
          }
        } else {
          req.user = {
            id: tokenData.id,
          };
        }

        return next();
      }
    }

    const error = new httpErrors.Unauthorized('Unauthorized');
    return next(error);
  };
}

export default createAuth;
export { hapiAuthPlugin, AuthArtifacts, HapiAuthPluginOptions };
