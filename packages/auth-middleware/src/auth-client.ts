import AuthDriver, { OAuth } from '@ordermentum/auth-driver';

export default function createClient(authUrl: string) {
  const components = new URL(authUrl);
  const { username, password, origin } = components;
  return new AuthDriver(origin, { username, password });
}

export function createOauthClient(
  authUrl: string,
  clientId: string,
  clientSecret: string
) {
  return new OAuth({
    host: authUrl,
    clientId,
    clientSecret,
  });
}
