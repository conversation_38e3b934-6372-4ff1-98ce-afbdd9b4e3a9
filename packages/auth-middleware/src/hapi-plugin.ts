import AuthBearer from 'hapi-auth-bearer-token';
import Driver, {
  EntityResponse,
  OAuth,
  Authorization,
} from '@ordermentum/auth-driver';
import {
  AuthenticatorOptions,
  register as registerAsapStrategy,
} from '@ordermentum/hapi-asap';
import { Plugin, Server } from '@hapi/hapi';
import jwt from 'jsonwebtoken';
import { middlewareOptions, OAuthOptions, JWTOptions } from './index';
import createClient, { createOauthClient } from './auth-client';
import { version } from '../package.json';

export type HapiAuthPluginOptions = middlewareOptions &
  (OAuthOptions | JWTOptions) & {
    // lookup name for token - Default(token)
    accessTokenName?: string;
    // Accept token via query parameter - Default(false)
    allowQueryToken?: boolean;
    // Accept token via cookie - Default(true)
    allowCookieToken?: boolean;
    // if we accept expired tokens
    enforceExpiration?: boolean;
    // Accept multiple authorization headers - Default(false)
    // e.g. Authorization: FD AF6C74D1-BBB2-4171-8EE3-7BE9356EB018; Bearer 12345678.
    allowMultipleHeaders?: boolean;
    // Accept a custom token Default(Bearer)
    // e.g.  Authorization: Basic 12345678.
    tokenType?: string;
    asap?: AuthenticatorOptions;
  };

export interface AuthArtifacts {
  user:
    | EntityResponse
    | {
        id: string;
      };
  auth: Authorization | null;
}

async function validateJWT(
  token: string,
  opts: middlewareOptions & JWTOptions,
  authClient: Driver
) {
  let tokenData;
  try {
    tokenData = jwt.verify(token, opts.JWTSecret, {
      ignoreExpiration: !opts.enforceExpiration,
    });
  } catch (e) {
    return { isValid: false, credentials: {} };
  }

  if (tokenData?.id) {
    const artifacts: AuthArtifacts = {
      user: {
        id: tokenData.id,
      },
      auth: null,
    };
    if (opts.fetchUser && authClient) {
      try {
        const authUser = await authClient.get(tokenData.id);
        if (!authUser) {
          return { isValid: false, credentials: {} };
        }
        artifacts.user = authUser;
        artifacts.auth = new Authorization(authUser);
      } catch (fetchError) {
        return { isValid: false, credentials: {} };
      }
    }
    return {
      isValid: true,
      credentials: {
        token,
      },
      artifacts,
    };
  }
  return { isValid: false, credentials: {} };
}

async function validateOauth(token: string, authClient: OAuth) {
  const authUser = await authClient.userInfo(token).catch(_ => null);
  if (!authUser) {
    return { isValid: false, credentials: {} };
  }

  const artifacts: AuthArtifacts = {
    user: authUser,
    auth: new Authorization(authUser),
  };

  return {
    isValid: true,
    credentials: {
      token,
    },
    artifacts,
  };
}

/**
 * Create a register function that can be used with @hapi/glue (https://hapi.dev/module/glue) that checks
 * - if JWT based, req.token is a valid JWT
 * - if OAuth based, check against the API
 * Optionally provide a function to do a custom check (e.g. magic header) that will take precedence
 * @param opts
 */
function implementation(server: Server, opts: HapiAuthPluginOptions) {
  let authClient;
  if ('clientId' in opts) {
    authClient = createOauthClient(
      opts.authURL,
      opts.clientId,
      opts.clientSecret
    );
  } else if (opts.fetchUser && opts.authURL) {
    authClient = createClient(opts.authURL);
  }
  if (opts.asap) {
    registerAsapStrategy(server, opts.asap);
  }
  server.register(AuthBearer);
  server.auth.strategy('auth', 'bearer-access-token', {
    allowQueryToken: opts.allowQueryToken ?? false,
    allowCookieToken: opts.allowCookieToken ?? true,
    accessTokenName: opts.accessTokenName ?? 'token',
    tokenType: opts.tokenType ?? 'Bearer',
    validate: async (req: any, token: string) => {
      if (opts.customCheck) {
        const isValid = opts.customCheck(req);
        if (isValid) {
          return {
            isValid: true,
            credentials: {},
          };
        }
      }
      if (opts.asap) {
        const auth = await server.auth.test('hapi-asap', req).catch(_ => null);
        if (auth?.credentials)
          return {
            isValid: true,
            credentials: auth.credentials,
            artifacts: auth.artifacts ?? {},
          };
      }
      if (token) {
        if ('clientId' in opts) return validateOauth(token, authClient);
        return validateJWT(token, opts, authClient);
      }
      return { isValid: false, credentials: {} };
    },
  });
}

const register = async (
  server: Server,
  options: HapiAuthPluginOptions
): Promise<void> => {
  await implementation(server, options);
  server.auth.default('auth');
};

export const plugin: Plugin<HapiAuthPluginOptions> = {
  register,
  pkg: { name: 'Auth Middleware', version },
};

export default plugin;
