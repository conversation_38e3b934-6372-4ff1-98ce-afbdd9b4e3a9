import { expect } from 'chai';
import express from 'express';
import jwt from 'jsonwebtoken';
import request from 'supertest';
import nock from 'nock';
import type { User } from '@ordermentum/auth-driver';

import createAuth from '../src';

const JWTSecret = 'testytestsosecretsosecure';

function testHandler(req, res) {
  return res.json({
    user: req.user,
    id: req.user?.id,
  });
}

function injectToken(req, _, next) {
  req.token = req.headers.testauth;
  next();
}

describe('Auth middleware', () => {
  describe('With no custom function', () => {
    let server;

    before(() => {
      server = express();
      const userAuth = createAuth({
        JWTSecret,
      });
      server.use(injectToken);
      server.get('/', userAuth, testHandler);
    });

    it('should reject invalid tokens', async () =>
      request(server).get('/').set('testauth', 'notlegittokenk').expect(401));

    it('should parse legitimate tokens', async () => {
      const id = 'notauuidbutyougetthepicture';
      const token = jwt.sign({ id }, JWTSecret);

      const res = await request(server)
        .get('/')
        .set('testauth', token)
        .expect(200);

      expect(res.body.user).to.exist;
      expect(res.body.id).to.equal(id);
    });
  });

  describe('With a custom function', () => {
    let server;

    before(() => {
      server = express();
      const userAuth = createAuth({
        JWTSecret,
        customCheck: req => req.headers.super === 'WINNER',
      });
      server.use(injectToken);
      server.get('/', userAuth, testHandler);
    });

    it('should use the result of the custom function when set', async () => {
      const res = await request(server)
        .get('/')
        .set('super', 'WINNER')
        .expect(200);

      expect(res.body.user).to.be.undefined;
      expect(res.body.id).to.be.undefined;
    });

    it('should reject invalid tokens when custom function is false', async () =>
      request(server).get('/').set('testauth', 'notlegittokenk').expect(401));

    it('should parse legitimate tokens when custom function is false', async () => {
      const id = 'notauuidbutyougetthepicture';
      const token = jwt.sign({ id }, JWTSecret);

      const res = await request(server)
        .get('/')
        .set('testauth', token)
        .expect(200);

      expect(res.body.user).to.exist;
      expect(res.body.id).to.equal(id);
    });
  });

  describe('With fetchUser', () => {
    let server;
    const authURL = 'https://auth.ordermentum.com';

    before(() => {
      server = express();
      const userAuth = createAuth({
        JWTSecret,
        fetchUser: true,
        authURL,
      });
      server.use(injectToken);
      server.get('/', userAuth, testHandler);
    });

    afterEach(() => {
      nock.cleanAll();
    });

    it('should fetch the user from auth and set as req.user', async () => {
      const id = 'notauuidbutyougetthepicture';
      const token = jwt.sign({ id }, JWTSecret);
      // @ts-expect-error
      const dummyData: User = {
        admin: false,
        context: {
          suppliers: [],
          retailers: [],
          supplierPrivileges: {},
          retailerPrivileges: {},
          admin: false,
          superAdmin: false,
        },
        created_at: '',
        firstName: '',
        lastName: '',
        metadata: { type: '' },
        name: { firstName: '', full: '', lastName: '' },
        password: '',
        superAdmin: false,
        userRoles: [],
        id,
        emails: [`${id}@ordermentum.com`],
      };

      const authCall = nock(authURL)
        .get(`/entities/${id}`)
        .reply(200, dummyData);

      const res = await request(server)
        .get('/')
        .set('testauth', token)
        .expect(200);

      expect(res.body.user).to.exist;
      expect(res.body.id).to.equal(id);
      authCall.done();
    });

    it('should error out when failing to fetch user', async () => {
      const id = 'notauuidbutyougetthepicture';
      const token = jwt.sign({ id }, JWTSecret);

      const authCall = nock(authURL).get(`/entities/${id}`).reply(404);

      const res = await request(server)
        .get('/')
        .set('testauth', token)
        .expect(401);

      expect(res.body.user).to.not.exist;
      authCall.done();
    });
  });
});
