{"name": "@ordermentum/payments-javascript-sdk", "version": "4.12.0", "description": "Javascript SDK for OM payments", "main": "build/index.js", "types": "build/index.d.ts", "scripts": {"spec": "mocha -r ts-node/register 'test/**/*.ts'", "prepublish": "npm run build", "build": "yarn run tsc"}, "repository": {"type": "git", "url": "git+https://github.com/ordermentum/auth-middleware.git"}, "files": ["build"], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/ordermentum/auth-middleware/issues"}, "homepage": "https://github.com/ordermentum/auth-middleware#readme", "devDependencies": {"@types/chai": "^4.3.3", "@types/hapi-auth-bearer-token": "^6.1.3", "@types/mocha": "^9.1.1", "@types/node": "^18.7.15", "@types/sinon": "^17.0.3", "@types/bunyan": "1.8.11", "chai": "^3.5.0", "eslint": "^8.57.0", "istanbul": "^0.4.4", "mocha": "^2.5.3", "nyc": "^10.3.0", "ts-node": "^10.9.1", "typescript": "5.1.6"}, "dependencies": {"axios": "^1.7.7", "bunyan": "1.8.15", "moment": "2.30.1", "null-logger": "1.0.0", "qs": "6.14.0", "sinon": "17.0.1", "zod": "^3.25.30"}}