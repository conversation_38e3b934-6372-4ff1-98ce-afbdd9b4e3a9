# @ordermentum/payments-javascript-sdk

## 4.12.0

### Minor Changes

- 99cfb87: Add promotional payments types

## 4.11.1

### Patch Changes

- bd042a7: Ensure currency types are exported from the library

## 4.11.0

### Minor Changes

- 4ee73b2: Realign the PaymentTransaction type in payments-javascript-sdk to the SerializeAttributes object for the Transaction model in Payments.

  Note: This change breaks backwards compatibility with magic string definitions of some fields of PaymentTransaction, these should be converted to use the enums as part of the upgrade.

## 4.10.0

### Minor Changes

- 4496816: Revert error handling changes

## 4.9.0

### Minor Changes

- 64afcd2: Updated upfront payments API types and endpoints:

  - **Removed** `immediate` field from `CreateUpfrontPaymentsPayload`
  - **Removed** `documentId` field from `CreateUpfrontPaymentsPayload`
  - **Changed** `invoiceIds: string[]` to `invoiceId: string` (singular) in `CreateUpfrontPaymentsPayload`
  - **Removed** `FinaliseUpfrontPaymentsResponse` type - finalise endpoint now returns void
  - Enhanced test coverage and documentation for upfront payment types

## 4.8.0

### Minor Changes

- 2a6fdf9: Adding zai error context to the sdk error result

## 4.7.0

### Minor Changes

- 8f4dea3: Added `topUpAmountInCents` and `refundAmountInCents` fields to `FinaliseUpfrontPaymentsResponse` type. These fields are always present in the response - when no top-up or refund is required, they will have a value of 0. Enhanced test coverage and documentation for upfront payment response structures.

## 4.7.0

### Minor Changes

- Added `topUpAmountInCents` and `refundAmountInCents` fields to `FinaliseUpfrontPaymentsResponse` type
- Enhanced test coverage for upfront payment response structures
- Improved documentation for upfront payment types

## 4.6.0

### Minor Changes

- 45ead86: Minor changes to the upfront request types

## 4.5.0

### Minor Changes

- f366f23: Remove unused properties on the upfront payment type

## 4.4.0

### Minor Changes

- 3a0b337: Added upfront payments endpoint

## 4.3.0

### Minor Changes

- 0e3d024: Added v3 endpoints for transactions and users

## 4.2.0

### Minor Changes

- 085138f: Bump payment sdk axios version

## 4.1.0

### Minor Changes

- 45176e7: Adding v2/users/hold_funds

## 4.0.0

### Major Changes

- 127de39: Add async transactions api and add versioning to resources

## 3.1.1

### Patch Changes

- b95641b: Add wallet withdraw funds and wallet balance apis to user resource

## 3.1.0

### Minor Changes

- a87da83: Added update funding route

## 3.0.0

### Major Changes

- 95128ab: Add payments javsacript sdk
