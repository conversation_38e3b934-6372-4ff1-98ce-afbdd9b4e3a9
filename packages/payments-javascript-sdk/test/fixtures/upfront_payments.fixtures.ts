import { randomUUID } from 'crypto';
import {
  CancelUpfrontPaymentPayload,
  CreateUpfrontPaymentsPayload,
  FinaliseUpfrontPaymentsPayload,
  CreateUpfrontPaymentResponse,
} from '../../src/types';

export const mockUpfrontPaymentTransaction = (
  attrs?: Partial<CreateUpfrontPaymentsPayload>
) => ({
  amountCents: 0,
  description: 'Sample Description',
  sellerUserId: randomUUID(),
  buyerUserId: randomUUID(),
  orderId: randomUUID(),
  invoiceId: randomUUID(),
  userId: randomUUID(),
  paymentMethodId: randomUUID(),
  ...attrs,
});

export const mockFinaliseUpfrontPaymentTransaction = (
  attrs?: Partial<FinaliseUpfrontPaymentsPayload>
) => ({
  finalAmountCents: 0,
  orderId: randomUUID(),
  invoiceIds: [randomUUID()],
  description: randomUUID(),
  ...attrs,
});

export const mockCancelAndRevertUpfrontPaymentTransaction = (
  attrs?: Partial<CancelUpfrontPaymentPayload>
) => ({
  orderId: randomUUID(),
  reason: 'OM description',
  ...attrs,
});

export const mockCreateUpfrontPaymentResponse = (
  attrs?: Partial<CreateUpfrontPaymentResponse>
): CreateUpfrontPaymentResponse => ({
  holdingTransactionId: randomUUID(),
  ...attrs,
});
