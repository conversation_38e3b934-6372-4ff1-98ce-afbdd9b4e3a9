import { randomUUID } from 'node:crypto';
import {
  PromotionalPaymentRequest,
  PromotionalPaymentResponse,
} from '../../src/types/promotional_payments.types';

export const mockPromotionalPaymentRequest = (
  attrs?: Partial<PromotionalPaymentRequest>
): PromotionalPaymentRequest => ({
  promoAmountCents: 1000,
  purchaseAmountCents: 5000,
  senderId: randomUUID(),
  senderPaymentMethodId: randomUUID(),
  recipientId: randomUUID(),
  orderIds: [randomUUID()],
  invoiceIds: [randomUUID()],
  description: 'Sample promotional payment',
  ...attrs,
});

export const mockPromotionalPaymentResponse = (
  attrs?: Partial<PromotionalPaymentResponse>
): PromotionalPaymentResponse => ({
  paymentTransactionId: randomUUID(),
  promoTransactionId: randomUUID(),
  ...attrs,
});
