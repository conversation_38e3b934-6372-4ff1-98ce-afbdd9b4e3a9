import { expect } from 'chai';
import logger from 'null-logger';
import PaymentsClient from '../src/client';

describe('Client', () => {
  it('return an instance', async () => {
    const apiBase = 'https://payment-service.ordermentum.com';
    // @ts-expect-error
    const client = new PaymentsClient({ apiBase, logger, auth: '123' });

    expect(client.logger).to.equal(logger);
    expect(client.apiBase).to.equal(apiBase);
  });

  it('should get parsed json', async function test() {
    this.timeout(10000);
    const apiBase = 'http://jsonip.com/';
    const auth = {
      username: 'test',
      password: 'test',
    };
    // @ts-expect-error
    const client = new PaymentsClient({ apiBase, logger, auth });
    // @ts-expect-error
    const resp = await client.get('/');

    expect(resp.ip).to.not.equal(null);
    expect(resp.status).to.not.equal(null);
  });

  it('should throw error', async function test() {
    this.timeout(10000);
    const apiBase = 'https://mock.codes';
    const auth = {
      username: 'test',
      password: 'test',
    };
    const client = new PaymentsClient({
      apiBase,
      logger,
      // @ts-expect-error
      auth,
      sentry: { captureException: () => {} },
    });
    let thrown = false;

    try {
      await client.get({}, '500');
    } catch (e) {
      thrown = true;
      expect((e as any).status).to.equal(500);
      expect((e as any).error).to.equal('Internal Server Error');
    }

    expect(thrown).to.equal(true);
  });

  it('should create basic auth', () => {
    // @ts-expect-error
    const client = new PaymentsClient({
      apiBase: 'http://a.com',
      logger,
      username: 'abc',
      password: '123',
    });
    expect(client.authentication()).to.equal('YWJjOjEyMw==');
  });

  it('should delete', async function test() {
    this.timeout(10000);
    const apiBase = 'https://jsonplaceholder.typicode.com/';
    const auth = {
      username: 'test',
      password: 'test',
    };
    // @ts-expect-error
    const client = new PaymentsClient({ apiBase, logger, auth });
    const resp = await client.delete('/posts/1');
    expect(resp).to.deep.equal({});
  });

  it('should update', async function test() {
    this.timeout(10000);
    const apiBase = 'https://jsonplaceholder.typicode.com/';
    const auth = {
      username: 'test',
      password: 'test',
    };
    // @ts-expect-error
    const client = new PaymentsClient({ apiBase, logger, auth });
    const resp = await client.put('/posts/1', { params: { id: '1' } });
    expect(resp.id).to.equal(1);
  });

  it('should post', async function test() {
    this.timeout(10000);
    const apiBase = 'https://jsonplaceholder.typicode.com/';
    const auth = {
      username: 'test',
      password: 'test',
    };
    // @ts-expect-error
    const client = new PaymentsClient({ apiBase, logger, auth });
    const resp = await client.post('/posts/', { params: { id: '1' } });
    expect(resp.id).to.equal(101);
  });

  it('should patch', async function test() {
    this.timeout(10000);
    const apiBase = 'https://jsonplaceholder.typicode.com/';
    const auth = {
      username: 'test',
      password: 'test',
    };
    // @ts-expect-error
    const client = new PaymentsClient({ apiBase, logger, auth });
    const resp = await client.patch('/posts/1', { params: { id: '1' } });
    expect(resp.id).to.equal(1);
  });
});
