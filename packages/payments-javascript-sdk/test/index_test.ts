import { expect } from 'chai';
import sinon from 'sinon';
import axios from 'axios';
import createClient from '../src';
import {
  mockCancelAndRevertUpfrontPaymentTransaction,
  mockFinaliseUpfrontPaymentTransaction,
  mockUpfrontPaymentTransaction,
  mockCreateUpfrontPaymentResponse,
} from './fixtures/upfront_payments.fixtures';

describe('Default export', () => {
  let axiosStub: sinon.SinonStub;
  const client = createClient({
    username: '',
    password: '',
    callback: () => {},
  });

  beforeEach(() => {
    axiosStub = sinon.stub().resolves({ data: {} });
    sinon.stub(axios, 'create').returns({
      post: axiosStub,
      interceptors: {
        response: {
          // @ts-expect-error
          use: () => {},
        },
      },
    });
  });

  afterEach(() => {
    sinon.restore();
  });

  it('makes a POST call to /v2/transactions when using the async transactions resource', async () => {
    await client.asyncTransactions.create({ params: { id: '1' } });
    expect(axiosStub.called).to.be.true;
    expect(axiosStub.args[0][0]).to.equal('/v2/transactions');
  });

  it('makes a POST call to v2/users/{id}/hold-funds when using the async hold funds', async () => {
    await client.asyncUsers.holdFunds(1, { id: '1' });
    expect(axiosStub.called).to.be.true;
    expect(axiosStub.args[0][0]).to.equal('/v2/users/1/hold-funds');
  });

  it('makes a POST call to /v3/upfront-payments', async () => {
    const upfrontPaymentTransaction = mockUpfrontPaymentTransaction();
    await client.upfrontPayments.create(upfrontPaymentTransaction);
    expect(axiosStub.called).to.be.true;
    expect(axiosStub.args[0][0]).to.equal('/v3/upfront-payments');
  });

  it('returns correct response structure for upfront payment creation', async () => {
    const mockResponse = mockCreateUpfrontPaymentResponse();
    axiosStub.resolves({ data: mockResponse });

    const upfrontPaymentTransaction = mockUpfrontPaymentTransaction();
    const response = await client.upfrontPayments.create(
      upfrontPaymentTransaction
    );

    expect(response).to.deep.equal(mockResponse);
    expect(response.holdingTransactionId).to.equal(
      mockResponse.holdingTransactionId
    );
  });

  it('makes a POST call to /v3/upfront-payments/{id}/finalise when using the new hold funds', async () => {
    const finaliseUpfrontTransaction = mockFinaliseUpfrontPaymentTransaction();
    await client.upfrontPayments.finalise(finaliseUpfrontTransaction);

    expect(axiosStub.called).to.be.true;
    expect(axiosStub.args[0][0]).to.equal(`/v3/upfront-payments/finalise`);
  });

  it('makes a POST call to /v3/upfront-payments/{id}/cancel-and-revert when using the new revert endpoint', async () => {
    const cancelAndRevertTransaction =
      mockCancelAndRevertUpfrontPaymentTransaction();
    await client.upfrontPayments.cancelAndRevert(cancelAndRevertTransaction);

    expect(axiosStub.called).to.be.true;
    expect(axiosStub.args[0][0]).to.equal(
      `/v3/upfront-payments/cancel-and-revert`
    );
  });
});
