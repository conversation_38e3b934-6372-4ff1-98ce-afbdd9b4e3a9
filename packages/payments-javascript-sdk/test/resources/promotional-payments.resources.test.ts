import { expect } from 'chai';
import sinon from 'sinon';
import axios from 'axios';
import createClient from '../../src';
import {
  mockPromotionalPaymentRequest,
  mockPromotionalPaymentResponse,
} from '../fixtures/promotional_payments.fixtures';

describe('promotional-payments.resources', () => {
  let axiosStub: sinon.SinonStub;
  const client = createClient({
    username: '',
    password: '',
    callback: () => {},
  });

  beforeEach(() => {
    axiosStub = sinon.stub().resolves({ data: {} });
    sinon.stub(axios, 'create').returns({
      post: axiosStub,
      interceptors: {
        response: {
          // @ts-expect-error
          use: () => {},
        },
      },
    });
  });

  afterEach(() => {
    sinon.restore();
  });

  it('makes a POST call to /v3/promotional-payments when creating a promotional payment', async () => {
    const promotionalPaymentRequest = mockPromotionalPaymentRequest();
    await client.promotionalPayments.makePromotionalPayment(
      promotionalPaymentRequest
    );

    expect(axiosStub.called).to.be.true;
    expect(axiosStub.args[0][0]).to.equal('/v3/promotional-payments');
  });

  it('returns correct response structure for promotional payment creation', async () => {
    const mockResponse = mockPromotionalPaymentResponse();
    axiosStub.resolves({ data: mockResponse });

    const promotionalPaymentRequest = mockPromotionalPaymentRequest();
    const response = await client.promotionalPayments.makePromotionalPayment(
      promotionalPaymentRequest
    );

    expect(response).to.deep.equal(mockResponse);
    expect(response.paymentTransactionId).to.equal(
      mockResponse.paymentTransactionId
    );
    expect(response.promoTransactionId).to.equal(
      mockResponse.promoTransactionId
    );
  });

  it('sends correct payload structure for promotional payment', async () => {
    const promotionalPaymentRequest = mockPromotionalPaymentRequest({
      promoAmountCents: 2000,
      purchaseAmountCents: 10000,
      description: 'Test promotional payment',
    });

    await client.promotionalPayments.makePromotionalPayment(
      promotionalPaymentRequest
    );

    expect(axiosStub.called).to.be.true;
    expect(axiosStub.args[0][1]).to.deep.equal(promotionalPaymentRequest);
  });
});
