import sinon from 'sinon';
import { expect } from 'chai';
import NULL_LOGGER from 'null-logger';
import resource from '../src/resources/resource';

const FAKE_CLIENT = {
  post() {},
  patch() {},
  put() {},
  get() {},
  delete() {},
  logger: NULL_LOGGER,
};

describe('resource', () => {
  const path = '/v1/cats';
  const instance = resource(path);
  // @ts-expect-error
  const cats = instance(FAKE_CLIENT);
  it('return an instance', () => {
    expect(cats.path).to.equal(path);
    expect(cats.client).to.equal(FAKE_CLIENT);
  });

  it('findById', async () => {
    const cat = { id: '123', name: 'asj' };
    const getStub = sinon
      .stub(FAKE_CLIENT, 'get')
      // @ts-expect-error
      .returns(new Promise((resolve) => resolve({ id: '123', name: 'asj' })));
    const response = await cats.findById('123');
    expect(getStub.called).to.be.equal(true);
    expect(response).to.deep.equal(cat);
    getStub.restore();
  });

  it('search', async () => {
    const getStub = sinon
      .stub(FAKE_CLIENT, 'get')
      // @ts-expect-error
      .returns(new Promise((resolve) => resolve({ id: '123', name: 'asj' })));
    await cats.search({ filter: { user_id: '123' } });
    expect(getStub.called).to.be.equal(true);
    getStub.restore();
  });

  it('findOne', async () => {
    const getStub = sinon.stub(FAKE_CLIENT, 'get').returns(
      // @ts-expect-error
      new Promise((resolve) =>
        resolve([
          { id: '123', name: 'asj' },
          { id: '123', name: 'dwag' },
        ])
      )
    );
    await cats.findOne({ filter: { user_id: '123' } });
    expect(getStub.called).to.be.equal(true);
    getStub.restore();
  });

  it('create', async () => {
    const cat = { name: 'droppo' };
    const postStub = sinon
      .stub(FAKE_CLIENT, 'post')
      // @ts-expect-error
      .returns(new Promise((resolve) => resolve(cat)));
    const response = await cats.create(cat);
    expect(postStub.called).to.be.equal(true);
    expect(response).to.deep.equal(cat);
    postStub.restore();
  });

  it('update', async () => {
    const cat = { name: 'droppo' };
    const putStub = sinon
      .stub(FAKE_CLIENT, 'put')
      // @ts-expect-error
      .returns(new Promise((resolve) => resolve(cat)));
    const response = await cats.update('123', cat);
    expect(putStub.called).to.be.equal(true);
    expect(response).to.deep.equal(cat);
    putStub.restore();
  });

  it('destroy', async () => {
    const destroyStub = sinon
      .stub(FAKE_CLIENT, 'delete')
      // @ts-expect-error
      .returns(new Promise((resolve) => resolve('done')));
    //@ts-expect-error
    const response = await cats.destroy(123);
    expect(destroyStub.called).to.be.equal(true);
    expect(response).to.deep.equal('done');
    destroyStub.restore();
  });

  it('update custom', async () => {
    const cat = { name: 'droppo' };
    const putStub = sinon
      .stub(FAKE_CLIENT, 'put')
      // @ts-expect-error
      .returns(new Promise((resolve) => resolve(cat)));
    const response = await cats.update('123', cat, 'refund');
    expect(putStub.called).to.be.equal(true);
    expect(response).to.deep.equal(cat);
    putStub.restore();
  });
});
