import { expect } from 'chai';
import { stub } from 'sinon';
import PaymentsClient from '../src/client';

describe('Error handler', () => {
  it('should capture error', async function test() {
    this.timeout(10000);
    const sentry = {
      captureException: stub(),
    };
    const apiBase = 'https://mock.codes';
    const logger = console;
    const auth = {
      username: 'test',
      password: 'test',
    };
    // @ts-expect-error
    const client = new PaymentsClient({ apiBase, logger, auth, sentry });

    let thrown = false;
    try {
      await client.get({}, '500');
    } catch (e) {
      thrown = true;
      expect((e as any).status).to.equal(500);
      expect((e as any).error).to.equal('Internal Server Error');
    }

    expect(thrown).to.equal(true);
  });
});
