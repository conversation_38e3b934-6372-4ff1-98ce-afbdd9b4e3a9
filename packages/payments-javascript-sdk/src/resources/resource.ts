import Client from '../client';

export default function resource(path: string) {
  return (client: Client) => ({
    path,
    client,

    findOne(params: any = {}) {
      return this.search(params).then((items: any[]) => items[0]);
    },

    search(params: any = {}) {
      client.logger.trace('search with params', { params }, this.path);
      return client.get({ params }, this.path);
    },

    findById(id: string) {
      client.logger.trace('findById', { path: this.path, id });
      return client.get({}, `${this.path}/${id}`);
    },

    create(params: any = {}) {
      return client.post(this.path, params);
    },

    destroy(id: string) {
      client.logger.trace('destory', { path: this.path, id });
      return client.delete(`${this.path}/${id}`);
    },

    update(id: string, params: object = {}, childPath = '') {
      const newPath = childPath
        ? `${this.path}/${id}/${childPath}`
        : `${this.path}/${id}`;
      return client.put(`${newPath}`, params);
    },
  });
}
