import Client from '../client';
import {
  CancelUpfrontPaymentPayload,
  CreateUpfrontPaymentResponse,
  CreateUpfrontPaymentsPayload,
  FinaliseUpfrontPaymentsPayload,
} from '../types/upfront_payments.types';

export const upfrontPayments = function (path: string) {
  return (client: Client) => ({
    path,
    client,
    create(
      payload: CreateUpfrontPaymentsPayload
    ): Promise<CreateUpfrontPaymentResponse> {
      return client.post(this.path, payload);
    },
    finalise(payload: FinaliseUpfrontPaymentsPayload): Promise<void> {
      return client.post(`${this.path}/finalise`, payload);
    },
    cancelAndRevert(payload: CancelUpfrontPaymentPayload): Promise<void> {
      return client.post(`${this.path}/cancel-and-revert`, payload);
    },
  });
};
