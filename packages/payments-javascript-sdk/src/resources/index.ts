import batch from './batch';
import paymentRunCharges from './payment-run-charges';
import subscription from './subscription';
import resource from './resource';
import users from './users';
import asyncUsers from './async-users';
import { asyncTransactions } from './async-transactions';
import { upfrontPayments } from './upfront-payments.resources';
import { promotionalPayments } from './promotional-payments.resources';

export default {
  users: users('/v1/users'),
  transactions: resource('/v1/transactions'),
  paymentMethods: resource('/v1/payment-methods'),
  paymentRuns: resource('/v1/payment-runs'),
  paymentRunCharges: paymentRunCharges('/v1/payment-run-charges'),
  bankAccounts: resource('/v1/bank-accounts'),
  cards: resource('/v1/cards'),
  batch: batch('/v1/batch'),
  subscriptions: subscription('/v1/subscriptions'),
  asyncTransactions: asyncTransactions('/v2/transactions'),
  asyncUsers: asyncUsers('/v2/users'),
  upfrontPayments: upfrontPayments('/v3/upfront-payments'),
  promotionalPayments: promotionalPayments('/v3/promotional-payments'),
};
