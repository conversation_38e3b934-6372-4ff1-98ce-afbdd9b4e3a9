import Client from '../client';

export default function resource(path: string) {
  return (client: Client) => ({
    path,
    client,
    getList(userId: string) {
      client.logger.trace('getList', { path: '/users', userId });
      return client.get({}, `/users/${userId}/subscriptions`);
    },
    getUsage(userId: string) {
      client.logger.trace('getUsage', { path: '/users', userId });
      return client.get({}, `/users/${userId}/subscriptions/usage`);
    },
    incrementUsage(userId: string, body: any) {
      client.logger.trace('getUsage', { path: '/users', userId });
      return client.post(`/users/${userId}/subscriptions/usage`, body);
    },
  });
}
