import Client from '../client';
import {
  PromotionalPaymentRequest,
  PromotionalPaymentResponse,
} from '../types/promotional_payments.types';

export const promotionalPayments = function (path: string) {
  return (client: Client) => ({
    path,
    client,
    makePromotionalPayment(
      payload: PromotionalPaymentRequest
    ): Promise<PromotionalPaymentResponse> {
      return client.post(this.path, payload);
    },
  });
};
