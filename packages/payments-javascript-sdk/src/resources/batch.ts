import Client from '../client';

export default function resource(path: string) {
  return (client: Client) => ({
    path,
    client,
    findById(id: string) {
      client.logger.trace('findById', { path: this.path, id });
      return client.get({}, `${this.path}/${id}`);
    },
    getInvoiceIds(id: string) {
      client.logger.trace('getInvoices', { path: this.path, id });
      return client.get({}, `${this.path}/${id}/invoices`);
    },
    search(params = {}) {
      client.logger.trace('search with params', { params }, this.path);
      return client.get({ params }, this.path);
    },
    sync(body: { entityId: string }) {
      client.logger.trace('sync', { path: this.path });
      return client.post(`${this.path}/sync`, body);
    },
  });
}
