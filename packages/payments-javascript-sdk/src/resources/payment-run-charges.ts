import Client from '../client';

export default function resource(path: string) {
  return (client: Client) => ({
    path,
    client,

    search(params = {}) {
      client.logger.trace('search with params', { params }, this.path);
      return client.get({ params }, this.path);
    },

    findById(id: string) {
      client.logger.trace('findById', { path: this.path, id });
      return client.get({}, `${this.path}/${id}`);
    },

    create(params: object = {}) {
      return client.post(this.path, params);
    },

    charge(id: string, params: object = {}) {
      return client.post(`${this.path}/${id}/process`, params);
    },

    update(id: string, params: object = {}) {
      return client.put(`${this.path}/${id}`, params);
    },
  });
}
