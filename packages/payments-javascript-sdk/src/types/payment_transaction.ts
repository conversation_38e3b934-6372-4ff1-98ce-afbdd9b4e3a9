import { CurrencyCodes } from './currency';

export enum states {
  pending = 'pending',
  payment_pending = 'payment_pending',
  settlement_pending = 'settlement_pending',
  completed = 'completed',
  failed = 'failed',
  settled = 'settled',
  settlement_failed = 'settlement_failed',
  noop = 'noop',
  refunded = 'refunded',
  partial_refund = 'partial_refund',
  refund_flagged = 'refund_flagged',
  payment_deposited = 'payment_deposited',
  batched = 'batched',
}

export enum Backend {
  PROMISEPAY = 'promisepay',
  STRIPE = 'stripe',
  FOODBOMB = 'foodbomb',
  FINSTRO = 'finstro',
}

export enum PaymentMethodType {
  BANK = 'bank',
  CARD = 'card',
  WALLET = 'wallet',
}

export type PaymentMethod = {
  id: string;
  type?: PaymentMethodType;
};

export enum PaymentType {
  TRADE_ACCOUNT = 'trade-account',
  VISA = 'visa',
  MASTERCARD = 'mastercard',
  AMEX = 'amex',
  DIRECT = 'direct',
  UNKNOWN = 'unknown',
  FOODBOMB = 'foodbomb',
  WALLET = 'wallet',
}

export enum TransactionType {
  CAPTURE = 'capture',
  REFUND = 'refund',
  ACCOUNTING = 'accounting',
  ESCROW = 'escrow',
}

export const Workflow = {
  HoldFundsInWallet: 'hold_funds_in_wallet',
} as const;

type ObjectValues<T> = T[keyof T];
export type WorkflowType = ObjectValues<typeof Workflow>;

export interface TransactionContext {
  isZaiAsync?: boolean;
  workflow?: WorkflowType;
  immediate?: boolean;
  backend?: Backend;
  statusLink?: string;
  callbacksLink?: string;
  walletPayment?: boolean;
  [key: string]: any;
}

export type PaymentTransaction = {
  id: string;
  seller_id: string;
  buyer_id: string;
  amount: number;
  currency: CurrencyCodes;
  payment_method: PaymentMethod;
  description: string;
  name: string;
  release_at: string;
  received_at: string;
  state: states;
  declineReason: string;
  declineCode: string | null;
  backend: Backend;
  webhook: string;
  reversal_webhook: string;
  settlementAmount?: string;
  settlement_delay_hours: number;
  settlement_id: string;
  is_reversal: boolean;
  batchId: string;
  userId?: string;
  refundedById?: string;
  relatedTransactions: string[];
  invoiceIds?: string[];
  orderIds?: string[];
  reference: string;
  created_at?: string;
  updated_at?: string;
  bank_payment_method_id?: string;
  card_payment_method_id?: string;
  wallet_payment_method_id?: string;
  discount: string;
  refunded_at?: string | null;
  refund_amount?: number;
  refund_data?: {
    [key: string]: string | number;
  };
  fundedAt?: string;
  transactionType?: TransactionType;
  externalChargeId?: string;
  paymentMethodId?: string;
  type?: PaymentType;
  context?: TransactionContext | null;
};
