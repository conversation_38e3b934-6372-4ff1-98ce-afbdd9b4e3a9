import { z } from 'zod';

/**
 * The request to initiate a promotional payment workflow
 *
 * NOTE: Cut & pasted from the Payments API, this will eventually be published from there
 */
export const PromotionalPaymentRequestSchema = z.object({
  /**
   * This is the promotional amount (in cents) that will be funded by
   * the principle business (us). This is a Zai wallet that is manually
   * funded (at the time of writing).
   */
  promoAmountCents: z
    .number()
    .int()
    .positive()
    .describe('The promo amount in cents'),

  /**
   * This is the total amount (in cents) of the purchase being made
   * by the buyer.
   */
  purchaseAmountCents: z
    .number()
    .int()
    .positive()
    .describe('The purchase amount in cents'),

  /**
   * This is the user id of the buyer.
   */
  senderId: z.string().uuid().describe('The sender/buyer user ID'),

  /**
   * This is the payment method id of the payment method that the buyer
   * will use to pay for the purchase.
   */
  senderPaymentMethodId: z
    .string()
    .uuid()
    .describe('The sender payment method ID'),

  /**
   * This is the user id of the seller.
   */
  recipientId: z.string().uuid().describe('The recipient/seller user ID'),

  /**
   * This is the order ids that are associated with the purchase.
   */
  orderIds: z.array(z.string().uuid()).describe('The order IDs'),

  /**
   * This is the invoice ids that are associated with the purchase.
   */
  invoiceIds: z.array(z.string().uuid()).describe('The invoice IDs'),

  /**
   * This is the description of the payment.
   */
  description: z.string().describe('The description of the payment'),
});

export type PromotionalPaymentRequest = z.infer<
  typeof PromotionalPaymentRequestSchema
>;

/**
 * The response from the promotional payment workflow
 */
export type PromotionalPaymentResponse = {
  paymentTransactionId: string;
  promoTransactionId: string;
};
