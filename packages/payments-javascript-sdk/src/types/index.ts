import * as Logger from 'bunyan';
import axios, { AxiosStatic } from 'axios';

export interface IClient {
  logger: Logger;
  username: string;
  password: string;
  timeout: number;
  callback: () => any;
  apiBase: string;
}

export abstract class Client {
  constructor({
    callback = () => {},
    logger,
    timeout = 5000,
    apiBase,
    username,
    password,
  }: IClient) {
    this.apiBase = apiBase;
    this.logger = logger;
    this.callback = callback;
    this.adaptor = axios;
    this.username = username;
    this.password = password;
    this.timeout = timeout;
  }

  apiBase: string;

  logger: Logger;

  timeout: number;

  callback: () => any;

  adaptor: AxiosStatic;

  username: string;

  password: string;
}

export * from './payment_transaction';
export * from './upfront_payments.types';
export * from './currency';
export * from './promotional_payments.types';
