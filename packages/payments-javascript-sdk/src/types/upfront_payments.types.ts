export type CreateUpfrontPaymentsPayload = {
  amountCents: number;
  description: string;
  sellerUserId: string;
  buyerUserId: string;

  /**
   * The order ID of the upfront payment to be created.
   */
  orderId: string;

  /**
   * The invoice ID associated with the upfront payment.
   */
  invoiceId: string;

  userId: string;
  paymentMethodId: string;
};

export type CreateUpfrontPaymentResponse = {
  /**
   * The transaction id of the initial payment made to hold the upfront amount.
   * This ID is used to reference the holding transaction throughout the upfront payment lifecycle.
   *
   * @required This field is always present in the response
   */
  holdingTransactionId: string;
};

export type FinaliseUpfrontPaymentsPayload = {
  /**
   * The order ID of the upfront payment to be finalized.
   */
  orderId: string;

  /**
   * A list of invoice IDs associated with the upfront payment.
   * These IDs are used to identify the invoices being finalized in the request.
   */
  invoiceIds: string[];

  /**
   * This is the final amount the upfront order was closed at.
   * It will determine if a refund or top-up charge is needed, or no action if the amount is the same.
   */
  finalAmountCents: number;

  /**
   * The description of the upfront payment transaction
   */
  description: string;
};

export type CancelUpfrontPaymentPayload = {
  orderId: string;
  reason: string;
};
