import NULL_LOGGER from 'null-logger';
import Client from './client';
import resources from './resources';

function createClient({
  logger = NULL_LOGGER,
  username,
  password,
  timeout = 3000,
  callback,
  apiBase = 'https://payments-testing.ordermentum.com',
}) {
  const client = new Client({
    username,
    password,
    apiBase,
    timeout,
    callback,
    logger,
  });

  return {
    client,
    users: resources.users(client),
    transactions: resources.transactions(client),
    paymentMethods: resources.paymentMethods(client),
    paymentRuns: resources.paymentRuns(client),
    paymentRunCharges: resources.paymentRunCharges(client),
    cards: resources.cards(client),
    bankAccounts: resources.bankAccounts(client),
    batch: resources.batch(client),
    subscriptions: resources.subscriptions(client),
    asyncTransactions: resources.asyncTransactions(client),
    asyncUsers: resources.asyncUsers(client),
    upfrontPayments: resources.upfrontPayments(client),
    promotionalPayments: resources.promotionalPayments(client),
  };
}

export * from './types';
export default createClient;
