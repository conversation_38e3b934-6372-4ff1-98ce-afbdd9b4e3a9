import axios, { AxiosRequestConfig } from 'axios';
import Qs from 'qs';
import errorHandler from './interceptors/error_handler';
import { Client } from './types';

export class PaymentsClient extends Client {
  super({
    callback = () => {},
    logger,
    timeout = 5000,
    apiBase,
    username,
    password,
  }) {
    this.apiBase = apiBase;
    this.logger = logger;
    this.callback = callback;
    this.adaptor = axios;
    this.username = username;
    this.password = password;
    this.timeout = timeout;
  }

  get instance() {
    const headers = this.getHeaders();
    this.logger.trace('Request Headers', this.getHeaders());
    const instance = this.adaptor.create({
      baseURL: this.apiBase,
      timeout: this.timeout,
      responseType: 'json',
      headers,
      auth: {
        username: this.username,
        password: this.password,
      },
      paramsSerializer: params =>
        Qs.stringify(params, { arrayFormat: 'brackets' }),
    });

    errorHandler(instance, this);
    return instance;
  }

  getHeaders() {
    const headers = {
      'Content-Type': 'application/json',
    };
    return headers;
  }

  authentication() {
    return Buffer.from(`${this.username}:${this.password}`).toString('base64');
  }

  async get(params: AxiosRequestConfig, url: string) {
    this.logger.trace('Get request', this.apiBase, url, 'Params', params);
    return this.instance.get(url, params).then(r => r.data);
  }

  async post(url: string, body: object) {
    return this.instance.post(url, body).then(r => r.data);
  }

  async patch(url: string, body: object) {
    return this.instance.patch(url, body).then(r => r.data);
  }

  async put(url: string, body: object) {
    return this.instance.put(url, body).then(r => r.data);
  }

  async delete(url: string) {
    this.logger.trace('Delete request', this.apiBase, url);
    return this.instance.delete(url).then(r => r.data);
  }
}

export default PaymentsClient;
