import { AxiosInstance, AxiosResponse } from 'axios';
import { Client } from '../types';

export default function errorHandler(instance: AxiosInstance, client: Client) {
  return instance.interceptors.response.use(
    (_: AxiosResponse) => {
      return _;
    },
    (error) => {
      if (error) {
        client.logger.error(error);

        return Promise.reject({
          status: error?.response?.status ?? '',
          error: error?.response?.statusText ?? '',
          message: error?.response?.statusText ?? '',
          data: error?.response?.data?.data ?? null,
        });
      }
      return null;
    }
  );
}
