import { responseBuilder } from './builders/response_builder';
import { PaginationParams } from './types/pagination_type';
import { ChartData, ChartMeta, ChartParams } from './types/chart_type';

/**
 * Builds a paginated response type typically used for lists and search etc
 */
export function build<Item>(params: PaginationParams<Item>) {
  return responseBuilder(params);
}

/**
 * Builds a paged response type used for charts and the like.
 */
export function buildChart(
  pagination: PaginationParams<ChartData>,
  chartable: ChartParams
) {
  return responseBuilder<ChartData, ChartParams, ChartMeta>(
    pagination,
    chartable
  );
}
