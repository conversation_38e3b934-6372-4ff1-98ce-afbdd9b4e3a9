import Qs from 'qs';
import { Links } from '../types/links_type';
import { ListResponse } from '../types/list_response_type';
import { PaginationMeta, PaginationParams } from '../types/pagination_type';

export function totalPages(totalResults: number, pageSize: number): number {
  return Math.max(1, Math.ceil(totalResults / pageSize));
}

/**
 * Utility class to build the resultant response instance
 */
export function responseBuilder<
  Item,
  AdditionalParams,
  Metadata extends PaginationMeta
>(params: PaginationParams<Item>, additional?: AdditionalParams) {
  return {
    get pageSize(): number {
      return parseInt(String(params.pageSize ?? '25'), 10);
    },

    get pageNo(): number {
      return parseInt(String(params.pageNo ?? '1'), 10);
    },

    get totalResults(): number {
      return parseInt(String(params.totalResults), 10);
    },

    link(page: number): string {
      return `${params.baseUrl}?${this.buildQuery(page)}`;
    },

    buildQuery(page: number): string {
      return Qs.stringify(
        { ...params.query, pageNo: page },
        { skipNulls: true }
      );
    },

    get links(): Links {
      return {
        self: this.current,
        first: this.first,
        prev: this.previous,
        next: this.next,
        last: this.last,
      };
    },

    get previous(): string | null {
      if (this.pageNo === 1) {
        return null;
      }

      return this.link(Math.max(1, this.pageNo - 1));
    },

    get totalPages(): number {
      return totalPages(this.totalResults, this.pageSize);
    },

    get next(): string | null {
      if (!this.totalPages || params.pageNo === this.totalPages) {
        return null;
      }

      return this.link(Math.min(this.totalPages, this.pageNo + 1));
    },

    get first(): string | null {
      if (this.totalPages === 0) {
        return null;
      }

      return this.link(1);
    },

    get last(): string | null {
      if (this.totalPages > 0) {
        return this.link(this.totalPages);
      }

      return null;
    },

    get current(): string {
      return this.link(this.pageNo);
    },

    get meta(): Metadata {
      return {
        ...additional,
        totalResults: this.totalResults,
        totalPages: this.totalPages,
        pageSize: this.pageSize,
        pageNo: this.pageNo,
      } as Metadata;
    },

    paginate() {
      const result: ListResponse<Item, Metadata> = {
        meta: this.meta,
        links: this.links,
        data: params.data,
      };

      if (params.aggregations) {
        result.aggregations = params.aggregations;
      }

      return result;
    },
  };
}
