import { PaginationMeta } from './pagination_type';

export type ChartGroupId = string;

export type ChartGroup = {
  id: ChartGroupId;
  name: string;
};

export interface ChartParams {
  title: string | null;
  subtitle: string | null;
  max?: number;
  min?: number;
  average?: number;
  total?: number;
  groups: ChartGroup[];
  // The idea with this is that it will contain what range the chart covers
  // e.g for a date range, it will contain iso8601 formatted dates of the start and end date
  range?: {
    start: string;
    end: string;
  };
}

export type ChartMeta = ChartParams & PaginationMeta;

export type ChartData = {
  label: string;
  total: number;
  values: Record<ChartGroupId, { value: number; formatted: string }>;
};
