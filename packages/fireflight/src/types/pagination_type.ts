import { Aggregation } from './aggregation_type';

/**
 * These pagination types mirror a result from ElasticSearch
 * for compatibility.
 */
export type PaginationParams<Item> = {
  baseUrl: string;
  pageNo: string | number | undefined;
  pageSize: string | number | undefined;
  query: object;
  totalResults: string | number;
  aggregations?: Aggregation[];
  data: Item[];
};

export interface PaginationMeta {
  totalResults: number;
  totalPages: number;
  pageSize: number;
  pageNo: number;
}
