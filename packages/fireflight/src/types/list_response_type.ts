import { Aggregation } from './aggregation_type';
import { Links } from './links_type';
import { PaginationMeta } from './pagination_type';

/**
 * An instance of this type contains all the information to be passed
 * back from the endpoint to the client. Each client will parse and
 * act on the data which represents paged items of data with configurable
 * metadata.
 */
export type ListResponse<Data, Metadata extends PaginationMeta> = {
  data: Data[];
  meta: Metadata;
  links: Links;
  aggregations?: Aggregation[];
};
