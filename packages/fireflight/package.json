{"name": "@ordermentum/fireflight", "version": "1.3.4", "description": "Pagination Helpers for ES6", "main": "build/index.js", "scripts": {"lint": "eslint src/ test/", "test": "npm run lint && BABEL_ENV=TEST istanbul cover ./node_modules/.bin/mocha -- --require ts-node/register 'test/**/*.ts'", "build": "tsc", "spec": "npm run lint && ./node_modules/.bin/mocha --require ts-node/register 'test/**/*.ts'", "distribute": "npm run prepublish", "prepublish": "npm run build"}, "repository": {"type": "git", "url": "git+https://github.com/ordermentum/fireflight.git"}, "keywords": ["pagination", "decorators"], "files": ["build/*"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/ordermentum/fireflight/issues"}, "homepage": "https://github.com/ordermentum/fireflight#readme", "dependencies": {"qs": "^6.2.1"}, "devDependencies": {"@types/chai": "^4.3.3", "@types/mocha": "^9.1.1", "@types/node": "^18.7.15", "@types/sinon": "^10.0.13", "chai": "^3.5.0", "eslint": "^8.57.0", "istanbul": "^0.4.4", "mocha": "^2.5.3", "ts-node": "^10.9.1", "typescript": "5.1.6"}}