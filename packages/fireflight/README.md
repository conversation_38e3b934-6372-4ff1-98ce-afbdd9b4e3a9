# Fireflight

Type declarations and builders in Typescript that resemble the [JSON API](https://jsonapi.org) standard.

## Supported types

- `build`: A set of paged items of paged navigation
- `buildSeries`: A set of paged items with meta information for charting

## Example search results/list

```typescript
import pagination from '@ordermentum/fireflight';

const data = [
  { name: 'Sourdough', subtotal: 1500, tax: 150 },
  { name: 'Soy and Linseed', subtotal: 1500, tax: 150 },
];

const paginator = build({ 
  baseUrl: 'https://api.ordermentum.com/v1/products',
  pageNo: 1,
  pageSize: 2,
  query: { pageNo: 1 },
  totalPages: 5,
  data
});
const page = paginator.paginate();

console.log(page.links);
console.log(page.meta);
console.log(page.data);
```
