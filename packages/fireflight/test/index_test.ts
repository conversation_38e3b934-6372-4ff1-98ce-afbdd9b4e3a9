import { expect } from 'chai';
import { build, buildChart } from '../src';
import { AggregationType } from '../src/types/aggregation_type';
import { ChartData, ChartGroup, ChartParams } from '../src/types/chart_type';
import { PaginationParams } from '../src/types/pagination_type';

describe('build', () => {
  it('works', () => {
    const baseUrl = 'https://api.ordermentum.com/v1';
    const pageNo = 1;
    const pageSize = 2;
    const totalResults = 5;
    const query = { pageNo: 1 };

    const data = [
      { name: 'Sourdough', subtotal: 1500, tax: 150 },
      { name: 'Soy and Linseed', subtotal: 1500, tax: 150 },
    ];

    const paginator = build({
      baseUrl,
      pageNo,
      pageSize,
      query,
      totalResults,
      data,
    });

    const page = paginator.paginate();
    expect(page.data).to.be.eql(data);
    expect(page).to.have.property('meta');
    expect(page).to.have.property('links');
    expect(page).to.have.property('data');
  });

  it('types', () => {
    const baseUrl = 'https://api.ordermentum.com/v1';
    const pageNo = 1;
    const pageSize = '2';
    const totalResults = 5;
    const query = { pageNo: 1 };

    const data = [
      { name: 'Sourdough', subtotal: 1500, tax: 150 },
      { name: 'Soy and Linseed', subtotal: 1500, tax: 150 },
    ];

    const paginator = build({
      baseUrl,
      pageNo,
      pageSize,
      query,
      totalResults,
      data,
    });

    expect(paginator.pageSize).to.equal(2);
    expect(paginator.pageNo).to.equal(1);

    const page = paginator.paginate();
    expect(page.data).to.be.eql(data);
    expect(page).to.have.property('meta');
    expect(page).to.have.property('links');
    expect(page.meta.pageSize).to.equal(2);
    expect(page).to.have.property('data');
  });

  it('next', () => {
    const baseUrl = 'https://api.ordermentum.com/v1';
    const pageNo = 1;
    const pageSize = '2';
    const totalResults = 1;
    const query = { pageNo: 1 };

    const data = [
      { name: 'Sourdough', subtotal: 1500, tax: 150 },
      { name: 'Soy and Linseed', subtotal: 1500, tax: 150 },
    ];

    const paginator = build({
      baseUrl,
      pageNo,
      pageSize,
      query,
      totalResults,
      data,
    });

    expect(paginator.pageSize).to.equal(2);
    expect(paginator.pageNo).to.equal(1);

    const page = paginator.paginate();
    expect(page.links.next).to.equal(null);
  });

  it('last', () => {
    const baseUrl = 'https://api.ordermentum.com/v1';
    const pageNo = 1;
    const pageSize = 0;
    const totalResults = 0;
    const query = { pageNo: 1 };

    const data = [
      { name: 'Sourdough', subtotal: 1500, tax: 150 },
      { name: 'Soy and Linseed', subtotal: 1500, tax: 150 },
    ];

    const paginator = build({
      baseUrl,
      pageNo,
      pageSize,
      query,
      totalResults,
      data,
    });

    const page = paginator.paginate();
    expect(page.links.last).to.equal(null);
  });

  it('creates appropriate links when total results is 0', () => {
    const baseUrl = 'https://api.ordermentum.com/v1';
    const pageNo = 1;
    const pageSize = 2;
    const totalResults = 0;
    const query = { pageNo: 1 };
    const data = [];

    const paginator = build({
      baseUrl,
      pageNo,
      pageSize,
      query,
      totalResults,
      data,
    });

    const page = paginator.paginate();
    expect(page.data).to.be.eql(data);
    expect(page).to.have.property('meta');
    expect(page).to.have.property('links');
    expect(page).to.have.property('data');
    expect(page.links.next).to.eqls(null);
    expect(page.links.last).to.not.eqls(null);
    expect(page.links.prev).to.eqls(null);
    expect(page.meta.totalPages).to.eql(1);
  });

  describe('aggregations', () => {
    it('should create aggregations when included', () => {
      const baseUrl = 'https://api.ordermentum.com/v1';
      const pageNo = 1;
      const pageSize = 2;
      const totalResults = 5;
      const query = { pageNo: 1 };

      const data = [
        { name: 'Sourdough', subtotal: 1500, tax: 150 },
        { name: 'Soy and Linseed', subtotal: 1500, tax: 150 },
      ];

      const aggregations = [
        {
          id: 'availability',
          label: 'Availability',
          type: AggregationType.MULTI_SELECT,
          filters: [
            {
              id: 'sydney',
              label: 'Availability',
              count: 2,
            },
          ],
        },
      ];

      const paginator = build({
        baseUrl,
        pageNo,
        pageSize,
        query,
        totalResults,
        data,
        aggregations,
      });

      const page = paginator.paginate();
      expect(page).to.have.property('aggregations');
      expect(page.aggregations).to.be.eql(aggregations);
    });

    it('should not create aggregations when not included', () => {
      const baseUrl = 'https://api.ordermentum.com/v1';
      const pageNo = 1;
      const pageSize = 2;
      const totalResults = 5;
      const query = { pageNo: 1 };

      const data = [
        { name: 'Sourdough', subtotal: 1500, tax: 150 },
        { name: 'Soy and Linseed', subtotal: 1500, tax: 150 },
      ];

      const paginator = build({
        baseUrl,
        pageNo,
        pageSize,
        query,
        totalResults,
        data,
      });

      const page = paginator.paginate();
      expect(page).to.not.have.property('aggregations');
      expect(page.aggregations).to.be.undefined;
    });
  });

  describe('series metadata', () => {
    it('should create series metadata when included', () => {
      const baseUrl = 'https://api.ordermentum.com/v1';
      const pageNo = 1;
      const pageSize = 2;
      const totalResults = 5;
      const query = { pageNo: 1 };

      const groups: ChartGroup[] = [
        { id: '1', name: 'Sourdough' },
        { id: '2', name: 'Soy and Linseed' },
      ];

      const data: ChartData[] = [
        {
          label: 'Sydney',
          total: 0,
          values: {
            '1': { formatted: '$15', value: 1500 },
            '2': { formatted: '$7', value: 700 },
          },
        },
        {
          label: 'Oberon',
          total: 0,
          values: {
            '1': { formatted: '$15', value: 1500 },
            '2': { formatted: '$7', value: 700 },
          },
        },
      ];

      const pagination: PaginationParams<ChartData> = {
        baseUrl,
        pageNo,
        pageSize,
        query,
        totalResults,
        data,
      };

      const params: ChartParams = {
        title: 'Sales',
        subtitle: 'Sales by product',
        max: 1500,
        min: 1500,
        average: 1500,
        total: 1500,
        groups,
        range: { start: '2021-01-01', end: '2021-01-31' },
      };
      const paginator = buildChart(pagination, params);

      const page = paginator.paginate();
      expect(page).to.have.property('meta');
      expect(page.meta.average).to.be.eql(params.average);
      expect(page.meta.total).to.be.eql(params.total);
    });
  });
});
