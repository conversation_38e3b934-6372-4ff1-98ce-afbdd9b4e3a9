{"name": "@ordermentum/cart-javascript-sdk", "version": "0.2.0", "main": "lib/src/index.js", "types": "lib/src/index.d.ts", "repository": "**************:ordermentum/cart-javascript-sdk.git", "author": "<PERSON> <john.dagos<PERSON>@gmail.com>", "license": "MIT", "dependencies": {"axios": "1.11.0", "null-logger": "^1.0.0", "qs": "^6.9.0"}, "files": ["lib/*"], "scripts": {"test": "cross-env NODE_ENV=test nyc npm run spec", "spec": "mocha -R spec ./test/test_helper.ts test/*.*", "lint": "eslint 'src/**/*.ts'", "typecheck": "tsc --noEmit", "autotest": "./node_modules/.bin/_mocha --watch", "clean": "rm -rf lib", "build": "yarn clean && tsc", "prepublish": "yarn run build", "reporter": "nyc --reporter=html yarn run test"}, "devDependencies": {"@types/axios": "0.14.4", "@types/chai": "4.3.16", "@types/mocha": "8.2.3", "@types/node": "15.14.9", "@types/qs": "6.14.0", "@types/sinon": "10.0.20", "chai": "4.4.1", "cross-env": "6.0.3", "eslint": "8.57.1", "mocha": "7.2.0", "nyc": "14.1.1", "sinon": "8.1.1", "ts-node": "10.9.2", "typescript": "5.4.5"}, "nyc": {"require": ["ts-node/register"], "reporter": ["lcov", "text"], "sourceMap": false, "instrument": false}}