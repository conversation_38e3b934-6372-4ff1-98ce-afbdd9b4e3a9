{"compilerOptions": {"lib": ["es2019"], "target": "es2017", "module": "commonjs", "allowJs": false, "declaration": true, "strict": true, "alwaysStrict": true, "noUnusedLocals": true, "noImplicitAny": true, "noImplicitThis": true, "resolveJsonModule": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "allowSyntheticDefaultImports": true, "moduleResolution": "node", "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "strictPropertyInitialization": false, "outDir": "lib", "skipLibCheck": true}, "include": ["src"], "exclude": ["node_modules"]}