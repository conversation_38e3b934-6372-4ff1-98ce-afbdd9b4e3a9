import { expect } from 'chai';
import { describe, it } from 'mocha';
import cartClient from '../src';

describe('Client', () => {
  it('return an instance', async () => {
    const apiBase = 'http://cart.ordermentum-sandbox.com';
    const token = 'test';
    const client = cartClient({ apiBase, token });
    expect(client.cart).to.not.equal(null);
    expect(client.cart.client).to.deep.equal(client.client);
  });
});
