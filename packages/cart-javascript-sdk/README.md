# Cart Browser/Node SDK

### Getting started

- Supports two authentications, 
 - *Basic* with a string as basic token
 - *Bearer token* with a string as bearer token

 
 (Only one of them is required, basic takes precedence over bearer)

```javascript
import cartSdk, { CartClient } from '@ordermentum/cart-javascript-sdk';

const cartClient: CartClient = cartSdk({
  apiBase: {CART_URL}, // Default https://cart.ordermentum.com
  basicAuthToken: {CART_SECRET}, // sets Authorization: "Basic {CART_SECRET}"
});
```

```javascript
import cartSdk, { CartClient } from '@ordermentum/cart-javascript-sdk';

const cartClient: CartClient = cartSdk({
  apiBase: {CART_URL}, // Default https://cart.ordermentum.com
  token: {TOKEN}, // sets Authorization: "Bearer {TOKEN}"
});
```
