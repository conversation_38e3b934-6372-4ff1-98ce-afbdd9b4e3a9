import { AxiosStatic } from 'axios';
import resources, { Resources } from './resources/index';

import Client from './client';

import Logger from './definitions/logger';

// https://stackoverflow.com/questions/40510611/typescript-interface-require-one-of-two-properties-to-exist/49725198#49725198
type RequireOnlyOne<T, Keys extends keyof T = keyof T> = Pick<
  T,
  Exclude<keyof T, Keys>
> &
  {
    [K in Keys]-?: Required<Pick<T, K>> &
      Partial<Record<Exclude<Keys, K>, undefined>>;
  }[Keys];

type Configuration = {
  apiBase?: string;
  timeout?: number;
  logger?: Logger;
  token?: string;
  basicAuthToken?: string;
  adaptor?: AxiosStatic;
};

function createClient({
  apiBase = 'https://cart.ordermentum.com',
  timeout = 3000,
  token,
  basicAuthToken,
  logger,
  adaptor,
}: RequireOnlyOne<Configuration, 'token' | 'basicAuthToken'>) {
  const client = new Client({
    token,
    basicAuthToken,
    apiBase,
    timeout,
    logger,
    adaptor,
  });

  logger?.info({ token, apiBase, timeout });

  const { cart }: Resources = resources(client);

  return {
    client,
    cart,
  };
}

export type CartClient = Resources & { client: Client };
export default createClient;
