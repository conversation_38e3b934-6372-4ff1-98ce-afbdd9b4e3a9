import axios, { AxiosStatic, AxiosRequestConfig } from 'axios';
import qs from 'qs';
import Logger from './definitions/logger';
import { version } from '../package.json';

export interface IClient {
  logger?: Logger;
  username?: string;
  token?: string;
  basicAuthToken?: string;
  timeout?: number;
  callback?: () => void;
  apiBase: string;
  adaptor?: AxiosStatic;
}

abstract class Client {
  constructor({
    callback = () => {},
    logger,
    timeout = 5000,
    apiBase,
    basicAuthToken,
    token,
    adaptor,
  }: IClient) {
    this.apiBase = apiBase;
    this.logger = logger;
    this.callback = callback;
    this.adaptor = adaptor || axios;
    this.token = token;
    this.timeout = timeout;
    this.basicAuthToken = basicAuthToken;
  }

  apiBase: string;

  logger: Logger | undefined;

  timeout: number;

  callback: () => void;

  adaptor: AxiosStatic;

  token?: string;

  basicAuthToken?: string;
}

export default class CartClient extends Client {
  super({
    apiBase = 'https://cart.ordermentum.com',
    timeout = 3000,
    token,
    basicAuthToken,
    logger,
    adaptor,
  }: IClient) {
    this.apiBase = apiBase;
    this.token = token;
    this.logger = logger;
    this.adaptor = adaptor || axios;
    this.timeout = timeout;
    this.basicAuthToken = basicAuthToken;
  }

  get instance() {
    return this.adaptor.create({
      baseURL: this.apiBase,
      timeout: this.timeout,
      paramsSerializer: params => qs.stringify(params),
      responseType: 'json',
      headers: {
        'User-Agent': `Cart Client ${version}`,
        Authorization: this.basicAuthToken
          ? `Basic ${this.basicAuthToken}`
          : `Bearer ${this.token}`,
      },
    });
  }

  async get(url: string, params: AxiosRequestConfig) {
    return this.instance.get(url, params).then(r => r.data);
  }

  async post(url: string, body: any, params: AxiosRequestConfig) {
    return this.instance.post(url, body, params).then(r => r.data);
  }

  async patch(url: string, body: any, params: AxiosRequestConfig) {
    return this.instance.patch(url, body, params).then(r => r.data);
  }

  async put(url: string, body: any, params: AxiosRequestConfig) {
    return this.instance.put(url, body, params).then(r => r.data);
  }

  async delete(url: string, params: AxiosRequestConfig = {}) {
    return this.instance.delete(url, params).then(r => r.data);
  }
}
