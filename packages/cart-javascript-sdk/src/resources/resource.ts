import { AxiosResponse, AxiosRequestConfig } from 'axios';
import Client from '../client';

function singleResource(response: AxiosResponse) {
  if (Array.isArray(response.data) && response.data.length > 0) {
    return response.data[0];
  }
  return null;
}

const findAll =
  ({
    defaultFilter,
    path,
    client,
  }: {
    defaultFilter: any;
    path: string;
    client: Client;
  }) =>
  (
    query: {
      [key: string]: any;
    } = {},
    requestConfig: AxiosRequestConfig = {}
  ) => {
    const params = { ...defaultFilter, ...query };
    client.logger?.trace('findAll', { path, params });
    return client.get(path, { ...requestConfig, params });
  };

const findOne =
  ({ path, client }: { path: string; client: Client }) =>
  (
    query: {
      [key: string]: any;
    } = {},
    requestConfig: AxiosRequestConfig = {}
  ) => {
    const params = { pageSize: 1, pageNo: 1, ...query };

    client.logger?.trace('findOne', { path, params });
    return client.get(path, { ...requestConfig, params }).then(singleResource);
  };

const findById =
  ({ path, client }: { path: string; client: Client }) =>
  (id: string, requestConfig: AxiosRequestConfig = {}) => {
    client.logger?.trace('findById', { path, id });
    return client.get(`${path}/${id}`, requestConfig);
  };

const create =
  ({ client, path }: { path: string; client: Client }) =>
  (params = {}, requestConfig: AxiosRequestConfig = {}) => {
    client.logger?.trace('create', { path, params });
    return client.post(path, params, requestConfig);
  };

const destroyAll =
  ({ client, path }: { path: string; client: Client }) =>
  (requestConfig: AxiosRequestConfig = {}) => {
    client.logger?.trace('create', { path, requestConfig });
    return client.delete(path, requestConfig);
  };

const destroy =
  ({ client, path }: { path: string; client: Client }) =>
  (id: string, requestConfig: AxiosRequestConfig = {}) => {
    client.logger?.trace('destroy', { path, id });
    return client.delete(`${path}/${id}`, requestConfig);
  };

const update =
  ({ client, path }: { path: string; client: Client }) =>
  (
    id: string | null = null,
    params = {},
    url = '',
    requestConfig: AxiosRequestConfig = {}
  ) => {
    client.logger?.trace('update', { path, id, params });
    if (url) {
      return id
        ? client.put(`${path}/${id}/${url}`, params, requestConfig)
        : client.put(`${path}/${url}`, params, requestConfig);
    }
    return id
      ? client.put(`${path}/${id}`, params, requestConfig)
      : client.put(`${path}`, params, requestConfig);
  };

const patch =
  ({ client, path }: { path: string; client: Client }) =>
  (
    id: string | null = null,
    params = {},
    url = '',
    requestConfig: AxiosRequestConfig = {}
  ) => {
    client.logger?.trace('patch', { path, id, params });
    if (url) {
      return id
        ? client.patch(`${path}/${id}/${url}`, params, requestConfig)
        : client.patch(`${path}/${url}`, params, requestConfig);
    }
    return id
      ? client.patch(`${path}/${id}`, params, requestConfig)
      : client.patch(`${path}`, params, requestConfig);
  };

export type Resource = {
  path: string;
  client: Client;
  defaultFilter: { pageSize: number; pageNo: number };
  findAll: (
    params?: { [key: string]: any },
    requestConfig?: AxiosRequestConfig
  ) => any;
  findOne: (
    params?: { [key: string]: any },
    requestConfig?: AxiosRequestConfig
  ) => any;
  findById: (id: string, requestConfig?: AxiosRequestConfig) => any;
  get: (id: string, requestConfig?: AxiosRequestConfig) => any;
  create: (body?: any, requestConfig?: AxiosRequestConfig) => any;
  destroy: (id: string, requestConfig?: AxiosRequestConfig) => any;
  destroyAll: (requestConfig?: AxiosRequestConfig) => any;
  update: (
    id?: string | null,
    body?: {},
    url?: string,
    requestConfig?: AxiosRequestConfig
  ) => any;
  patch: (
    id?: string | null,
    body?: {},
    url?: string,
    requestConfig?: AxiosRequestConfig
  ) => any;
};

export default function resource(path: string) {
  const defaultFilter = {
    pageSize: 25,
    pageNo: 1,
  };

  return (client: Client): Resource => ({
    path,
    client,
    defaultFilter,
    findAll: findAll({ client, defaultFilter, path }),
    findOne: findOne({ client, path }),
    findById: findById({ client, path }),
    get: findById({ client, path }),
    create: create({ client, path }),
    destroy: destroy({ client, path }),
    update: update({ client, path }),
    patch: patch({ client, path }),
    destroyAll: destroyAll({ client, path }),
  });
}
