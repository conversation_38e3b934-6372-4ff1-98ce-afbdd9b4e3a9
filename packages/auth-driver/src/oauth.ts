/* eslint-disable camelcase */
import axios, { AxiosHeaders, AxiosInstance } from 'axios';
import axiosRetry from '@ordermentum/axios-retry';
import { User, AuthToken, MFAGrant } from './types';
import { errorify } from './helpers';

export type OAuthOptions = {
  retries?: number;
  headers?: AxiosHeaders;
};

export class OAuth {
  #clientId: string;

  #clientSecret: string;

  #host: string;

  client: AxiosInstance;

  constructor({
    clientId,
    clientSecret,
    host,
    client,
    opts = {},
  }: {
    clientId: string;
    clientSecret: string;
    host: string;
    client?: AxiosInstance;
    opts?: OAuthOptions;
  }) {
    this.#clientId = clientId;
    this.#clientSecret = clientSecret;
    this.#host = host;
    this.client = this.configureClient(opts, client);
  }

  configureClient(opts: OAuthOptions, instance?: AxiosInstance): AxiosInstance {
    let client;

    if (instance) {
      client = instance;
    } else {
      client = axios.create({
        baseURL: this.#host,
        headers: opts.headers,
      });
    }

    if (opts.retries && opts.retries > 0) {
      axiosRetry(client, { retries: opts.retries });
    }

    client.interceptors.response.use(
      res => res,
      err => Promise.reject(errorify(err))
    );

    return client;
  }

  static build(
    client: AxiosInstance,
    {
      clientId,
      host,
      clientSecret,
    }: { clientId: string; clientSecret: string; host: string }
  ) {
    return new OAuth({
      clientId,
      clientSecret,
      host,
      client,
    });
  }

  async token({
    username,
    password,
    scope,
  }: {
    username: string;
    password: string;
    scope?: string;
  }): Promise<AuthToken> {
    return this.client
      .post('/oauth/token', {
        username,
        password,
        scope,
        grant_type: 'password',
        client_secret: this.#clientSecret,
        client_id: this.#clientId,
      })
      .then(res => res.data);
  }

  async revoke(
    token: string,
    type: 'access_token' | 'refresh_token' = 'access_token'
  ): Promise<null> {
    return this.client.post('/oauth/revoke', {
      client_id: this.#clientId,
      client_secret: this.#clientSecret,
      token,
      token_type_hint: type,
    });
  }

  async refresh(refreshToken: string): Promise<AuthToken> {
    return this.client
      .post('/oauth/token', {
        refresh_token: refreshToken,
        client_id: this.#clientId,
        client_secret: this.#clientSecret,
        grant_type: 'refresh_token',
      })
      .then(res => res.data);
  }

  async userInfo(token: string): Promise<User> {
    return this.client
      .post('/oauth/userinfo', {
        access_token: token,
        client_id: this.#clientId,
        client_secret: this.#clientSecret,
      })
      .then(res => res.data);
  }

  async mfaToken(mfa: MFAGrant): Promise<AuthToken> {
    const { code, userId, state } = mfa;
    return this.client
      .post('/oauth/token', {
        code,
        userId,
        state,
        client_id: this.#clientId,
        grant_type: 'mfa_token',
      })
      .then(res => res.data);
  }

  async generateMFACode(mfa: Omit<MFAGrant, 'code'>): Promise<AuthToken> {
    const { userId, state } = mfa;
    return this.client
      .post('/v1/mfa/generate', {
        userId,
        state,
      })
      .then(res => res.data);
  }
}

export default OAuth;
