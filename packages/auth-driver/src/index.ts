import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import qs from 'qs';
import {
  EntityResponse,
  User,
  AuthToken,
  AuthSearch,
  Entity,
  EntityUser,
  Role,
  CreateMfa,
  CreateMfaResponse,
  VerifyMfa,
  GetMfaResponse,
  GenerateMfaResponse,
  GenerateMfa,
  MFAValidateTokenRequest,
  ValidateSessionRequest,
  ValidateChallengeSession,
  TokenResponse,
} from './types';

import { Options, configureInterceptors } from './helpers';

export { OAuth, OAuthOptions } from './oauth';
export { AxiosHeaders } from 'axios';

export {
  AuthSearch,
  Entity,
  AuthToken,
  EntityResponse,
  Permission,
  User,
  EntityUser,
  Role,
  UserRole,
  Context,
  CreateMfa,
  CreateMfaResponse,
  GetMfaResponse,
  GenerateMfa,
  GenerateMfaResponse,
  VerifyMfa,
  MFAGrant,
  MFAMethod,
} from './types';

export {
  getEntities,
  isSuperAdmin,
  isAdmin,
  getAdminAuth,
  getRetailerAuth,
  getSupplierAuth,
  extractClaims,
  Authorization,
} from './helpers';

/*
 * Usage:
 * import Driver from 'auth-driver';
 * const driver = new Driver('https://localhost:3000', {
 *   username: 'username',
 *   password: 'key'
 * });
 */
export default class Driver {
  axios: AxiosInstance;

  // Token needs to be `Bearer {token}`
  constructor(
    url: string,
    opts?: Options | null,
    token?: string,
    client?: AxiosInstance
  ) {
    if (!opts && !token) throw new Error('No credentials found');
    if (!url) throw new Error('Driver must be instantiated with a url');
    this.axios = this.configureClient(url, opts, token, client);
  }

  static build(client: AxiosInstance, opts?: Options | null, token?: string) {
    const url = client.defaults.baseURL ?? '';
    return new Driver(url, opts, token, client);
  }

  configureClient(
    url: string,
    opts?: Options | null,
    token?: string,
    client?: AxiosInstance
  ): AxiosInstance {
    let instance;
    if (client) {
      instance = client;
    } else {
      instance = axios.create({
        baseURL: url,
        timeout: opts?.timeout ?? 3000,
      });
    }
    configureInterceptors(instance, opts, token);
    return instance;
  }

  validate(token: string) {
    return this.axios.get(`/entities?token=${token}`).then(res => res.data);
  }

  signIn(email: string, password: string): Promise<AuthToken> {
    return this.axios.post('/login', { email, password }).then(res => res.data);
  }

  /**
   * The funky type is necessary because the
   * entities creation api expects email to be in an array. Ideally it should expect just an email string
   * TODO: change the api in auth to accept just email
   */
  create(
    data: Omit<Entity, 'email'> & {
      email?: string;
    }
  ): Promise<EntityResponse> {
    return this.axios.post('/entities', data).then(res => res.data);
  }

  findByPerm(type, uuid) {
    return this.axios
      .get(`/entities?perm.type=${type}&perm.entity=${uuid}`)
      .then(res => res.data);
  }

  deleteUser(id: string): Promise<void> {
    return this.axios.delete(`/v1/users/${id}`);
  }

  resetUserMfa(userId: string): Promise<void> {
    return this.axios.delete(`/v1/users/${userId}/reset-mfa`);
  }

  search(params: AuthSearch[]): Promise<User[]> {
    /*
     * params should be an array of objects. eg, "I want to know whether Alice has any kind of permission over c47e":
     * [{
     *   key: 'email',
     *   value: '<EMAIL>'
     * },
     * {
     *   key: 'perm.entity',
     *   value: 'c47eb6a4-b7bd-4aa2-9107-bc907e159ec5'
     * }]
     */
    const q = Object.assign({}, ...params.map(o => ({ [o.key]: o.value })));
    return this.axios
      .get(`/entities?${qs.stringify(q)}`)
      .then(res => res.data.map(u => u));
  }

  generateToken(email: string): Promise<AuthToken> {
    return this.axios.post('/login/token', { email }).then(res => res.data);
  }

  getAll(): Promise<User[]> {
    return this.axios.get('/entities').then(res => res.data.map(u => u));
  }

  verifyChallenge(id: string, token: string): Promise<boolean> {
    return this.axios
      .post<{ valid: boolean }>(`/v1/challenge/${id}`, { token })
      .then(res => res.data.valid);
  }

  generateChallenge(id: string): Promise<{ token: string }> {
    return this.axios
      .get<{ token: string }>(`/v1/challenge/${id}`)
      .then(res => res.data);
  }

  emailChallenge(id: string, callback: string): Promise<boolean> {
    return this.axios
      .post<{ sent: boolean }>(`/v1/challenge/${id}/email`, {
        id,
        callback,
      })
      .then(res => res.data.sent);
  }

  validateChallengeToken(
    { id, mfaToken }: MFAValidateTokenRequest,
    opts?: AxiosRequestConfig
  ): Promise<boolean> {
    return this.axios
      .post<{ valid: boolean }>(
        `/v1/challenge/${id}/validate-token`,
        {
          mfaToken,
        },
        opts
      )
      .then(res => res.data.valid);
  }

  validateChallengeSession(
    { id }: ValidateSessionRequest,
    opts?: AxiosRequestConfig
  ): Promise<ValidateChallengeSession> {
    return this.axios
      .get<ValidateChallengeSession>(`/v1/challenge/${id}/session`, opts)
      .then(res => res.data);
  }

  /**
   * @deprecated This route doesn't support token clients, use /v1/social/:provider/upgrade
   */
  claim({
    code,
    provider,
  }: {
    code: string;
    provider: string;
  }): Promise<AuthToken> {
    return this.axios
      .post(`/v1/social/${provider}/claim`, { code })
      .then(res => res.data);
  }

  socialUpgrade({
    token,
    provider,
    clientId,
    clientSecret,
  }: {
    token: string;
    provider: string;
    clientId: string;
    clientSecret: string;
  }): Promise<AuthToken> {
    return this.axios
      .post(`/v1/social/${provider}/upgrade`, {
        token,
        provider,
        clientId,
        clientSecret,
      })
      .then(res => res.data);
  }

  get(id: string): Promise<EntityResponse> {
    return this.axios.get(`/entities/${id}`).then(res => res.data);
  }

  update(id: string, data: Entity): Promise<EntityResponse> {
    return this.axios.post(`/entities/${id}`, data).then(res => res.data);
  }

  getUsersForEntity(
    id: string,
    entityType: string
  ): Promise<{ users: EntityUser[] }> {
    return this.axios
      .get(`/v1/entities/${entityType}/${id}/users`)
      .then(res => res.data);
  }

  populateDefaultRoles({
    id,
    entityType,
  }: {
    id: string;
    entityType: string;
  }): Promise<{ roles: Role[] }> {
    return this.axios
      .post(`/v1/entities/${entityType}/${id}/populate`)
      .then(res => res.data);
  }

  changePrivilegePreset({
    id,
    entityType,
    preset,
  }: {
    id: string;
    entityType: string;
    preset: string;
  }): Promise<{ roles: Role[] }> {
    return this.axios
      .post(`/v1/entities/${entityType}/${id}/privilege-preset`, { preset })
      .then(res => res.data);
  }

  getRolesForEntity(
    id: string,
    entityType: string
  ): Promise<{ roles: Role[] }> {
    return this.axios
      .post('/v1/roles/search', {
        entityId: id,
        entityType,
      })
      .then(res => res.data);
  }

  addUserToRole(id: string, userId: string): Promise<string> {
    return this.axios
      .put(`/v1/roles/${id}/users`, {
        userId,
      })
      .then(res => res.data);
  }

  removeUserFromRole(id: string, userId: string): Promise<string> {
    return this.axios
      .delete(`/v1/roles/${id}/users/${userId}`)
      .then(res => res.data);
  }

  removeUserFromEntity(
    entityId: string,
    entityType: string,
    userId: string
  ): Promise<{ success: boolean }> {
    return this.axios
      .delete(`/v1/entities/${entityType}/${entityId}/users/${userId}`)
      .then(res => res.data);
  }

  addUserToEntity(
    entityId: string,
    entityType: string,
    userId: string
  ): Promise<void> {
    return this.axios
      .put(`/v1/entities/${entityType}/${entityId}/users/${userId}`)
      .then(res => res.data);
  }

  addUserToRoleByName(
    entityId: string,
    entityType: string,
    userId: string,
    name: string
  ): Promise<void> {
    return this.axios
      .patch(`/v1/roles/user`, { entityId, entityType, userId, name })
      .then(res => res.data);
  }

  getUsersForRole(
    id: string,
    entityType: string,
    roleName: string
  ): Promise<{ data: EntityUser[] }> {
    return this.axios
      .get(`/v1/entities/${entityType}/${id}/${roleName}/users`)
      .then(res => res.data);
  }

  getMfa(id: string): Promise<GetMfaResponse> {
    return this.axios.get(`/v1/mfa/${id}`).then(res => res.data);
  }

  createMfa(data: CreateMfa): Promise<CreateMfaResponse> {
    return this.axios.post('/v1/mfa', data).then(res => res.data);
  }

  verifyMfa(
    id: string,
    data: VerifyMfa,
    opts?: AxiosRequestConfig
  ): Promise<TokenResponse> {
    return this.axios
      .patch(`/v1/mfa/${id}/verify`, data, opts)
      .then(res => res.data);
  }

  generateMfaCode(data: GenerateMfa): Promise<GenerateMfaResponse> {
    return this.axios.post(`/v1/mfa/generate`, data).then(res => res.data);
  }

  deleteMfa(id: string, data: VerifyMfa): Promise<void> {
    return this.axios
      .delete(`/v1/mfa/${id}`, {
        data,
      })
      .then(res => res.data);
  }
}
