import { v4 } from 'uuid';
import { jwtDecode } from 'jwt-decode';
import pino from 'pino';
import createError, { InternalServerError } from 'http-errors';
import {
  AxiosError,
  AxiosHeaders,
  AxiosInstance,
  InternalAxiosRequestConfig,
} from 'axios';
import axiosRetry from '@ordermentum/axios-retry';
import { Entity, User, Claims } from './types';

const logger = pino({
  level: 'info',
});

export const getEntities = (entity: Entity) => {
  const entities: { [key: string]: string[] } = {};
  const permissions = entity.permissions ?? [];

  for (const permission of permissions) {
    const type = permission.metadata?.type ?? 'unknown';
    entities[type] = entities[type] || [];
    entities[type].push(permission.entity);
  }

  return entities;
};

const getAdminPermissions = (entity: Entity, type?: string) =>
  (entity.permissions ?? []).filter(
    permission =>
      permission.entity === 'ordermentum' &&
      (type ? permission.type === type : true)
  );

export const isAdmin = (entity: Entity) => {
  const adminPermissions = getAdminPermissions(entity, 'administrator');
  return adminPermissions.length > 0;
};

export const isSuperAdmin = (entity: Entity) => {
  const adminPermissions = getAdminPermissions(entity, 'superAdministrator');
  return adminPermissions.length > 0;
};

export const isSuperUser = (auth: User): boolean =>
  !!(auth.context.admin || auth.context.superAdmin);

/**
 * @description Extracts claims from a JWT token
 * @param accessToken {string}
 * @returns {Claims} - Claims extracted from the token | return empty claims if error
 */
export const extractClaims = (accessToken: string): Claims | null => {
  try {
    const decoded = jwtDecode<{
      amr: string[];
      iat: number;
      stepUpPrivileges: string[];
    }>(accessToken);

    if (!decoded) {
      logger.info('Failed to decode JWT');
      return null;
    }

    if (decoded && typeof decoded === 'object') {
      const claims: Claims = {
        amr: decoded.amr,
        iat: decoded.iat
          ? new Date((decoded.iat as number) * 1000).toISOString()
          : undefined,
        stepUpPrivileges: decoded.stepUpPrivileges,
      };
      return claims;
    }
  } catch (e) {
    logger.info(e, 'Failed to extract claims');
    return null;
  }
  return null;
};

/**
 * @description A helper class that can wrap an auth{User} object and provide methods for privilege checks
 * @example
 * const Auth = new Authorization(auth);
 * //check privilege is present on the user
 * Auth.can('access', {id}, 'supplier')'; // Does use have access on the supplier {id}
 * Auth.canAll(['access', 'exports'], {id}, 'supplier')'; // Does use have all privileges on this supplier {id}
 * Auth.canOne(['access', 'exports'], {id}, 'supplier')'; // Does use have one of privileges on this supplier {id}
 */
export class Authorization {
  auth: User;

  constructor(auth: User) {
    this.auth = auth;
  }

  // Checks if the entity being accessed is the user itself
  is(id) {
    if (this.auth.id == null || id == null) {
      return false;
    }
    return (
      this.isSuperUser ||
      this.auth.id === id ||
      this.auth.id.toString() === id.toString()
    );
  }

  preChecks(entity: string) {
    if (this.isSuperUser || this.is(entity)) {
      return true;
    }
    return false;
  }

  /**
   * @description Does this privilege exist on this entity
   * @param privilege {string}
   * @param entity {string}
   * @param entityType {string}
   * @returns {boolean}
   */
  can(privilege: string, entity: string, entityType: string) {
    if (this.preChecks(entity)) {
      return true;
    }
    switch (entityType) {
      case 'supplier': {
        return (
          this.auth.context.supplierPrivileges?.[entity]?.includes(privilege) ??
          false
        );
      }
      case 'retailer': {
        return (
          this.auth.context.retailerPrivileges?.[entity]?.includes(privilege) ??
          false
        );
      }
      default:
        return false;
    }
  }

  /**
   * @description Does all the privileges exist on the entity
   * @param privileges {string[]}
   * @param entity {string}
   * @param entityType {string}
   * @returns {boolean}
   */
  canAll(privileges: string[], entity: string, entityType: string) {
    if (!privileges.length) {
      return true;
    }
    if (this.preChecks(entity)) {
      return true;
    }
    const entityPrivileges =
      this.auth.context[
        entityType === 'supplier' ? 'supplierPrivileges' : 'retailerPrivileges'
      ]?.[entity] ?? [];

    if (!entityPrivileges.length) {
      return false;
    }

    return privileges.every(privilege => entityPrivileges.includes(privilege));
  }

  /**
   * @description Does one of the privileges exist on the entity
   * @param privileges {string[]}
   * @param entity {string}
   * @param entityType {string}
   * @returns {boolean}
   */
  canOne(privileges: string[], entity: string, entityType: string) {
    if (!privileges.length) {
      return true;
    }
    if (this.preChecks(entity)) {
      return true;
    }
    const entityPrivileges =
      this.auth.context[
        entityType === 'supplier' ? 'supplierPrivileges' : 'retailerPrivileges'
      ]?.[entity] ?? [];

    if (!entityPrivileges.length) {
      return false;
    }

    return privileges.some(privilege => entityPrivileges.includes(privilege));
  }

  // Only use this when the entitytype is ambigious
  canUnsafe(privilege: string, entity: string): boolean {
    if (this.isSuperUser) {
      return true;
    }
    if (this.is(entity)) {
      return true;
    }

    return (
      this.auth.context.supplierPrivileges?.[entity]?.includes(privilege) ??
      this.auth.context.retailerPrivileges?.[entity]?.includes(privilege) ??
      false
    );
  }

  get isSuperAdmin() {
    return !!this.auth.context.superAdmin;
  }

  get isAdmin() {
    return !!this.auth.context.admin;
  }

  get isSuperUser() {
    return !!(this.auth.context.admin || this.auth.context.superAdmin);
  }

  get id() {
    return this.auth.id;
  }

  get context() {
    return this.auth.context;
  }

  get permissions() {
    return this.auth.permissions;
  }

  get isMfaEnabled() {
    return !!this.auth.mfaId;
  }

  /**
   * Checks if the user has stepped up authentication (MFA) for sensitive actions.
   * @param options {object} Options for step-up validation
   * @param options.privilege {string} Optional specific privilege to check
   * @param options.leeway {number} Allowed leeway in seconds for issued_at (default: 900s = 15min)
   * @returns {boolean}
   * @example
   * // Check if user has any step-up privileges
   * auth.isSteppedUp()
   *
   * // Check if user has specific privilege
   * auth.isSteppedUp({ privilege: 'set-owner' })
   *
   * // Check with custom time leeway
   * auth.isSteppedUp({ leeway: 300 }) // 5 minutes
   *
   * // Check specific privilege with custom leeway
   * auth.isSteppedUp({ privilege: 'set-owner', leeway: 600 }) // 10 minutes
   */
  isSteppedUp(options?: { privilege?: string; leeway?: number }): boolean {
    const { privilege, leeway = 900 } = options || {};

    if (!this.auth.claims) return false;

    const { amr, stepUpPrivileges, iat } = this.auth.claims;

    // First check if MFA was completed
    if (!Array.isArray(amr) || !amr.includes('mfa')) {
      return false;
    }

    // If specific privilege requested, check if it's in stepUpPrivileges
    if (privilege) {
      if (
        !Array.isArray(stepUpPrivileges) ||
        !stepUpPrivileges.includes(privilege)
      ) {
        return false;
      }
    }
    // Finally check the time window
    if (iat) {
      const issuedAt = new Date(iat).getTime();

      if (isNaN(issuedAt)) {
        return false;
      }

      const now = Date.now();
      return now - issuedAt <= leeway * 1000;
    }

    return false;
  }
}

/**
 * @param id userId
 * @param retailerId
 * @param privileges List of privileges
 * @description [For system use only] Generate a retailer based authentication
 */
export const getRetailerAuth = (
  id: string = v4(),
  retailerId: string = v4(),
  privileges: string[] = ['access']
) =>
  // @ts-ignore
  new Authorization({
    id,
    context: {
      retailers: [retailerId],
      suppliers: [],
      retailerPrivileges: {
        [retailerId]: privileges,
      },
      supplierPrivileges: {},
      admin: false,
      superAdmin: false,
    },
  });

/**
 * @param id userId
 * @description [For system use only] Generate an admin based authentication
 */
export const getAdminAuth = (id: string = v4()) =>
  // @ts-ignore
  new Authorization({
    id,
    context: {
      admin: true,
      superAdmin: false,
      supplierPrivileges: {},
      retailerPrivileges: {},
      suppliers: [],
      retailers: [],
    },
  });

/**
 * @param id userId
 * @param supplierId
 * @param privileges List of privileges
 * @description [For system use only] Generate a supplier based authentication
 */
export const getSupplierAuth = (
  id: string = v4(),
  supplierId: string = v4(),
  privileges: string[] = ['access']
) =>
  // @ts-ignore
  new Authorization({
    id,
    context: {
      suppliers: [supplierId],
      retailers: [],
      supplierPrivileges: {
        [supplierId]: privileges,
      },
      retailerPrivileges: {},
      admin: false,
      superAdmin: false,
    },
  });

export const btoa = s => Buffer.from(s).toString('base64');

export function errorify(err: AxiosError): Error {
  if (typeof err !== 'object') return new InternalServerError('Unknown Error');
  if (err.code === 'ECONNABORTED')
    return new InternalServerError('Connection Aborted');
  if (err.code === 'ECONNREFUSED')
    return new InternalServerError('Connection Refused');
  if (!err.response)
    return createError(500, 'Invalid Response', { error: err });

  const { response } = err;
  const {
    status = 500,
    statusText = 'Unknown Error',
    data = null,
  } = response ?? {};

  return createError(status, statusText ?? 'Unknown Error', {
    ...(data ?? {}),
  });
}

export type Options = {
  username: string;
  password: string;
  retries?: number;
  timeout?: number;
};

export function configureInterceptors(
  instance: AxiosInstance,
  opts?: Options | null,
  token?: string
) {
  const onRequestInterceptor = (
    config: InternalAxiosRequestConfig
  ): InternalAxiosRequestConfig => {
    const newConfig: InternalAxiosRequestConfig = {
      ...config,
      headers: new AxiosHeaders({
        ...config.headers,
        'Content-Type': 'application/json',
        Authorization:
          token ??
          `Basic ${btoa(`${opts?.username ?? ''}:${opts?.password ?? ''}`)}`,
      }),
    };
    return newConfig;
  };

  if (opts?.retries && opts.retries > 0) {
    axiosRetry(instance, { retries: opts.retries });
  }

  instance.interceptors.request.use(onRequestInterceptor);
  instance.interceptors.response.use(
    res => res,
    err => Promise.reject(errorify(err))
  );

  return instance;
}
