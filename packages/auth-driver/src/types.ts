/* eslint-disable camelcase */
export type AuthSearch = {
  key: string;
  value: string | string[];
};

export type Permission = {
  entity: string;
  type: string;
  metadata?: { [type: string]: string };
};

export type Entity = {
  emails: string[];
  email: string;
  // eslint-disable-next-line camelcase
  created_at: string;
  id: string;
  name: {
    full: string;
    firstName: string;
    lastName: string;
  };
  firstName: string;
  lastName: string;
  permissions?: Permission[];
  password: string;
  metadata: {
    type: string;
  };
  mfaId?: string | null;
  ipAddress?: string;
  userAgent?: string;
  mfaEnforcedFrom?: string | null;
};

export type Context = {
  suppliers: string[];
  retailers: string[];
  retailerPrivileges?: {
    [id: string]: string[];
  };
  supplierPrivileges?: {
    [id: string]: string[];
  };
  admin: boolean;
  superAdmin: boolean;
};

export type UserRole = {
  id: string;
  userId: string;
  roleId: string;
  role?: Role;
  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string;
};

export type User = Entity & {
  admin: boolean;
  superAdmin: boolean;
  context: Context;
  userRoles: UserRole[];
  claims?: {
    amr?: string[];
    iat?: string;
    stepUpPrivileges?: string[];
  };
};

export type AuthToken = {
  id: string;
  access_token: string;
  refresh_token: string;
  token: string; // backwards compat
  expires_in: number; // seconds
  refresh_token_expires_in: number; // seconds
  token_type: string;
};

export type EntityResponse = null | User;
export type SignInResponse = AuthToken;

export type EntityUser = {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  createdAt: string;
  updatedAt: string;
  roleId: string;
  roleName: string;
  privileges: string[];
};

export type Role = {
  id: string;
  name: string;
  entityId: string;
  entityType: string;
  privileges: string[];
  description: string;
  createdAt: string;
  updatedAt: string;
  sortOrder: number;
};

export type MFAValidateTokenRequest = {
  id: string;
  mfaToken: string;
};

export type ValidateSessionRequest = {
  id: string;
};

export type MFAGrant = {
  code: string;
  userId: string;
  state: string;
};

export enum MFAMethod {
  TOTP = 'totp',
  SMS = 'sms',
  EMAIL = 'email',
}

export type GetMfaResponse = {
  id: string;
  method: MFAMethod;
  userId: string;
  target: string | null;
  generatedCount: number;
  lastVerifiedAt: string | null;
  createdAt: string;
  updatedAt: string;
};

export type CreateMfa = {
  method: MFAMethod;
  target: string | null;
};

export type CreateMfaResponse = {
  id: string;
  method: MFAMethod;
  url: string | null;
  qrCodeUrl: string | null;
};

export type VerifyMfa = {
  code: string;
};

export type GenerateMfa = {
  state?: string;
  userId: string;
  token?: string;
};

export type GenerateMfaResponse = {
  retries: number;
};

export type ValidateChallengeSession = {
  retries: number;
};

export type TokenResponse = {
  id: string;
  token: string;
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: string;
  refresh_token_expires_in: number;
};

export type Claims = {
  amr?: string[];
  iat?: string;
  stepUpPrivileges?: string[];
};
