import { expect } from 'chai';
import axios from 'axios';
import { OAuth } from '../src';

describe('Oauth', () => {
  it('creates client', async () => {
    const oauth = new OAuth({
      host: 'https://localhost:3000',
      clientId: 'clientId',
      clientSecret: 'clientSecret',
    });
    expect(oauth.client.defaults.baseURL).to.equal('https://localhost:3000');
  });

  it('creates client with custom axios instance', async () => {
    const client = axios.create({
      baseURL: 'https://localhost:3000',
      headers: new axios.AxiosHeaders({
        'X-Custom-Header': 'foobar',
      }),
    });
    const oauth = OAuth.build(client, {
      host: 'https://localhost:3000',
      clientId: 'clientId',
      clientSecret: 'clientSecret',
    });
    expect(oauth.client.defaults.baseURL).to.equal('https://localhost:3000');
    expect(oauth.client.defaults.headers['X-Custom-Header']).to.equal('foobar');
  });
});
