import { expect } from 'chai';
import nock from 'nock';
import axios from 'axios';
import { randomUUID } from 'crypto';
import Driver from '../src';
import { createMfaVerificationToken } from './fixtures/token.fixtures';

describe('AuthDriver', () => {
  it('deserializes correct', async () => {
    const driver = new Driver('http://auth:3001', {
      username: '',
      password: '',
    });
    expect(driver).to.not.equal(null);
    expect(driver.axios.defaults.baseURL).to.equal('http://auth:3001');
  });

  it('creates a driver from an axios instance', async () => {
    const client = axios.create({
      baseURL: 'http://auth:3001',
      headers: new axios.AxiosHeaders({
        'X-Custom-Header': 'foobar',
      }),
    });

    const driver = Driver.build(client, {
      username: '',
      password: '',
    });

    expect(driver).to.not.equal(null);
    expect(driver.axios.defaults.baseURL).to.equal('http://auth:3001');
    expect(driver.axios.defaults.headers['X-Custom-Header']).to.equal('foobar');
  });

  it('throws an error if no credentials are provided', async () => {
    expect(() => new Driver('http://auth:3001')).to.throw(
      'No credentials found'
    );
  });

  it('throws an error if no url is provided', async () => {
    expect(() => new Driver('', { username: '', password: '' })).to.throw(
      'Driver must be instantiated with a url'
    );
  });

  it('makes a request to validate a token', async () => {
    const driver = new Driver('http://auth:3001', {
      username: '',
      password: '',
    });

    nock('http://auth:3001')
      .get('/entities?token=123')
      .reply(200, { token: '123' });

    const res = await driver.validate('123');
    expect(res).to.deep.equal({ token: '123' });
  });

  it('makes a request to sign in', async () => {
    const driver = new Driver('http://auth:3001', {
      username: '',
      password: '',
    });

    nock('http://auth:3001')
      .post('/login', { email: '<EMAIL>', password: '123' })
      .reply(200, { token: '123' });

    const res = await driver.signIn('<EMAIL>', '123');
    expect(res).to.deep.equal({ token: '123' });
  });

  it('retries a request if retries is set', async () => {
    const driver = new Driver('http://auth:3001', {
      username: '',
      password: '',
      retries: 3,
    });

    nock('http://auth:3001')
      .get('/entities?token=123')
      .delayConnection(400)
      .reply(500, 'Connection Aborted')
      .get('/entities?token=123')
      .reply(200, { token: '123' });

    const res = await driver.validate('123');
    expect(res).to.deep.equal({ token: '123' });
  });

  it('does not retry a request if retries is not set', async () => {
    const driver = new Driver('http://auth:3001', {
      username: '',
      password: '',
    });

    nock('http://auth:3001')
      .get('/entities?token=123')
      .reply(500, 'Connection Aborted')
      .get('/entities?token=123')
      .reply(200, { token: '123' });

    let err;
    try {
      await driver.validate('123');
    } catch (e) {
      err = e;
    }

    expect(err.message).to.equal('Unknown Error');
    expect(err.status).to.equal(500);
  });

  it('it does return correct error data', async () => {
    const driver = new Driver('http://auth:3001', {
      username: '',
      password: '',
    });

    nock('http://auth:3001')
      .post('/v1/social/google/claim')
      .reply(403, {
        error: 'Forbidden',
        message: 'MFA is required',
        data: {
          method: 'totp',
          state: '47c491120bfc2474066d522733deb5fd',
          target: null,
          userId: '155fb029-ea61-4bf3-b8cc-07cd972ef345',
        },
      });

    let err;
    try {
      await driver.claim({ code: '12321', provider: 'google' });
    } catch (e) {
      err = e;
    }

    expect(err.message).to.equal('MFA is required');
    expect(err.status).to.equal(403);
    expect(err.data, 'should not wrap the error data').to.deep.equal({
      method: 'totp',
      state: '47c491120bfc2474066d522733deb5fd',
      target: null,
      userId: '155fb029-ea61-4bf3-b8cc-07cd972ef345',
    });
  });

  it('accepts additional headers', async () => {
    const userId = randomUUID();
    const driver = new Driver('http://auth:3001', {
      username: '',
      password: '',
    });

    const apiCallMock = nock('http://auth:3001', {
      reqheaders: {
        'X-Custom-Header': 'foobar',
      },
    })
      .get(`/v1/challenge/${userId}/session`)
      .reply(200, { retries: 1 });

    const result = await driver.validateChallengeSession(
      { id: userId },
      { headers: { 'X-Custom-Header': 'foobar' } }
    );

    expect(result.retries).to.equal(1);
    expect(apiCallMock.isDone()).to.be.true;
  });

  describe('verifyMfa', () => {
    it('verifies MFA code successfully', async () => {
      const userId = randomUUID();
      const mfaId = randomUUID();
      const driver = new Driver('http://auth:3001', {
        username: '',
        password: '',
      });

      const expectedResponse = createMfaVerificationToken(userId);

      nock('http://auth:3001')
        .patch(`/v1/mfa/${mfaId}/verify`, { code: '123456' })
        .reply(200, expectedResponse);

      const result = await driver.verifyMfa(mfaId, { code: '123456' });

      expect(result).to.deep.equal(expectedResponse);
    });

    it('verifies MFA code with additional options', async () => {
      const userId = randomUUID();
      const mfaId = randomUUID();
      const driver = new Driver('http://auth:3001', {
        username: '',
        password: '',
      });

      const expectedResponse = createMfaVerificationToken(userId);

      const apiCallMock = nock('http://auth:3001', {
        reqheaders: {
          'X-Custom-Header': 'foobar',
        },
      })
        .patch(`/v1/mfa/${mfaId}/verify`, { code: '123456' })
        .reply(200, expectedResponse);

      const result = await driver.verifyMfa(
        mfaId,
        { code: '123456' },
        { headers: { 'X-Custom-Header': 'foobar' } }
      );

      expect(result).to.deep.equal(expectedResponse);
      expect(apiCallMock.isDone()).to.be.true;
    });

    it('handles MFA verification error', async () => {
      const mfaId = randomUUID();
      const driver = new Driver('http://auth:3001', {
        username: '',
        password: '',
      });

      nock('http://auth:3001')
        .patch(`/v1/mfa/${mfaId}/verify`, { code: 'invalid' })
        .reply(400, {
          error: 'Invalid MFA code',
          message: 'The provided MFA code is invalid or expired',
        });

      let err;
      try {
        await driver.verifyMfa(mfaId, { code: 'invalid' });
      } catch (e) {
        err = e;
      }

      expect(err.message).to.equal(
        'The provided MFA code is invalid or expired'
      );
      expect(err.status).to.equal(400);
    });
  });
});
