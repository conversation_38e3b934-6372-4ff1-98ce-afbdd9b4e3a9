import { expect } from 'chai';
import {
  Authorization,
  getSupplierAuth,
  getRetailerAuth,
  getAdminAuth,
} from '../src/helpers';
import {
  UserFixtures,
  ClaimPatterns,
  TestConstants,
} from './fixtures/authorization.fixtures';

describe('Authorization', () => {
  describe('isSteppedUp', () => {
    describe('basic MFA validation', () => {
      it('should return false when no claims are present', () => {
        const user = UserFixtures.basic();
        const auth = new Authorization(user);

        expect(auth.isSteppedUp()).to.be.false;
      });

      it('should return false when claims.amr is not an array', () => {
        const user = UserFixtures.withInvalidMfaFormat();
        const auth = new Authorization(user);

        expect(auth.isSteppedUp()).to.be.false;
      });

      it('should return false when claims.amr does not include mfa', () => {
        const user = UserFixtures.withPasswordOnly();
        const auth = new Authorization(user);

        expect(auth.isSteppedUp()).to.be.false;
      });

      it('should return false when claims.amr includes mfa but no iat', () => {
        const user = UserFixtures.withNullTimestamp();
        const auth = new Authorization(user);

        expect(auth.isSteppedUp()).to.be.false;
      });

      it('should return true when MFA is completed and within time window', () => {
        const user = UserFixtures.withCurrentMfa();
        const auth = new Authorization(user);

        expect(auth.isSteppedUp()).to.be.true;
      });

      it('should return false when MFA is completed but outside time window', () => {
        const user = UserFixtures.withOldMfa();
        const auth = new Authorization(user);

        expect(auth.isSteppedUp()).to.be.false;
      });

      it('should return true when MFA is completed and within custom time window', () => {
        const user = UserFixtures.withRecentMfa();
        const auth = new Authorization(user);

        expect(auth.isSteppedUp({ leeway: TestConstants.SHORT_LEEWAY })).to.be
          .true;
      });
    });

    describe('privilege-specific validation', () => {
      it('should return false when specific privilege is requested but not in stepUpPrivileges', () => {
        const user = UserFixtures.withSetPermissionsPrivileges();
        const auth = new Authorization(user);

        expect(
          auth.isSteppedUp({ privilege: TestConstants.PRIVILEGES.SET_OWNER })
        ).to.be.false;
      });

      it('should return false when specific privilege is requested but stepUpPrivileges is not an array', () => {
        const user = UserFixtures.withInvalidPrivilegesFormat();
        const auth = new Authorization(user);

        expect(
          auth.isSteppedUp({ privilege: TestConstants.PRIVILEGES.SET_OWNER })
        ).to.be.false;
      });

      it('should return false when specific privilege is requested but stepUpPrivileges is undefined', () => {
        const user = UserFixtures.withCurrentMfa();
        const auth = new Authorization(user);

        expect(
          auth.isSteppedUp({ privilege: TestConstants.PRIVILEGES.SET_OWNER })
        ).to.be.false;
      });

      it('should return true when specific privilege is in stepUpPrivileges and within time window', () => {
        const user = UserFixtures.withBothPrivileges();
        const auth = new Authorization(user);

        expect(
          auth.isSteppedUp({ privilege: TestConstants.PRIVILEGES.SET_OWNER })
        ).to.be.true;
        expect(
          auth.isSteppedUp({
            privilege: TestConstants.PRIVILEGES.SET_PERMISSIONS,
          })
        ).to.be.true;
      });

      it('should return false when specific privilege is in stepUpPrivileges but outside time window', () => {
        const user = UserFixtures.withOldMfa();
        user.claims = {
          ...user.claims,
          stepUpPrivileges: [TestConstants.PRIVILEGES.SET_OWNER],
        };
        const auth = new Authorization(user);

        expect(
          auth.isSteppedUp({ privilege: TestConstants.PRIVILEGES.SET_OWNER })
        ).to.be.false;
      });

      it('should return true when specific privilege is in stepUpPrivileges and within custom time window', () => {
        const user = UserFixtures.withRecentMfa();
        user.claims = {
          ...user.claims,
          stepUpPrivileges: [TestConstants.PRIVILEGES.SET_OWNER],
        };
        const auth = new Authorization(user);

        expect(
          auth.isSteppedUp({
            privilege: TestConstants.PRIVILEGES.SET_OWNER,
            leeway: TestConstants.SHORT_LEEWAY,
          })
        ).to.be.true;
      });
    });

    describe('privilege-first checking logic', () => {
      it('should check privileges before time window for better performance', () => {
        const user = UserFixtures.withSetPermissionsPrivileges();
        const auth = new Authorization(user);

        // Should fail on privilege check, not time check
        expect(
          auth.isSteppedUp({ privilege: TestConstants.PRIVILEGES.SET_OWNER })
        ).to.be.false;
      });

      it('should check MFA completion before privilege check', () => {
        const user = UserFixtures.withPasswordAndSetOwner();
        const auth = new Authorization(user);

        // Should fail on MFA check, not privilege check
        expect(
          auth.isSteppedUp({ privilege: TestConstants.PRIVILEGES.SET_OWNER })
        ).to.be.false;
      });
    });

    describe('backward compatibility', () => {
      it('should work with existing code that calls isSteppedUp() without parameters', () => {
        const user = UserFixtures.withCurrentMfa();
        const auth = new Authorization(user);

        expect(auth.isSteppedUp()).to.be.true;
      });

      it('should work with existing code that calls isSteppedUp(leeway) with only time parameter', () => {
        const user = UserFixtures.withRecentMfa();
        const auth = new Authorization(user);

        expect(auth.isSteppedUp({ leeway: TestConstants.SHORT_LEEWAY })).to.be
          .true;
      });
    });

    describe('edge cases', () => {
      it('should handle empty stepUpPrivileges array', () => {
        const user = UserFixtures.withEmptyPrivileges();
        const auth = new Authorization(user);

        expect(
          auth.isSteppedUp({ privilege: TestConstants.PRIVILEGES.SET_OWNER })
        ).to.be.false;
      });

      it('should handle null iat', () => {
        const user = UserFixtures.withNullTimestamp();
        const auth = new Authorization(user);

        expect(auth.isSteppedUp()).to.be.false;
      });

      it('should handle invalid iat format', () => {
        const user = UserFixtures.withInvalidTimestamp();
        const auth = new Authorization(user);

        expect(auth.isSteppedUp()).to.be.false;
      });

      it('should handle zero leeway', () => {
        // Test that a token issued in the past fails with zero leeway
        const user = UserFixtures.withExactCurrentMfa();
        const auth = new Authorization(user);

        expect(auth.isSteppedUp({ leeway: TestConstants.ZERO_LEEWAY })).to.be
          .false;

        // Test that a token issued in the future passes with zero leeway
        const futureUser = UserFixtures.withExactCurrentMfa();
        futureUser.claims = {
          ...futureUser.claims,
          iat: new Date(Date.now() + 1000).toISOString(), // 1 second in the future
        };
        const futureAuth = new Authorization(futureUser);

        expect(futureAuth.isSteppedUp({ leeway: TestConstants.ZERO_LEEWAY })).to
          .be.true;
      });

      it('should handle negative leeway', () => {
        const user = UserFixtures.withCurrentMfa();
        const auth = new Authorization(user);

        expect(auth.isSteppedUp({ leeway: TestConstants.NEGATIVE_LEEWAY })).to
          .be.false;
      });
    });
  });

  describe('integration with helper functions', () => {
    it('should work with getSupplierAuth helper', () => {
      const supplierAuth = getSupplierAuth('user-id', 'supplier-id', [
        'access',
      ]);
      supplierAuth.auth.claims = ClaimPatterns.mfaWithSetOwner;

      expect(
        supplierAuth.isSteppedUp({
          privilege: TestConstants.PRIVILEGES.SET_OWNER,
        })
      ).to.be.true;
    });

    it('should work with getRetailerAuth helper', () => {
      const retailerAuth = getRetailerAuth('user-id', 'retailer-id', [
        'access',
      ]);
      retailerAuth.auth.claims = ClaimPatterns.mfaWithSetPermissions;

      expect(
        retailerAuth.isSteppedUp({
          privilege: TestConstants.PRIVILEGES.SET_PERMISSIONS,
        })
      ).to.be.true;
    });

    it('should work with getAdminAuth helper', () => {
      const adminAuth = getAdminAuth('user-id');
      adminAuth.auth.claims = ClaimPatterns.mfaWithBothPrivileges;

      expect(
        adminAuth.isSteppedUp({ privilege: TestConstants.PRIVILEGES.SET_OWNER })
      ).to.be.true;
      expect(
        adminAuth.isSteppedUp({
          privilege: TestConstants.PRIVILEGES.SET_PERMISSIONS,
        })
      ).to.be.true;
    });
  });
});
