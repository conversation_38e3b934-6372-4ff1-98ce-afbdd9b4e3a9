import { expect } from 'chai';
import jwt from 'jsonwebtoken';
import { extractClaims } from '../src/helpers';

describe('Helper Functions', () => {
  context('extractClaims', () => {
    context('valid JWT tokens', () => {
      it('should extract all claims from a valid JWT token', () => {
        const payload = {
          amr: ['mfa', 'pwd'],
          iat: Math.floor(Date.now() / 1000),
          stepUpPrivileges: ['set-owner', 'set-permissions'],
          sub: 'user123',
          exp: Math.floor(Date.now() / 1000) + 3600,
        };

        const token = jwt.sign(payload, 'test-secret');
        const claims = extractClaims(token);

        expect(claims).to.not.be.null;
        expect(claims).to.deep.include({
          amr: ['mfa', 'pwd'],
          stepUpPrivileges: ['set-owner', 'set-permissions'],
        });
        expect(claims!.iat).to.be.a('string');
        expect(new Date(claims!.iat!)).to.be.instanceOf(Date);
      });

      it('should extract only amr when other claims are not present', () => {
        const payload = {
          amr: ['mfa'],
          sub: 'user123',
        };

        const token = jwt.sign(payload, 'test-secret');
        const claims = extractClaims(token);

        expect(claims).to.not.be.null;
        expect(claims!.amr).to.deep.equal(['mfa']);
        expect(claims!.stepUpPrivileges).to.be.undefined;
        // Note: JWT library automatically adds iat, so we check it exists but don't test exact value
        expect(claims!.iat).to.be.a('string');
      });

      it('should extract only iat when other claims are not present', () => {
        const payload = {
          iat: Math.floor(Date.now() / 1000),
          sub: 'user123',
        };

        const token = jwt.sign(payload, 'test-secret');
        const claims = extractClaims(token);

        expect(claims).to.not.be.null;
        expect(claims!.iat).to.be.a('string');
        expect(claims!.amr).to.be.undefined;
        expect(claims!.stepUpPrivileges).to.be.undefined;
      });

      it('should extract only stepUpPrivileges when other claims are not present', () => {
        const payload = {
          stepUpPrivileges: ['set-owner'],
          sub: 'user123',
        };

        const token = jwt.sign(payload, 'test-secret');
        const claims = extractClaims(token);

        expect(claims).to.not.be.null;
        expect(claims!.stepUpPrivileges).to.deep.equal(['set-owner']);
        expect(claims!.amr).to.be.undefined;
        // Note: JWT library automatically adds iat, so we check it exists but don't test exact value
        expect(claims!.iat).to.be.a('string');
      });

      it('should handle empty arrays in claims', () => {
        const payload = {
          amr: [],
          stepUpPrivileges: [],
          iat: Math.floor(Date.now() / 1000),
        };

        const token = jwt.sign(payload, 'test-secret');
        const claims = extractClaims(token);

        expect(claims).to.not.be.null;
        expect(claims).to.deep.include({
          amr: [],
          stepUpPrivileges: [],
        });
        expect(claims!.iat).to.be.a('string');
      });

      it('should convert iat timestamp correctly', () => {
        const timestamp = 1640995200; // 2022-01-01 00:00:00 UTC
        const payload = {
          iat: timestamp,
        };

        const token = jwt.sign(payload, 'test-secret');
        const claims = extractClaims(token);

        expect(claims).to.not.be.null;
        expect(claims!.iat).to.equal('2022-01-01T00:00:00.000Z');
      });

      it('should handle zero iat timestamp', () => {
        // Create a token with a specific timestamp by decoding and re-encoding
        const payload = {
          iat: 0,
          sub: 'test',
        };

        const token = jwt.sign(payload, 'test-secret');
        const claims = extractClaims(token);

        expect(claims).to.not.be.null;
        // JWT library will override our iat, so we test that it extracts a valid timestamp
        expect(claims!.iat).to.be.a('string');
        expect(new Date(claims!.iat!)).to.be.instanceOf(Date);
      });
    });

    context('edge cases', () => {
      it('should handle missing iat gracefully', () => {
        const payload = {
          amr: ['mfa'],
          stepUpPrivileges: ['set-owner'],
        };

        const token = jwt.sign(payload, 'test-secret');
        const claims = extractClaims(token);

        expect(claims).to.not.be.null;
        expect(claims!.amr).to.deep.equal(['mfa']);
        expect(claims!.stepUpPrivileges).to.deep.equal(['set-owner']);
        // JWT library automatically adds iat, so we check it exists
        expect(claims!.iat).to.be.a('string');
      });

      it('should handle undefined iat gracefully', () => {
        const payload = {
          amr: ['mfa'],
          stepUpPrivileges: ['set-owner'],
        };

        const token = jwt.sign(payload, 'test-secret');
        const claims = extractClaims(token);

        expect(claims).to.not.be.null;
        expect(claims!.amr).to.deep.equal(['mfa']);
        expect(claims!.stepUpPrivileges).to.deep.equal(['set-owner']);
        // JWT library automatically adds iat, so we check it exists
        expect(claims!.iat).to.be.a('string');
      });

      it('should handle non-array amr values', () => {
        const payload = {
          amr: 'mfa', // Should be array but is string
          iat: Math.floor(Date.now() / 1000),
        };

        const token = jwt.sign(payload, 'test-secret');
        const claims = extractClaims(token);

        expect(claims).to.not.be.null;
        expect(claims!.amr).to.equal('mfa');
        expect(claims!.iat).to.be.a('string');
      });

      it('should handle non-array stepUpPrivileges values', () => {
        const payload = {
          stepUpPrivileges: 'set-owner', // Should be array but is string
          iat: Math.floor(Date.now() / 1000),
        };

        const token = jwt.sign(payload, 'test-secret');
        const claims = extractClaims(token);

        expect(claims).to.not.be.null;
        expect(claims!.stepUpPrivileges).to.equal('set-owner');
        expect(claims!.iat).to.be.a('string');
      });

      it('should handle mixed data types in arrays', () => {
        const payload = {
          amr: ['mfa', 123, true, null],
          stepUpPrivileges: ['set-owner', 456, false],
          iat: Math.floor(Date.now() / 1000),
        };

        const token = jwt.sign(payload, 'test-secret');
        const claims = extractClaims(token);

        expect(claims).to.not.be.null;
        expect(claims!.amr).to.deep.equal(['mfa', 123, true, null]);
        expect(claims!.stepUpPrivileges).to.deep.equal([
          'set-owner',
          456,
          false,
        ]);
        expect(claims!.iat).to.be.a('string');
      });
    });

    context('error handling', () => {
      it('should return null for invalid JWT token', () => {
        const invalidToken = 'invalid.jwt.token';
        const claims = extractClaims(invalidToken);

        expect(claims).to.be.null;
      });

      it('should return null for malformed JWT token', () => {
        const malformedToken = 'header.payload'; // Missing signature
        const claims = extractClaims(malformedToken);

        expect(claims).to.be.null;
      });

      it('should return null for empty string', () => {
        const claims = extractClaims('');

        expect(claims).to.be.null;
      });

      it('should return null for null token', () => {
        const claims = extractClaims(null as any);

        expect(claims).to.be.null;
      });

      it('should return null for undefined token', () => {
        const claims = extractClaims(undefined as any);

        expect(claims).to.be.null;
      });

      it('should return null for non-string token', () => {
        const claims = extractClaims(123 as any);

        expect(claims).to.be.null;
      });

      it('should return null for object token', () => {
        const claims = extractClaims({} as any);

        expect(claims).to.be.null;
      });

      it('should return null for malformed iat value', () => {
        // Create a valid token first
        const validToken = jwt.sign(
          {
            iat: Math.floor(Date.now() / 1000),
            amr: ['mfa'],
            stepUpPrivileges: ['test'],
          },
          'test-secret'
        );

        // Manually create a malformed token by replacing the iat value in the payload
        const parts = validToken.split('.');
        const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
        payload.iat = 'not-a-timestamp';

        const malformedPayload = Buffer.from(JSON.stringify(payload)).toString(
          'base64'
        );
        const malformedToken = `${parts[0]}.${malformedPayload}.${parts[2]}`;

        const claims = extractClaims(malformedToken);

        expect(claims).to.be.null;
      });
    });

    context('real-world scenarios', () => {
      it('should handle typical MFA token with all claims', () => {
        const now = Math.floor(Date.now() / 1000);
        const payload = {
          sub: 'user-123',
          iss: 'auth-service',
          aud: 'api-service',
          amr: ['mfa'],
          iat: now,
          exp: now + 3600,
          stepUpPrivileges: ['set-owner', 'set-permissions', 'delete-entity'],
          jti: 'token-123',
        };

        const token = jwt.sign(payload, 'test-secret');
        const claims = extractClaims(token);

        expect(claims).to.not.be.null;
        expect(claims).to.deep.include({
          amr: ['mfa'],
          stepUpPrivileges: ['set-owner', 'set-permissions', 'delete-entity'],
        });
        expect(claims!.iat).to.be.a('string');
        expect(new Date(claims!.iat!)).to.be.instanceOf(Date);
      });

      it('should handle password-only authentication token', () => {
        const now = Math.floor(Date.now() / 1000);
        const payload = {
          sub: 'user-123',
          iss: 'auth-service',
          aud: 'api-service',
          amr: ['pwd'],
          iat: now,
          exp: now + 3600,
        };

        const token = jwt.sign(payload, 'test-secret');
        const claims = extractClaims(token);

        expect(claims).to.not.be.null;
        expect(claims!.amr).to.deep.equal(['pwd']);
        expect(claims!.iat).to.be.a('string');
        expect(claims!.stepUpPrivileges).to.be.undefined;
      });

      it('should handle token with no step-up privileges', () => {
        const now = Math.floor(Date.now() / 1000);
        const payload = {
          sub: 'user-123',
          amr: ['mfa'],
          iat: now,
          // No stepUpPrivileges field
        };

        const token = jwt.sign(payload, 'test-secret');
        const claims = extractClaims(token);

        expect(claims).to.not.be.null;
        expect(claims!.amr).to.deep.equal(['mfa']);
        expect(claims!.iat).to.be.a('string');
        expect(claims!.stepUpPrivileges).to.be.undefined;
      });

      it('should handle token with empty step-up privileges array', () => {
        const now = Math.floor(Date.now() / 1000);
        const payload = {
          sub: 'user-123',
          amr: ['mfa'],
          iat: now,
          stepUpPrivileges: [],
        };

        const token = jwt.sign(payload, 'test-secret');
        const claims = extractClaims(token);

        expect(claims).to.not.be.null;
        expect(claims).to.deep.include({
          amr: ['mfa'],
          stepUpPrivileges: [],
        });
        expect(claims!.iat).to.be.a('string');
      });
    });

    context('type safety', () => {
      it('should return Claims type', () => {
        const payload = {
          amr: ['mfa'],
          iat: Math.floor(Date.now() / 1000),
          stepUpPrivileges: ['set-owner'],
        };

        const token = jwt.sign(payload, 'test-secret');
        const claims = extractClaims(token);

        expect(claims).to.not.be.null;
        expect(claims).to.be.an('object');
        expect(claims!.amr).to.be.an('array');
        expect(claims!.iat).to.be.a('string');
        expect(claims!.stepUpPrivileges).to.be.an('array');
      });

      it('should handle optional fields correctly', () => {
        const payload = {
          sub: 'user-123',
        };

        const token = jwt.sign(payload, 'test-secret');
        const claims = extractClaims(token);

        expect(claims).to.not.be.null;
        expect(claims!.amr).to.be.undefined;
        expect(claims!.stepUpPrivileges).to.be.undefined;
        // JWT library automatically adds iat, so we check it exists
        expect(claims!.iat).to.be.a('string');
      });
    });
  });
});
