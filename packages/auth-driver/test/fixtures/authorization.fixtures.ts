import { User } from '../../src/types';

export interface ClaimsData {
  amr?: string[];
  iat?: string;
  stepUpPrivileges?: string[];
}

export interface UserFixtureOptions {
  id?: string;
  email?: string;
  claims?: ClaimsData;
  admin?: boolean;
  superAdmin?: boolean;
  supplierPrivileges?: { [key: string]: string[] };
  retailerPrivileges?: { [key: string]: string[] };
}

/**
 * Creates a base user object with default values
 */
export const createBaseUser = (options: UserFixtureOptions = {}): User => ({
  id: options.id || 'test-user-id',
  email: options.email || '<EMAIL>',
  emails: [options.email || '<EMAIL>'],
  password: 'test-password',
  firstName: 'Test',
  lastName: 'User',
  name: {
    full: 'Test User',
    firstName: 'Test',
    lastName: 'User',
  },
  created_at: '2023-01-01T00:00:00Z',
  metadata: { type: 'user' },
  admin: options.admin || false,
  superAdmin: options.superAdmin || false,
  context: {
    suppliers: [],
    retailers: [],
    supplierPrivileges: options.supplierPrivileges || {},
    retailerPrivileges: options.retailerPrivileges || {},
    admin: options.admin || false,
    superAdmin: options.superAdmin || false,
  },
  userRoles: [],
  claims: options.claims,
});

/**
 * Creates a user with specific claims for testing step-up authentication
 */
export const createUserWithClaims = (claims: ClaimsData | undefined): User =>
  createBaseUser({ claims });

/**
 * Time utilities for testing
 */
export const TimeUtils = {
  /**
   * Creates a timestamp for the current time
   */
  now: (): string => new Date().toISOString(),

  /**
   * Creates a timestamp for a specific number of minutes ago
   */
  minutesAgo: (minutes: number): string =>
    new Date(Date.now() - 1000 * 60 * minutes).toISOString(),

  /**
   * Creates a timestamp for a specific number of minutes in the future
   */
  minutesFromNow: (minutes: number): string =>
    new Date(Date.now() + 1000 * 60 * minutes).toISOString(),

  /**
   * Creates an invalid date string
   */
  invalid: (): string => 'invalid-date',
};

/**
 * Common claim patterns for testing
 */
export const ClaimPatterns = {
  /**
   * No claims at all
   */
  none: undefined,

  /**
   * MFA completed with current timestamp
   */
  mfaCurrent: {
    amr: ['mfa'],
    iat: TimeUtils.now(),
  },

  /**
   * MFA completed with exact current timestamp for zero leeway testing
   */
  mfaExactCurrent: {
    amr: ['mfa'],
    iat: new Date().toISOString(), // Exact current time
  },

  /**
   * MFA completed with recent timestamp (5 minutes ago)
   */
  mfaRecent: {
    amr: ['mfa'],
    iat: TimeUtils.minutesAgo(5),
  },

  /**
   * MFA completed with old timestamp (20 minutes ago)
   */
  mfaOld: {
    amr: ['mfa'],
    iat: TimeUtils.minutesAgo(20),
  },

  /**
   * MFA completed with invalid timestamp
   */
  mfaInvalidTime: {
    amr: ['mfa'],
    iat: TimeUtils.invalid(),
  },

  /**
   * MFA completed with null timestamp
   */
  mfaNullTime: {
    amr: ['mfa'],
    iat: null as any,
  },

  /**
   * Password authentication (no MFA)
   */
  passwordOnly: {
    amr: ['pwd'],
    iat: TimeUtils.now(),
  },

  /**
   * MFA with set-owner privileges
   */
  mfaWithSetOwner: {
    amr: ['mfa'],
    iat: TimeUtils.now(),
    stepUpPrivileges: ['set-owner'],
  },

  /**
   * MFA with set-permissions privileges
   */
  mfaWithSetPermissions: {
    amr: ['mfa'],
    iat: TimeUtils.now(),
    stepUpPrivileges: ['set-permissions'],
  },

  /**
   * MFA with both set-owner and set-permissions privileges
   */
  mfaWithBothPrivileges: {
    amr: ['mfa'],
    iat: TimeUtils.now(),
    stepUpPrivileges: ['set-owner', 'set-permissions'],
  },

  /**
   * MFA with empty privileges array
   */
  mfaWithEmptyPrivileges: {
    amr: ['mfa'],
    iat: TimeUtils.now(),
    stepUpPrivileges: [],
  },

  /**
   * MFA with privileges as string (invalid format)
   */
  mfaWithStringPrivileges: {
    amr: ['mfa'],
    iat: TimeUtils.now(),
    stepUpPrivileges: 'set-owner' as any,
  },

  /**
   * MFA with amr as string (invalid format)
   */
  mfaWithStringAmr: {
    amr: 'mfa' as any,
    iat: TimeUtils.now(),
  },

  /**
   * MFA with recent timestamp and set-owner privileges
   */
  mfaRecentWithSetOwner: {
    amr: ['mfa'],
    iat: TimeUtils.minutesAgo(5),
    stepUpPrivileges: ['set-owner'],
  },

  /**
   * MFA with old timestamp and set-owner privileges
   */
  mfaOldWithSetOwner: {
    amr: ['mfa'],
    iat: TimeUtils.minutesAgo(20),
    stepUpPrivileges: ['set-owner'],
  },

  /**
   * Password auth with set-owner privileges (should fail MFA check)
   */
  passwordWithSetOwner: {
    amr: ['pwd'],
    iat: TimeUtils.now(),
    stepUpPrivileges: ['set-owner'],
  },
};

/**
 * Common user fixtures for different scenarios
 */
export const UserFixtures = {
  /**
   * Basic user with no claims
   */
  basic: () => createUserWithClaims(ClaimPatterns.none),

  /**
   * User with current MFA
   */
  withCurrentMfa: () => createUserWithClaims(ClaimPatterns.mfaCurrent),

  /**
   * User with exact current MFA for zero leeway testing
   */
  withExactCurrentMfa: () =>
    createUserWithClaims(ClaimPatterns.mfaExactCurrent),

  /**
   * User with recent MFA
   */
  withRecentMfa: () => createUserWithClaims(ClaimPatterns.mfaRecent),

  /**
   * User with old MFA
   */
  withOldMfa: () => createUserWithClaims(ClaimPatterns.mfaOld),

  /**
   * User with MFA and set-owner privileges
   */
  withSetOwnerPrivileges: () =>
    createUserWithClaims(ClaimPatterns.mfaWithSetOwner),

  /**
   * User with MFA and set-permissions privileges
   */
  withSetPermissionsPrivileges: () =>
    createUserWithClaims(ClaimPatterns.mfaWithSetPermissions),

  /**
   * User with MFA and both privileges
   */
  withBothPrivileges: () =>
    createUserWithClaims(ClaimPatterns.mfaWithBothPrivileges),

  /**
   * User with MFA but no privileges
   */
  withEmptyPrivileges: () =>
    createUserWithClaims(ClaimPatterns.mfaWithEmptyPrivileges),

  /**
   * User with invalid MFA format
   */
  withInvalidMfaFormat: () =>
    createUserWithClaims(ClaimPatterns.mfaWithStringAmr),

  /**
   * User with invalid privileges format
   */
  withInvalidPrivilegesFormat: () =>
    createUserWithClaims(ClaimPatterns.mfaWithStringPrivileges),

  /**
   * User with invalid timestamp
   */
  withInvalidTimestamp: () =>
    createUserWithClaims(ClaimPatterns.mfaInvalidTime),

  /**
   * User with null timestamp
   */
  withNullTimestamp: () => createUserWithClaims(ClaimPatterns.mfaNullTime),

  /**
   * User with password auth only
   */
  withPasswordOnly: () => createUserWithClaims(ClaimPatterns.passwordOnly),

  /**
   * User with password auth but set-owner privileges
   */
  withPasswordAndSetOwner: () =>
    createUserWithClaims(ClaimPatterns.passwordWithSetOwner),

  /**
   * Admin user with MFA
   */
  adminWithMfa: () =>
    createBaseUser({
      admin: true,
      claims: ClaimPatterns.mfaCurrent,
    }),

  /**
   * Super admin user with MFA
   */
  superAdminWithMfa: () =>
    createBaseUser({
      superAdmin: true,
      claims: ClaimPatterns.mfaCurrent,
    }),
};

/**
 * Test constants
 */
export const TestConstants = {
  DEFAULT_LEEWAY: 900, // 15 minutes
  SHORT_LEEWAY: 600, // 10 minutes
  ZERO_LEEWAY: 0,
  NEGATIVE_LEEWAY: -1,
  PRIVILEGES: {
    SET_OWNER: 'set-owner',
    SET_PERMISSIONS: 'set-permissions',
  },
  TIME_WINDOWS: {
    RECENT: 5, // 5 minutes ago
    OLD: 20, // 20 minutes ago
  },
};
