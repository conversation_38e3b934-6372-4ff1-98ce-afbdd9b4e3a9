import { TokenResponse } from '../../src/types';
import { randomUUID } from 'crypto';

/**
 * Creates a base token response object with default values
 */
export const createBaseTokenResponse = (
  options: Partial<TokenResponse> = {}
): TokenResponse => ({
  id: randomUUID(),
  token: 'jwt-token-here',
  access_token: 'access-token-here',
  refresh_token: 'refresh-token-here',
  expires_in: 3600,
  token_type: 'Bearer',
  refresh_token_expires_in: 86400,
  ...options,
});

/**
 * Common token patterns
 */
export const TokenPatterns = {
  /**
   * Standard token response after successful MFA verification
   */
  mfaVerified: createBaseTokenResponse(),

  /**
   * Token response with custom expiration times
   */
  shortLived: createBaseTokenResponse({
    expires_in: 300, // 5 minutes
    refresh_token_expires_in: 3600, // 1 hour
  }),

  /**
   * Token response with long expiration times
   */
  longLived: createBaseTokenResponse({
    expires_in: 7200, // 2 hours
    refresh_token_expires_in: 604800, // 1 week
  }),

  /**
   * Token response with custom token type
   */
  customTokenType: createBaseTokenResponse({
    token_type: 'Custom',
  }),

  /**
   * Token response with specific user ID
   */
  forSpecificUser: (userId: string) =>
    createBaseTokenResponse({
      id: userId,
    }),
};

/**
 * Creates a token response for testing MFA verification
 */
export const createMfaVerificationToken = (userId?: string): TokenResponse => {
  if (userId) {
    return TokenPatterns.forSpecificUser(userId);
  }
  return TokenPatterns.mfaVerified;
};
