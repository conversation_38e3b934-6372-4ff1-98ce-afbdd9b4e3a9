# @ordermentum/auth-driver

## 7.3.2

### Patch Changes

- 333b837: Replace jsonwebtoken to jwt-decode

## 7.3.1

### Patch Changes

- edf1879: Add extractClaims helper function

## 7.3.0

### Minor Changes

- c11cece: Added isSteppedUp helper to support checking if users have completedMFA for sensitive actions

## 7.2.1

### Patch Changes

- da18312: Add mfaEnforcedFrom field in Entity field

## 7.2.0

### Minor Changes

- 88caf90: Added config headers on axios request interceptors

## 7.1.0

### Minor Changes

- 9d1af7e: Return correct error data

## 7.0.0

### Major Changes

- 7b07f50: Make privileges object optional

## 6.3.0

### Minor Changes

- 4815280: Added challenge endpoints

## 6.2.0

### Minor Changes

- a57308e: Remove need to specify MFA code when admin is resetting MFA

## 6.1.0

### Minor Changes

- 3e77ce4: Add handling for null user passed into authorization

## 6.0.2

### Patch Changes

- d68eb54: Add a call for updating an entity's privilege preset

## 6.0.1

### Patch Changes

- 66abe4e: Add ipAddress and userAgent to the Entity from auth driver

## 6.0.0

### Patch Changes

- 7f4bdd0: Add axios client options to OAuth client
