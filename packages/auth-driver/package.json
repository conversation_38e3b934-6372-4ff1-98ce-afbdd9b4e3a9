{"name": "@ordermentum/auth-driver", "version": "7.3.2", "description": "A client for ordermentum auth service.", "main": "build/index.js", "scripts": {"prepublish": "yarn build", "build": "yarn run tsc", "lint": "eslint src/** test/**", "spec": "mocha -r ts-node/register 'test/**/*.ts'"}, "repository": {"type": "git", "url": "https://github.com/ordermentum/auth-driver.git"}, "author": "Ordermentum <<EMAIL>>", "license": "ISC", "bugs": {"url": "https://github.com/ordermentum/libs/issues"}, "homepage": "https://github.com/ordermentum/libs#readme", "dependencies": {"@ordermentum/axios-retry": "^0.1.0", "axios": "1.11.0", "http-errors": "^2.0.0", "jwt-decode": "^4.0.0", "pino": "^8.11.0", "qs": "^6.10.5", "simple-oauth2": "^4.2.0", "uuid": "^8.3.2"}, "devDependencies": {"@babel/eslint-parser": "7.28.0", "@istanbuljs/nyc-config-typescript": "^1.0.2", "@types/bunyan": "^1.8.8", "@types/chai": "^4.3.5", "@types/mocha": "^10.0.1", "@types/node": "17.0.21", "@types/qs": "6.14.0", "@types/sinon": "^10.0.14", "@types/standard-http-error": "2.0.4", "@types/uuid": "8.3.4", "bunyan": "^1.8.15", "chai": "4.3.6", "eslint": "^8.57.0", "husky": "7.0.4", "lint-staged": "12.5.0", "mocha": "9.2.2", "mocha-sinon": "2.1.2", "nock": "^13.3.3", "nyc": "^15.1.0", "prettier": "2.8.8", "required_env": "1.0.1", "sinon": "^15.0.4", "ts-node": "10.9.2", "ts-node-dev": "1.1.8", "turbo": "1.13.4", "typescript": "5.1.6"}}