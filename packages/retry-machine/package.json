{"name": "retry-machine", "version": "2.0.0", "description": "Retry Promises", "main": "build/index.js", "types": "build/index.d.ts", "repository": "**************:ordermentum/libs.git", "author": "<PERSON> <john.dagos<PERSON>@gmail.com>", "license": "Apache-2.0", "files": ["build/**/*"], "scripts": {"lint": "eslint src/ test/", "build:coverage": "nyc check-coverage --statements 80 --branches 80 --functions 80 --lines 80", "test": "NODE_ENV=test nyc npm run spec", "report": "./node_modules/.bin/nyc report --reporter=html", "spec": "./node_modules/.bin/_mocha -R spec --require ts-node/register --recursive test/**/*", "build": "tsc", "prepublish": "yarn run build", "reporter": "nyc --reporter=html yarn run test"}, "dependencies": {"null-logger": "^2.0.5"}, "devDependencies": {"@types/chai": "^4.3.3", "@types/mocha": "^9.1.1", "@types/node": "^18.7.15", "@types/sinon": "^10.0.13", "bunyan": "1.8.15", "chai": "^3.5.0", "eslint": "^8.57.0", "istanbul": "^0.4.4", "mocha": "^2.5.3", "sinon": "^19.0.2", "nyc": "^10.3.0", "ts-node": "^10.9.1", "typescript": "5.1.6"}, "nyc": {"extension": [".ts"], "include": ["src/**/*.ts"], "exclude": ["test/**/*", "build/**/*", "node_modules"], "require": ["ts-node/register"], "reporter": ["text", "html"], "sourceMap": true, "instrument": true}, "publishConfig": {"access": "public"}}