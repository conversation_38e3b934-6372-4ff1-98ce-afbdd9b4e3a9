import nullLogger, { <PERSON><PERSON> } from 'null-logger';

export const wait = (ms: number): Promise<void> =>
  new Promise(resolve =>
    // eslint-disable-next-line no-promise-executor-return
    setTimeout(() => {
      resolve();
    }, ms)
  );

export const backoff = (
  delay: number,
  factor: number,
  attempt: number,
  max: number = 30000
): number => {
  const calculatedBackoff = Math.round(delay * factor ** (attempt - 1));
  return Math.min(calculatedBackoff, max);
};

export type Config = {
  max?: number;
  delay?: number;
  factor?: number;
  logger?: Logger;
  maxDelay?: number;
};

export function retry<T = any>(
  {
    max = 3,
    delay = 1000,
    factor = 1,
    logger = nullLogger,
    maxDelay = 30000,
  }: Config = {},
  failure: Function = async () => {}
): (promise: (...args: any[]) => Promise<T>, ...args: any[]) => Promise<T> {
  return async function closure(promise, ...args: any[]): Promise<T> {
    let attempts = max || 3;
    let result;

    /* eslint-disable no-await-in-loop */
    while (attempts > 0) {
      try {
        logger.debug('retry args:', ...args);
        result = await promise(...args);
        break;
      } catch (e) {
        logger.error('retry error executing: ', e);
        attempts -= 1;
        await failure(e, attempts);

        if (attempts <= 0) {
          throw e;
        }

        if (delay > 0) {
          await wait(backoff(delay, factor, max - attempts, maxDelay));
        }
      }
    }

    return result;
  };
}

export default {
  retry,
  wait,
  backoff,
};
