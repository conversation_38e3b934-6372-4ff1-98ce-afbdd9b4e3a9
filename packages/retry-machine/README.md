# Retry Machine

A simple and configurable utility for retrying promises with exponential backoff.

## Installation

```bash
npm install retry-machine
```
or
```bash
yarn add retry-machine
```

## Usage

`retry-machine` provides a flexible way to handle retries in your promises
with support for exponential backoff and optional logging.

```ts
import { retry } from 'retry-machine';

async function run(attempt) {
  console.log(`Attempt ${attempt}`);
  if (Math.random() < 0.7) {
    throw new Error('Random failure');
  }
  return 'Success!';
}

async function failure(error, attemptsRemaining) {
  console.error(e);
}

const runner = retry({ max: 5, delay: 1000, factor: 2, maxDelay: 5000 }, failure);
await runner(run, 1);
```

### Configuration options

- `max`: The maximum number of attempts to retry the function. Default is 3.
- `delay`: The initial delay between attempts, in milliseconds. Default is 1000.
- `factor`: The exponential backoff factor. Default is 1.
- `logger`: Optional logger to log failures and retry attempts.
- `maxDelay`: The maximum delay between attempts, in milliseconds. Default is 30000 (30 seconds).
