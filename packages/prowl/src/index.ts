import helmet from 'helmet';
import referrerPolicy from 'referrer-policy';
import hsts from 'hsts';
import csp from 'helmet-csp';
import cors from 'cors';
import { Application } from 'express';

const TRANSPORT_SECURITY_MAX_AGE = 15768000;

const DEFAULT_SOURCE: string[] = [
  "'self'",
  '*.ordermentum.com',
  '*.ordermentum-sandbox.com',
  '*.intercom.io',
  '*.mixpanel.com',
  'ordermentum.eu2app.churnzero.net',
  'api.segment.io',
  'app.getsentry.com',
  'cdn.mxpnl.com',
  'cdn.ravenjs.com',
  'cdn.segment.com',
  'i0.wp.com',
  'embed.chartio.com',
  'd1tfh33zic1a0u.cloudfront.net',
  'js.intercomcdn.com',
  'ordermentum-res.cloudinary.com',
  'res.cloudinary.com',
  's3-ap-southeast-2.amazonaws.com',
  'sentry.io',
  'appleid.cdn-apple.com',
  'static.ordermentum.com',
  'widget.intercom.io',
  'edge.xero.com',
  'wss://*.intercom.io',
  'www.google-analytics.com',
  'browser.sentry-cdn.com',
  'maps.googleapis.com',
  'maps.gstatic.com',
  'js.stripe.com',
];

const IMAGE_SOURCE: string[] = [
  '*.au.intercom-attachments.com',
  '*.intercom-attachments-1.com',
  '*.intercom-attachments-2.com',
  '*.intercom-attachments-3.com',
  '*.intercom-attachments-4.com',
  '*.intercom-attachments-5.com',
  '*.intercom-attachments-6.com',
  '*.intercom-attachments-7.com',
  '*.intercom-attachments-8.com',
  '*.intercom-attachments-9.com',
  '*.intercom-attachments.eu',
  '*.ordermentum-sandbox.com',
  '*.ordermentum.com',
  'cdn.branch.io',
  'data:',
  'downloads.au.intercomcdn.com',
  'downloads.intercomcdn.com',
  'downloads.intercomcdn.eu',
  'gifs.intercomcdn.com ',
  'http://img.youtube.com/',
  'https://img.youtube.com/',
  'i0.wp.com',
  'js.intercomcdn.com',
  'js.intercomcdn.com/images/',
  'maps.googleapis.com',
  'maps.gstatic.com',
  'messenger-apps.au.intercom.io',
  'messenger-apps.eu.intercom.io',
  'messenger-apps.intercom.io',
  'ordermentum-res.cloudinary.com',
  'res.cloudinary.com',
  'rs.fullstory.com',
  'static.au.intercomassets.com',
  'static.intercomassets.com',
  'static.intercomassets.eu',
  'static.ordermentum.com',
  'uploads.intercomusercontent.com',
  'video-messages.intercomcdn.com',
  'www.facebook.com',
  'www.google-analytics.com',
  'www.gravatar.com',
];

const FONT_SOURCE: string[] = [
  'fonts.googleapis.com',
  'fonts.gstatic.com',
  'use.fontawesome.com',
  'js.intercomcdn.com',
  'fonts.intercomcdn.com',
  'static.ordermentum.com',
  'data:',
];
const SCRIPT_SOURCE: string[] = [
  '*.intercom.io',
  "'unsafe-inline'",
  "'unsafe-eval'", // well why bother using CSP at all if segment just ruins it all
  'js.appboycdn.com',
  'ordermentum.eu2app.churnzero.net',
  '*.mixpanel.com',
  'd24n15hnbwhuhn.cloudfront.net',
  '*.fullstory.com',
  'fullstory.com',
  'cdn.mxpnl.com',
  'app.link',
  'edge.xero.com',
  'cdn.ravenjs.com',
  'static.ordermentum.com',
  'cdn.amplitude.com',
  'browser.sentry-cdn.com',
  'js.hs-analytics.net',
  'd2yyd1h5u9mauk.cloudfront.net',
  'cdn.segment.com',
  'js.intercomcdn.com',
  'wss://*.intercom.io',
  'appleid.cdn-apple.com',
  'connect.facebook.net',
  'www.google-analytics.com',
  'maps.googleapis.com',
  'maps.gstatic.com',
  'js.stripe.com',
];

const FRAME_SOURCE: string[] = [
  'https://*.youtube.com/',
  'http://*.youtube.com/',
  'https://ordermentum.au.looker.com/',
  'https://embed.chartio.com/',
  'https://ordermentum.ghost.io/',
  'http://ordermentum.ghost.io/',
  's-static.ak.facebook.com',
  'static.ak.facebook.com',
  'www.facebook.com',
  'https://*.embed-omniapp.co',
  'https://js.stripe.com',
];

const CONNECT_SOURCE: string[] = [
  '*.au.intercom.io',
  '*.eu.intercom.io',
  '*.ingest.sentry.io',
  '*.intercom.io',
  '*.mixpanel.com',
  'analytics.churnzero.net',
  'api.amplitude.com',
  'api.segment.io',
  'api2.branch.io',
  'app.getsentry.com',
  'appleid.cdn-apple.com',
  'cdn.segment.com',
  'edge.fullstory.com',
  'edge.xero.com',
  'js.intercomcdn.com',
  'maps.googleapis.com',
  'maps.gstatic.com',
  'ordermentum.eu2app.churnzero.net',
  'rs.fullstory.com',
  'sdk.iad-01.braze.com',
  'sdk.iad-06.braze.com',
  'sentry.io',
  'static.ordermentum.com',
  'uploads.au.intercomcdn.com',
  'uploads.intercomcdn.com',
  'uploads.intercomcdn.eu',
  'uploads.intercomusercontent.com',
  'via.intercom.io',
  'ws.pusherapp.com',
  'wss://*.intercom.io',
  'www.google-analytics.com',
];

const CHILD_SOURCE = [
  'intercom-sheets.com',
  'www.intercom-reporting.com',
  'www.youtube.com',
  'player.vimeo.com',
  'fast.wistia.net',
];

const STYLE_SOURCE: string[] = [
  "'unsafe-inline'",
  'fonts.googleapis.com',
  'app.link',
  'use.fontawesome.com',
  'static.ordermentum.com',
  'js.stripe.com',
];

const WORKER_SOURCE: string[] = ['blob:'];

type params = {
  host?: string;
  reportUri?: string;
  wssHost?: string;
  useCors?: boolean;
};

export function prowl(
  app: Application,
  {
    host,
    reportUri = 'https://sentry.io/api/2077323/security/?sentry_key=e408c075523d4bdca38793f7d5191870',
    wssHost,
    useCors,
  }: params = {}
) {
  app.disable('x-powered-by');
  // Browsers recommend against using X-XSS-Protection: 1; mode=block
  app.use(helmet());
  app.use(
    hsts({
      maxAge: TRANSPORT_SECURITY_MAX_AGE,
      includeSubDomains: true,
      preload: true,
    })
  );

  app.use(helmet.frameguard({ action: 'deny' }));
  app.use(referrerPolicy({ policy: 'same-origin' }));

  if (useCors) {
    app.use(
      cors({
        origin:
          process.env.NODE_ENV === 'development'
            ? '*'
            : /\.ordermentum(-sandbox)?\.com$/,
        credentials: true,
        allowedHeaders: [
          'Content-Type',
          'Authorization',
          'user-context',
          'context-entity',
          'context-id',
          'User-Agent',
        ],
      })
    );
  }

  const hosts = [host, wssHost].filter(i => i !== undefined) as string[];

  app.use(
    csp({
      directives: {
        reportUri,
        defaultSrc: [...DEFAULT_SOURCE, ...hosts],
        scriptSrc: [...SCRIPT_SOURCE, host!],
        styleSrc: [...STYLE_SOURCE, host!],
        connectSrc: [...CONNECT_SOURCE, ...hosts],
        fontSrc: [...FONT_SOURCE, host!],
        frameSrc: [...FRAME_SOURCE, host!],
        childSrc: [...CHILD_SOURCE, host!],
        imgSrc: [...IMAGE_SOURCE, host!],
        workerSrc: [...WORKER_SOURCE, host!],
      },
    })
  );
  return app;
}

export default prowl;
