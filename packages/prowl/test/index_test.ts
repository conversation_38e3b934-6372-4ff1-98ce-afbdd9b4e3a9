import express from 'express';
import { expect } from 'chai';
import request from 'supertest';
import { prowl } from '../src';

describe('Prowl', () => {
  let app;
  beforeEach(() => {
    app = express();
    app.use((_req, res, next) => {
      res.header('Crazy', 'You bet');
      next();
    });
  });

  it('must configure middleware', async () => {
    prowl(app, {
      wssHost: 'wss://app.ordermentum.com',
      host: 'https://app.ordermentum.com',
      useCors: true,
    });

    const res = await request(app).get('/');
    expect(res.headers).to.not.equal(null);
  });

  it('must not require wsshost', async () => {
    prowl(app, { host: 'https://app.ordermentum.com' });
  });

  it('must return correct headers', async () => {
    const secureApp = prowl(app, {
      wssHost: 'wss://app.ordermentum.com',
      host: 'https://app.ordermentum.com',
      useCors: true,
    });

    secureApp.get('/', (_req, res) => {
      res.send('Hello World!');
    });

    const res = await request(secureApp).get('/');
    expect(res.status).to.equal(200);
    expect(res.headers.crazy).to.equal('You bet');
    expect(res.headers['x-frame-options']).to.exist;
    expect(res.headers['x-xss-protection']).to.exist;
    expect(res.headers['content-security-policy']).to.exist;
    expect(res.headers['strict-transport-security']).to.exist;
  });
});
