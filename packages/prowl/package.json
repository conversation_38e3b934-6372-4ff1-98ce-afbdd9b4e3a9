{"name": "@ordermentum/prowl", "version": "1.0.0", "description": "Preconfigured Express Security Edit", "main": "build/index.js", "repository": "**************:ordermentum/prowl.git", "author": "<PERSON> <john.dagos<PERSON>@gmail.com>", "license": "MIT", "files": ["build/*"], "scripts": {"lint": "eslint src/** test/**", "build:coverage": "nyc check-coverage --statements 74 --branches 60 --functions 66 --lines 72", "test": "NODE_ENV=test nyc npm run spec", "report": "./node_modules/.bin/nyc report --reporter=html", "spec": "mocha -R spec ./test/test_helper.ts test/*.*", "build": "yarn clean && babel src --out-dir build --extensions \".ts,.tsx\"", "prepublish": "yarn run build", "clean": "rm -rf build", "reporter": "nyc --reporter=html yarn run test", "typecheck": "tsc --noEmit"}, "dependencies": {"cors": "^2.8.5", "helmet": "^4.5.0", "helmet-csp": "^3.2.0", "hsts": "^2.2.0", "referrer-policy": "^1.2.0"}, "peerDependencies": {"express": "5.1.0"}, "devDependencies": {"@babel/cli": "7.28.0", "@babel/core": "7.28.0", "@babel/plugin-proposal-class-properties": "7.18.6", "@babel/plugin-proposal-object-rest-spread": "7.20.7", "@babel/plugin-transform-async-to-generator": "7.27.1", "@babel/polyfill": "7.12.1", "@babel/preset-env": "7.28.0", "@babel/preset-typescript": "7.27.1", "@babel/register": "7.27.1", "@types/cors": "2.8.19", "@types/express": "5.0.3", "@types/mocha": "8.2.3", "@types/qs": "6.14.0", "@types/supertest": "2.0.16", "babel-plugin-istanbul": "6.1.1", "chai": "4.3.7", "cross-env": "6.0.3", "eslint": "^8.57.0", "express": "5.1.0", "mocha": "7.2.0", "nyc": "14.1.1", "sinon": "9.2.4", "supertest": "6.3.3", "typescript": "5.1.6"}}