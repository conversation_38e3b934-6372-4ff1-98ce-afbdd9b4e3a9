{"name": "@ordermentum/cache-machine", "version": "2.3.2", "description": "simple cache built on redis", "main": "build/index.js", "scripts": {"lint": "eslint src/** test/**", "spec": "mocha --exit -r ts-node/register 'test/**/*.ts'", "build": "tsc", "prepublish": "yarn run build"}, "repository": {"type": "git", "url": "git+https://github.com/ordermentum/cache-machine.git"}, "author": "<PERSON>", "license": "Apache-2.0", "bugs": {"url": "https://github.com/ordermentum/cache-machine/issues"}, "homepage": "https://github.com/ordermentum/cache-machine#readme", "keywords": ["cache", "redis"], "peerDependencies": {"ioredis": ">=4.9"}, "devDependencies": {"@types/qs": "6.14.0", "@types/uuid": "9.0.8", "@babel/eslint-parser": "7.28.0", "eslint": "^8.57.0", "nock": "^13.3.3", "prettier": "2.8.8", "required_env": "1.0.1", "@types/bunyan": "^1.8.8", "@types/chai": "^4.3.5", "@types/mocha": "^10.0.1", "@types/node": "17.0.21", "@types/sinon": "^10.0.14", "bunyan": "^1.8.15", "chai": "4.3.6", "uuid": "9.0.1", "husky": "7.0.4", "lint-staged": "12.5.0", "mocha": "9.2.2", "mocha-sinon": "2.1.2", "nyc": "^15.1.0", "pino": "^8.11.0", "sinon": "^15.0.4", "ts-node": "10.9.2", "ts-node-dev": "1.1.8", "turbo": "1.13.4", "typescript": "5.1.6"}}