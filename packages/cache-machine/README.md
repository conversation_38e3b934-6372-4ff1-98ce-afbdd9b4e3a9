# cache-machine

Cache-machine is a lightweight, user-friendly typescript library for Node.js applications using Redis. 

The library harnesses the power of the Redis data store to provide efficient caching mechanisms, rate limiting and providing common Redis operations.

## usage

```javascript
const cache = require('cache-machine')(redis);
await cache.set('my-key', { test: true });
await cache.get('my-key');
await cache.getAndDel('my-key');
const limited = await cache.rateLimit('my-key', 1, 60);
if (limited) console.log('oh no');
```
