import {
  WorkflowDefinition,
  _WorkflowDefinitionWithAutoDiscoveredExportName,
} from './types';
import { validateWorkflowDefinition, validateWorkflowFile } from './validation';

/**
 * Global registry for managing workflow definitions in the Temporal package.
 *
 * The WorkflowRegistry provides a centralized way to register, validate, and manage
 * workflow definitions before they are deployed to workers. It ensures that all
 * workflows are properly validated and prevents duplicate registrations.
 *
 * **Key Features:**
 * - Automatic validation of workflow definitions using Zod schemas
 * - File validation to ensure workflow files exist and have valid exports
 * - Duplicate registration prevention
 * - Utility methods to query registered workflows and queues
 *
 * @example
 * ```typescript
 * // Register a workflow
 * await WorkflowRegistry.register(myWorkflowDefinition);
 *
 * // Check what's registered
 * console.log('Registered workflows:', WorkflowRegistry.list());
 * console.log('Available queues:', WorkflowRegistry.getQueueNames());
 *
 * // Get a specific workflow
 * const workflow = WorkflowRegistry.get('MyWorkflow');
 * if (workflow) {
 *   console.log('Found workflow:', workflow.name);
 * }
 * ```
 */
export class WorkflowRegistry {
  private static workflows = new Map<
    string,
    WorkflowDefinition & { exportName: string }
  >();

  /**
   * Registers a workflow definition in the global registry.
   *
   * This method performs comprehensive validation before registration:
   * 1. Validates the workflow definition structure using Zod schema
   * 2. Validates that the workflow file exists and has a valid default export
   * 3. Prevents duplicate registrations (silently ignores if already registered)
   *
   * @param definition - The workflow definition to register
   *
   * @throws {Error} When workflow definition validation fails
   * @throws {Error} When workflow file validation fails
   *
   * @example
   * ```typescript
   * const myWorkflow: WorkflowDefinition = {
   *   name: 'ProcessPayment',
   *   workflow: processPaymentWorkflow,
   *   queueName: 'payment-processing',
   *   generateWorkflowId: (id) => `payment-${id}`,
   *   signals: {},
   *   queries: {},
   *   updates: {},
   *   path: __filename
   * };
   *
   * await WorkflowRegistry.register(myWorkflow);
   * ```
   */
  static async register(definition: WorkflowDefinition) {
    // Validate the workflow definition before registering
    const validatedDefinition = validateWorkflowDefinition(definition);

    // Validate that the file exists and has a valid WorkflowDefinition export
    const { exportName } = await validateWorkflowFile(validatedDefinition);

    if (this.workflows.has(validatedDefinition.name)) {
      return;
    }

    // Store the workflow definition with the discovered export name
    this.workflows.set(validatedDefinition.name, {
      ...validatedDefinition,
      exportName,
    });
  }

  /**
   * Retrieves a specific workflow definition by name.
   *
   * @param name - The name of the workflow to retrieve
   * @returns The workflow definition if found, undefined otherwise
   *
   * @example
   * ```typescript
   * const workflow = WorkflowRegistry.get('ProcessPayment');
   * if (workflow) {
   *   console.log(`Found workflow: ${workflow.name} on queue: ${workflow.queueName}`);
   * }
   * ```
   */
  static get(
    name: string
  ): _WorkflowDefinitionWithAutoDiscoveredExportName | undefined {
    return this.workflows.get(name);
  }

  /**
   * Returns all registered workflow definitions.
   *
   * @returns Array of all registered workflow definitions
   *
   * @example
   * ```typescript
   * const allWorkflows = WorkflowRegistry.getAll();
   * allWorkflows.forEach(workflow => {
   *   console.log(`Workflow: ${workflow.name}, Queue: ${workflow.queueName}`);
   * });
   * ```
   */
  static getAll(): _WorkflowDefinitionWithAutoDiscoveredExportName[] {
    return Array.from(this.workflows.values());
  }

  /**
   * Returns the names of all registered workflows.
   *
   * @returns Array of workflow names
   *
   * @example
   * ```typescript
   * const workflowNames = WorkflowRegistry.list();
   * console.log('Registered workflows:', workflowNames.join(', '));
   * ```
   */
  static list(): string[] {
    return Array.from(this.workflows.keys());
  }

  /**
   * Returns unique queue names from all registered workflows.
   *
   * This is useful for understanding which task queues need workers
   * and for configuring worker deployment strategies.
   *
   * @returns Array of unique queue names
   *
   * @example
   * ```typescript
   * const queues = WorkflowRegistry.getQueueNames();
   * console.log('Queues that need workers:', queues.join(', '));
   *
   * // Create workers for all registered queues
   * for (const queue of queues) {
   *   await createTemporalWorker({ taskQueue: queue, activities });
   * }
   * ```
   */
  static getQueueNames(): string[] {
    return [...new Set(this.getAll().map(w => w.queueName))];
  }
}
