import assert from 'assert';
import fs from 'fs';
import pino from 'pino';

const logger = pino({ name: 'temporal-config' });

// Determine TLS status based on environment variable
const disableTlsEnv = process.env.TEMPORAL_DISABLE_TLS?.toLowerCase();
// TLS is enabled by default unless explicitly disabled
const enableTls = !(disableTlsEnv === 'true' || disableTlsEnv === '1');

// Mounted on pods, see here: https://github.com/search?q=repo:ordermentum/helm-charts+%22/etc/temporal/tls%22&type=code
const CLIENT_CERT_PATH = '/etc/temporal/tls/tls.crt';
const CLIENT_KEY_PATH = '/etc/temporal/tls/tls.key';

let clientCert: Buffer | undefined;
let clientKey: Buffer | undefined;

if (enableTls) {
  try {
    clientCert = fs.readFileSync(CLIENT_CERT_PATH);
    clientKey = fs.readFileSync(CLIENT_KEY_PATH);
    logger.info('Successfully read client certificate and key for TLS.');
  } catch (error) {
    logger.warn(
      {
        err: error,
      },
      `TLS is enabled, but failed to read client certificate or key from ${CLIENT_CERT_PATH} and ${CLIENT_KEY_PATH}. TLS will effectively be disabled if the connection requires these.`
    );
  }
} else {
  logger.info(
    'TLS is disabled via TEMPORAL_DISABLE_TLS. Skipping certificate loading.'
  );
}

/**
 * Configuration object for Temporal client and worker connections.
 * Contains all necessary connection details and TLS settings.
 *
 * @example
 * ```typescript
 * const config = getTemporalConfig();
 * console.log(`Connecting to: ${config.address}`);
 * console.log(`Namespace: ${config.namespace}`);
 * console.log(`TLS enabled: ${config.enableTls}`);
 * ```
 */
export interface TemporalConfig {
  /** Temporal server address (e.g., 'localhost:7233' or 'namespace.tmprl.cloud:7233') */
  address: string;
  /** Temporal namespace to connect to */
  namespace: string;
  /** Path to client certificate file (when TLS is enabled) */
  clientCertPath?: string;
  /** Path to client key file (when TLS is enabled) */
  clientKeyPath?: string;
  /** Whether TLS is enabled for the connection */
  enableTls: boolean;
}

/**
 * TLS configuration object containing client certificate and key.
 * Used for secure connections to Temporal Cloud or TLS-enabled servers.
 *
 * @example
 * ```typescript
 * const tlsConfig = getTlsConfig();
 * if (tlsConfig) {
 *   console.log('TLS certificates loaded successfully');
 * }
 * ```
 */
export interface TemporalTlsConfig {
  /** Client certificate pair for TLS authentication */
  clientCertPair: {
    /** Client certificate buffer */
    crt: Buffer;
    /** Client private key buffer */
    key: Buffer;
  };
}

/**
 * Gets the Temporal configuration based on environment variables.
 *
 * **Environment Variables:**
 * - `TEMPORAL_NAMESPACE`: Temporal namespace (default: 'default')
 * - `TEMPORAL_DISABLE_TLS`: Set to 'true' or '1' to disable TLS (default: false)
 *
 * **Address Resolution:**
 * - If namespace is 'default': uses 'localhost:7233'
 * - Otherwise: uses '{namespace}.tmprl.cloud:7233'
 *
 * **TLS Requirements:**
 * When TLS is enabled, the function expects certificates at:
 * - `/etc/temporal/tls/tls.crt` (client certificate)
 * - `/etc/temporal/tls/tls.key` (client private key)
 *
 * @returns Configuration object with connection details
 *
 * @throws {AssertionError} When TLS is enabled but certificates are missing
 *
 * @example
 * ```typescript
 * // Basic usage
 * const config = getTemporalConfig();
 *
 * // Use in connection
 * const connection = await Connection.connect({
 *   address: config.address,
 *   tls: config.enableTls ? getTlsConfig() : false
 * });
 * ```
 */
export function getTemporalConfig(): TemporalConfig {
  const namespace = process.env.TEMPORAL_NAMESPACE || 'default';

  const config: TemporalConfig = {
    address:
      namespace && namespace !== 'default'
        ? `${namespace}.tmprl.cloud:7233`
        : 'localhost:7233',
    namespace,
    clientCertPath: enableTls ? CLIENT_CERT_PATH : undefined,
    clientKeyPath: enableTls ? CLIENT_KEY_PATH : undefined,
    enableTls,
  };

  if (config.enableTls)
    assert(
      clientCert && clientKey,
      `TLS is enabled, but client certificate or key is missing. Ensure ${CLIENT_CERT_PATH} and ${CLIENT_KEY_PATH} are accessible or disable TLS if not required.`
    );

  return config;
}

/**
 * Gets the TLS configuration with loaded certificates.
 *
 * This function returns the TLS configuration only when:
 * 1. TLS is enabled (TEMPORAL_DISABLE_TLS is not 'true' or '1')
 * 2. Both client certificate and key are successfully loaded
 *
 * @returns TLS configuration object or undefined if TLS is disabled or certificates are missing
 *
 * @example
 * ```typescript
 * const tlsConfig = getTlsConfig();
 *
 * const connectionOptions = {
 *   address: 'my-namespace.tmprl.cloud:7233',
 *   tls: tlsConfig || false
 * };
 *
 * const connection = await Connection.connect(connectionOptions);
 * ```
 */
export function getTlsConfig(): TemporalTlsConfig | undefined {
  if (!enableTls) {
    logger.info('TLS is disabled. Skipping certificate loading.');
    return undefined;
  }

  if (!clientCert || !clientKey) {
    logger.error(
      'TLS is enabled but certificate or key is missing on TemporalTlsConfig creation.'
    );
    return undefined;
  }

  const tlsConfig: TemporalTlsConfig = {
    clientCertPair: {
      crt: clientCert,
      key: clientKey,
    },
  };

  return tlsConfig;
}
