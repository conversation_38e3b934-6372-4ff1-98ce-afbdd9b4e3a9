import { LogLevel } from '@temporalio/worker';
import { GenericLogger, WorkflowDefinition, WorkflowFunction } from './types';
import { TemporalWorkerManager } from './worker';
import { WorkflowRegistry } from './workflow-registry';
import { getTemporalClient as _getTemporalClient } from './client';

export {
  TemporalWorkerManager,
  createTemporalWorker,
  HealthStatus,
} from './worker';
export * from './types';
export { createTestWorker } from './test-utils';
export {
  toWorkflowError,
  isServerError,
  isClientError,
  isConnectionError,
  isRetryableError,
  isNonRetryableError,
} from './helpers/errors.helpers';

/**
 * This needs to be a property on the barrel file itself
 * because test runner libraries like sinon (https://sinonjs.org/releases/latest/stubs/) have
 * module resolution issues when a file doesn't own a property
 */
export const getTemporalClient = _getTemporalClient;

/**
 * Starts Temporal workers with the provided activities and workflows.
 *
 * This is a convenience function that handles the common workflow of:
 * 1. Registering workflows in the WorkflowRegistry
 * 2. Creating a TemporalWorkerManager
 * 3. Creating workers for all registered workflows
 * 4. Starting all workers in the background
 *
 * Note: Only workflow bundling is supported. Each workflow definition must include
 * a valid 'path' property pointing to its source file.
 *
 * **Important:** Workers are started in the background and the function returns immediately
 * with the manager instance. The workers will continue running until explicitly shut down.
 *
 * @param activities - Object containing activity functions that workers can execute
 * @param workflows - Array of workflow definitions to register and deploy
 * @param options - Configuration options for the workers
 * @param options.logLevel - Temporal logging level (required)
 * @param options.logger - Custom logger instance (required)
 *
 * @returns Promise that resolves to the TemporalWorkerManager instance (workers start in background)
 *
 * @example
 * ```typescript
 * // Deploy workers with automatic bundling
 * const manager = await startTemporalWorkers(activities, workflows, {
 *   logLevel: 'INFO',
 *   logger: myLogger
 * });
 *
 * // Manager is returned immediately, workers run in background
 * console.log('Workers started, manager ready');
 *
 * // Later, shut down when needed
 * await manager.shutdown();
 * ```
 */
export async function startTemporalWorkers(
  activities: object,
  workflows: WorkflowDefinition[],
  options: {
    logLevel: LogLevel;
    logger: GenericLogger;
    /**
     * Prometheus metrics are emitted to the port specified here. Address: 0.0.0.0:${prometheusMetricsPort}
     */
    prometheusMetricsPort?: string;
  }
) {
  const { logger } = options;

  for (const workflow of workflows) {
    try {
      await WorkflowRegistry.register(workflow);
    } catch (error) {
      logger.error(
        `Failed to register workflow '${workflow.name}' for queue '${
          workflow.queueName
        }': ${error instanceof Error ? error.message : String(error)}`
      );
      throw new Error(
        `Workflow registration failed for '${workflow.name}': ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }

  const manager = new TemporalWorkerManager(logger, {
    prometheusMetricsPort: options.prometheusMetricsPort,
  });
  await manager.createWorkers(activities, {
    logLevel: options.logLevel,
  });

  await manager.start();

  return manager;
}

export function defineWorkflow<T extends WorkflowFunction>(
  definition: WorkflowDefinition<T>
): WorkflowDefinition<T> {
  return definition;
}
