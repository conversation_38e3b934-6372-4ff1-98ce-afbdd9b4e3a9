import { z } from 'zod';
import { WorkflowDefinition } from './types';

/**
 * Zod schema for validating WorkflowDefinition objects.
 * Ensures that workflow definitions conform to the expected structure and types.
 *
 * **Validation Rules:**
 * - `name`: Non-empty string
 * - `workflow`: Function that returns a Promise
 * - `queueName`: Non-empty string
 * - `generateWorkflowId`: Function that takes a string and returns a string
 * - `signals`, `queries`, `updates`: Objects (can be empty)
 * - `path`: Non-empty string pointing to workflow file
 *
 * @example
 * ```typescript
 * // Use the schema directly
 * const result = WorkflowDefinitionSchema.safeParse(myWorkflow);
 * if (result.success) {
 *   console.log('Valid workflow:', result.data.name);
 * } else {
 *   console.error('Validation errors:', result.error.errors);
 * }
 * ```
 */
export const WorkflowDefinitionSchema: z.ZodType<WorkflowDefinition> = z.object(
  {
    name: z.string().min(1, 'Workflow name cannot be empty'),
    workflow: z.function().returns(z.promise(z.any())),
    queueName: z.string().min(1, 'Queue name cannot be empty'),
    generateWorkflowId: z
      .function()
      .args(z.union([z.string(), z.array(z.string())]))
      .returns(z.string()),
    signals: z.record(z.string(), z.any()),
    queries: z.record(z.string(), z.any()),
    updates: z.record(z.string(), z.any()),
    path: z.string().min(1, 'Path cannot be empty'),
  }
);

/**
 * Type guard to check if an object is a valid WorkflowDefinition.
 * Returns true if the object passes validation, false otherwise.
 * This function does not throw errors and is safe to use in conditional checks.
 *
 * @param obj - Object to validate
 * @returns True if the object is a valid WorkflowDefinition, false otherwise
 *
 * @example
 * ```typescript
 * if (isValidWorkflowDefinition(someObject)) {
 *   // someObject is now typed as WorkflowDefinition
 *   console.log('Valid workflow:', someObject.name);
 *   await WorkflowRegistry.register(someObject);
 * } else {
 *   console.error('Invalid workflow definition');
 * }
 * ```
 */
export function isValidWorkflowDefinition(
  obj: unknown
): obj is WorkflowDefinition {
  try {
    WorkflowDefinitionSchema.parse(obj);
    return true;
  } catch {
    return false;
  }
}

/**
 * Validates a WorkflowDefinition and returns the validated object.
 * Throws detailed validation errors if the object is invalid.
 * Use this when you want to validate and get detailed error information.
 *
 * @param obj - Object to validate as a WorkflowDefinition
 * @returns The validated WorkflowDefinition object
 *
 * @throws {z.ZodError} When validation fails, with detailed error information
 *
 * @example
 * ```typescript
 * try {
 *   const validWorkflow = validateWorkflowDefinition(someObject);
 *   console.log('Validated workflow:', validWorkflow.name);
 *   await WorkflowRegistry.register(validWorkflow);
 * } catch (error) {
 *   if (error instanceof z.ZodError) {
 *     console.error('Validation errors:');
 *     error.errors.forEach(err => {
 *       console.error(`- ${err.path.join('.')}: ${err.message}`);
 *     });
 *   }
 * }
 * ```
 */
export function validateWorkflowDefinition(obj: unknown): WorkflowDefinition {
  return WorkflowDefinitionSchema.parse(obj);
}

/**
 * Validates that a workflow file exists and has a valid default export.
 * This function performs file system validation to ensure the workflow
 * file can be imported and contains a valid workflow definition.
 *
 * **Validation Steps:**
 * 1. Attempts to dynamically import the file at the specified path
 * 2. Checks that the file has a default export
 * 3. Validates that the default export is a valid WorkflowDefinition
 *
 * **Environment Variable:**
 * Set `SKIP_WORKFLOW_FILE_VALIDATION=true` to skip file validation (useful for testing)
 *
 * @param workflowDefinition - The workflow definition containing the file path to validate
 *
 * @throws {Error} When the file cannot be imported
 * @throws {Error} When the file has no default export
 * @throws {z.ZodError} When the default export is not a valid WorkflowDefinition
 *
 * @example
 * ```typescript
 * const myWorkflow: WorkflowDefinition = {
 *   name: 'ProcessOrder',
 *   workflow: processOrderWorkflow,
 *   queueName: 'orders',
 *   generateWorkflowId: (id) => `order-${id}`,
 *   signals: {},
 *   queries: {},
 *   updates: {},
 *   path: __filename // This file will be validated
 * };
 *
 * try {
 *   await validateWorkflowFile(myWorkflow);
 *   console.log('File validation passed');
 * } catch (error) {
 *   console.error('File validation failed:', error.message);
 * }
 * ```
 */
/**
 * Finds the first WorkflowDefinition export in a module.
 * Scans all named exports and returns the first one that is a valid WorkflowDefinition.
 *
 * @param module - The imported module object
 * @param filePath - File path for error messages
 * @returns Object with the export name and the workflow definition
 */
function findWorkflowDefinitionExport(
  module: Record<string, unknown>,
  filePath: string
): { exportName: string; definition: WorkflowDefinition } {
  const exportNames = Object.keys(module).filter(key => key !== 'default');

  for (const exportName of exportNames) {
    const exportValue = module[exportName];

    // Check if this export is a valid WorkflowDefinition
    if (isValidWorkflowDefinition(exportValue)) {
      return { exportName, definition: exportValue };
    }
  }

  // No valid WorkflowDefinition found
  throw new Error(
    `No WorkflowDefinition export found in ${filePath}. ` +
      `Expected: export const someVariableName: WorkflowDefinition = { ... }`
  );
}

export async function validateWorkflowFile(
  workflowDefinition: WorkflowDefinition
): Promise<{ exportName: string }> {
  const { path: filePath } = workflowDefinition;

  try {
    // Dynamic import to verify the file exists and has a WorkflowDefinition export
    const module = await import(filePath);

    // Find the first WorkflowDefinition export
    const { exportName, definition } = findWorkflowDefinitionExport(
      module,
      filePath
    );

    // Validate that the found definition matches the one we're registering
    if (definition.name !== workflowDefinition.name) {
      throw new Error(
        `Workflow name mismatch in ${filePath}. ` +
          `Expected name: '${workflowDefinition.name}', but found: '${definition.name}' in export '${exportName}'`
      );
    }

    return { exportName };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map(
        err => `${err.path.join('.')}: ${err.message}`
      );
      throw new Error(
        `Invalid workflow definition in ${filePath}:\n${errorMessages.join(
          '\n'
        )}`
      );
    }
    throw error;
  }
}
