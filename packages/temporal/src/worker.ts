import {
  Worker,
  WorkerOptions,
  NativeConnection,
  NativeConnectionOptions,
  Runtime,
  DefaultLogger,
  LogLevel,
  makeTelemetryFilterString,
  bundleWorkflowCode,
  WorkflowBundle,
} from '@temporalio/worker';
import path from 'path';
import { promises as fs } from 'fs';
import pino from 'pino';
import assert from 'assert';
import { getTemporalConfig, getTlsConfig } from './config';
import {
  GenericLogger,
  _WorkflowDefinitionWithAutoDiscoveredExportName,
} from './types';
import { WorkflowRegistry } from './workflow-registry';

/**
 * Health status information for the Temporal worker manager.
 * Used to monitor the overall health of workers and connections.
 *
 * @example
 * ```typescript
 * const manager = new TemporalWorkerManager();
 * const health = manager.getHealthStatus();
 *
 * if (health.state === 'unhealthy') {
 *   console.error('Worker unhealthy:', health.reason);
 * }
 * ```
 */
export interface HealthStatus {
  /** Current health state of the worker manager */
  state: 'healthy' | 'unhealthy';
  /** Reason for unhealthy state (only present when state is 'unhealthy') */
  reason?: string;
}

/**
 * Advanced worker manager for Temporal with health monitoring and lifecycle management.
 *
 * The TemporalWorkerManager provides comprehensive worker management capabilities including:
 * - Automatic workflow bundling from WorkflowRegistry
 * - Health monitoring and status reporting
 * - Graceful shutdown handling
 * - Multi-queue worker deployment
 * - Connection management and cleanup
 *
 * **Key Features:**
 * - Supports only workflow bundling (no workflowsPath option)
 * - Automatic SIGINT/SIGTERM signal handling
 * - Health checks for connection and worker states
 * - Temporary file cleanup
 * - Detailed worker state reporting
 *
 * @example
 * ```typescript
 * // Basic usage
 * const manager = new TemporalWorkerManager();
 * await manager.createWorkers(activities, { queuesToPoll: ['my-queue'] });
 * await manager.start(); // Blocks until shutdown
 *
 * // With custom logger and health monitoring
 * const manager = new TemporalWorkerManager(myLogger);
 * await manager.createWorkers(activities);
 *
 * // Monitor health
 * setInterval(() => {
 *   const health = manager.getHealthStatus();
 *   console.log('Health:', health.state);
 * }, 5000);
 *
 * await manager.start();
 * ```
 */
export class TemporalWorkerManager {
  private workers: Worker[] = [];

  private logger: GenericLogger;

  private connection: NativeConnection | null = null;

  private workflowBundle: WorkflowBundle | null = null;

  private tempWorkflowDir: string | null = null;

  private options?: { prometheusMetricsPort?: string };

  /**
   * Creates a new TemporalWorkerManager instance.
   *
   * @param logger - Optional custom logger. If not provided, creates a default pino logger
   */
  constructor(
    logger?: GenericLogger,
    options?: { prometheusMetricsPort?: string }
  ) {
    this.logger = logger || this.createDefaultLogger();
    this.options = options;
  }

  private createDefaultLogger(): GenericLogger {
    const pinoLogger = pino({ name: 'temporal-worker', level: 'info' });

    const createLogger = (logger: any): GenericLogger => ({
      info: (msg, ...args) => logger.info(msg, ...args),
      warn: (msg, ...args) => logger.warn(msg, ...args),
      error: (msg, ...args) => logger.error(msg, ...args),
      debug: (msg, ...args) => logger.debug(msg, ...args),
      child: (options: object) => createLogger(logger.child(options)),
    });

    return createLogger(pinoLogger);
  }

  /**
   * Initializes the worker manager by setting up Temporal runtime and connection.
   *
   * This method:
   * - Configures Temporal Runtime with logging and telemetry
   * - Establishes connection to Temporal server
   * - Cleans up any existing temporary directories
   *
   * @param logLevel - Temporal logging level (default: 'INFO')
   *
   * @throws {Error} When connection to Temporal server fails
   *
   * @example
   * ```typescript
   * const manager = new TemporalWorkerManager();
   * await manager.initialize('DEBUG'); // Enable debug logging
   * ```
   */
  async initialize(logLevel: LogLevel = 'INFO') {
    const config = getTemporalConfig();
    const tlsConfig = getTlsConfig();

    // Clean up any existing temp directories from previous runs
    await this.cleanupExistingTempDirectories();

    this.logger.info('Setting up Temporal Runtime...');
    Runtime.install({
      logger: new DefaultLogger(logLevel, entry => {
        const context = entry.meta ?? {};
        const { level } = entry;

        if (typeof this.logger[level.toLowerCase()] === 'function') {
          this.logger[level.toLowerCase()](context, entry.message);
        } else {
          this.logger.info(context, entry.message);
        }
      }),
      telemetryOptions: {
        logging: {
          filter: makeTelemetryFilterString({
            core: logLevel,
            other: logLevel,
          }),
        },
        metrics: this.options?.prometheusMetricsPort
          ? {
              prometheus: {
                bindAddress: `0.0.0.0:${this.options.prometheusMetricsPort}`,
              },
            }
          : undefined,
      },
    });

    const connectionOptions: NativeConnectionOptions = {
      address: config.address,
      tls: tlsConfig || false,
    };

    this.logger.info(`Connecting to Temporal at ${config.address}...`);
    this.connection = await NativeConnection.connect(connectionOptions);
    this.logger.info('Connected to Temporal server');
  }

  /**
   * Creates workers for the specified activities and queues.
   *
   * This method:
   * - Initializes the connection if not already done
   * - Creates workflow bundle from registered workflows
   * - Determines which queues to deploy workers for
   * - Creates one worker per task queue
   *
   * **Queue Selection:**
   * - If `queuesToPoll` is provided: creates workers only for those queues
   * - If `queuesToPoll` is empty/undefined: creates workers for all registered workflow queues
   *
   * @param activities - Object containing activity implementations
   * @param options - Configuration options
   * @param options.logLevel - Temporal logging level
   * @param options.queuesToPoll - Specific queues to create workers for
   *
   * @returns Promise that resolves to array of created workers
   *
   * @throws {Error} When workflow bundling fails
   * @throws {Error} When no workflows match the specified queues
   *
   * @example
   * ```typescript
   * const activities = {
   *   processPayment: async (amount: number) => { ... },
   *   sendEmail: async (to: string, subject: string) => { ... }
   * };
   *
   * // Create workers for all registered queues
   * await manager.createWorkers(activities);
   *
   * // Create workers for specific queues only
   * await manager.createWorkers(activities, {
   *   queuesToPoll: ['payment-processing', 'email-sending']
   * });
   * ```
   */
  async createWorkers(
    activities: any,
    options?: {
      logLevel?: LogLevel;
      queuesToPoll?: string[];
    }
  ) {
    if (!this.connection) {
      await this.initialize(options?.logLevel);
    }

    // Always create workflow bundle - bundling is now the only supported method
    try {
      await this.createWorkflowBundle();
    } catch (error) {
      this.logger.error(
        { error },
        'Failed to create workflow bundle. This can happen due to complex import dependencies or file path issues.'
      );

      // Provide helpful debugging information
      const allWorkflows = WorkflowRegistry.getAll();
      this.logger.info(
        `Registered workflows: ${allWorkflows
          .map(w => `${w.name} (${w.path})`)
          .join(', ')}`
      );

      throw new Error(
        'Workflow bundling failed. ' +
          'Ensure all workflow files and their dependencies are accessible and have correct paths. ' +
          `Original error: ${
            error instanceof Error ? error.message : String(error)
          }`
      );
    }

    // Use provided queues to poll or default to all registered queues
    const requestedQueues = options?.queuesToPoll || [];

    // Determine what to deploy
    const { queuesToDeploy, workflowsToDeploy } =
      this.determineDeployment(requestedQueues);

    this.logger.info(`Deploying workflows: ${workflowsToDeploy.join(', ')}`);
    this.logger.info(`On queues: ${queuesToDeploy.join(', ')}`);

    // Create one worker per queue
    for (const queueName of queuesToDeploy) {
      const worker = await this.createSingleWorker(queueName, activities);
      this.workers.push(worker);
    }

    return this.workers;
  }

  private async createSingleWorker(
    taskQueue: string,
    activities: any
  ): Promise<Worker> {
    assert(this.connection, 'Connection is not initialized');
    const config = getTemporalConfig();

    this.logger.info(
      {
        bundled: !!this.workflowBundle,
        activities: !!activities,
        taskQueue,
      },
      'Creating worker...'
    );

    const workerOptions: WorkerOptions = {
      connection: this.connection,
      namespace: config.namespace,
      taskQueue,
      activities,
    };

    // Workflow bundle is guaranteed to be available due to createWorkflowBundle() call
    workerOptions.workflowBundle = this.workflowBundle!;

    if (process.env.TEMPORAL_MAX_WORKFLOWS) {
      workerOptions.maxConcurrentWorkflowTaskExecutions = parseInt(
        process.env.TEMPORAL_MAX_WORKFLOWS,
        10
      );
    }
    if (process.env.TEMPORAL_MAX_ACTIVITIES) {
      workerOptions.maxConcurrentActivityTaskExecutions = parseInt(
        process.env.TEMPORAL_MAX_ACTIVITIES,
        10
      );
    }

    const worker = await Worker.create(workerOptions);

    this.logger.info(`Temporal worker created for task queue: ${taskQueue}`);
    return worker;
  }

  private determineDeployment(requestedQueues: string[]) {
    const allWorkflows = WorkflowRegistry.getAll();
    const allQueues = WorkflowRegistry.getQueueNames();

    // If no specific queues requested, deploy everything
    if (requestedQueues.length === 0) {
      return {
        queuesToDeploy: allQueues,
        workflowsToDeploy: WorkflowRegistry.list(),
      };
    }

    // If specific queues requested, use those and find workflows for those queues
    const workflowsForQueues = allWorkflows
      .filter(w => requestedQueues.includes(w.queueName))
      .map(w => w.name);

    return {
      queuesToDeploy: requestedQueues,
      workflowsToDeploy: workflowsForQueues,
    };
  }

  async start() {
    if (this.workers.length === 0) {
      throw new Error('No workers created. Call createWorkers() first.');
    }

    this.logger.info(`Starting ${this.workers.length} Temporal workers...`);

    // Setup graceful shutdown
    this.setupGracefulShutdown();

    // NOTE:
    // worker.run() is a blocking call that will block the main thread
    // means we cannot start multiple workers in the same process
    // So, we start all workers in the background
    // If any worker fails to start, we fail hard here by quitting the process
    // This way we know we don't have applications running with workers
    // that are not started
    for (const worker of this.workers) {
      worker.run().catch(error => {
        this.logger.error({ error }, 'Worker failed to start');
        this.logger.info('Shutting down process...');
        process.exit(1);
      });
    }
  }

  async shutdown() {
    this.logger.info('Shutting down Temporal workers...');
    await Promise.all(this.workers.map(worker => worker.shutdown()));

    // Clean up temporary workflow directory
    await this.cleanupTempDirectory();

    this.logger.info('All workers shut down');
  }

  private async cleanupTempDirectory() {
    if (!this.tempWorkflowDir) return;
    try {
      // Use fs.rm instead of fs.rmdir for better compatibility
      await fs.rm(this.tempWorkflowDir, { recursive: true, force: true });
      this.logger.debug(
        `Cleaned up temporary workflow directory: ${this.tempWorkflowDir}`
      );
    } catch (error) {
      this.logger.warn(
        { error, tempDir: this.tempWorkflowDir },
        'Failed to clean up temporary workflow directory'
      );
    }
    this.tempWorkflowDir = null;
  }

  private async cleanupExistingTempDirectories() {
    try {
      const cwd = process.cwd();
      const files = await fs.readdir(cwd);

      // Find all directories that start with .temporal-workflows-
      const tempDirs = files.filter(file =>
        file.startsWith('.temporal-workflows-')
      );

      if (tempDirs.length === 0) return;

      this.logger.info(
        `Found ${tempDirs.length} existing temp directories, cleaning up...`
      );

      for (const tempDir of tempDirs) {
        const fullPath = path.join(cwd, tempDir);
        try {
          await fs.rm(fullPath, { recursive: true, force: true });
          this.logger.debug(`Cleaned up existing temp directory: ${fullPath}`);
        } catch (error) {
          this.logger.warn(
            { error, tempDir: fullPath },
            'Failed to clean up existing temp directory'
          );
        }
      }
    } catch (error) {
      this.logger.warn(
        { error },
        'Failed to scan for existing temp directories'
      );
    }
  }

  private setupGracefulShutdown() {
    const handleShutdown = async (signal: string) => {
      this.logger.info(`Received ${signal}, shutting down gracefully...`);
      await this.shutdown();
      process.exit(0);
    };

    process.on('SIGINT', () => handleShutdown('SIGINT'));
    process.on('SIGTERM', () => handleShutdown('SIGTERM'));
  }

  /**
   * Gets the current health status of the worker manager.
   *
   * The health check evaluates:
   * 1. Connection to Temporal server
   * 2. Presence of created workers
   * 3. Individual worker states (checks for FAILED or STOPPED states)
   *
   * @returns Health status object with state and optional reason
   *
   * @example
   * ```typescript
   * const health = manager.getHealthStatus();
   * console.log(`Health: ${health.state}`);
   *
   * if (health.state === 'unhealthy') {
   *   console.error(`Reason: ${health.reason}`);
   *   // Take corrective action
   * }
   * ```
   */
  getHealthStatus(): HealthStatus {
    // Check connection health
    if (!this.connection) {
      return {
        state: 'unhealthy',
        reason: 'No connection to Temporal server',
      };
    }

    // Check if we have workers
    if (this.workers.length === 0) {
      return {
        state: 'unhealthy',
        reason: 'No workers created',
      };
    }

    // Check individual worker states
    const failedWorkers = this.workers.filter(worker => {
      const state = worker.getState();
      return state === 'FAILED' || state === 'STOPPED';
    });

    if (failedWorkers.length > 0) {
      return {
        state: 'unhealthy',
        reason: `${failedWorkers.length} worker(s) failed or stopped`,
      };
    }

    // All checks passed
    return {
      state: 'healthy',
    };
  }

  /**
   * Gets detailed information about individual workers.
   *
   * Returns an array with each worker's index and current Temporal state.
   * Useful for debugging and detailed monitoring.
   *
   * **Possible Worker States:**
   * - INITIALIZED: Worker created but not started
   * - RUNNING: Worker actively polling for tasks
   * - STOPPED: Worker stopped gracefully
   * - FAILED: Worker encountered an error
   *
   * @returns Array of worker details with index and state
   *
   * @example
   * ```typescript
   * const details = manager.getWorkerDetails();
   * details.forEach(worker => {
   *   console.log(`Worker ${worker.index}: ${worker.state}`);
   * });
   *
   * // Check for failed workers
   * const failedWorkers = details.filter(w => w.state === 'FAILED');
   * if (failedWorkers.length > 0) {
   *   console.error(`${failedWorkers.length} workers failed`);
   * }
   * ```
   */
  getWorkerDetails(): Array<{ index: number; state: string }> {
    return this.workers.map((worker, index) => ({
      index,
      state: worker.getState(),
    }));
  }

  getWorkerCount(): number {
    return this.workers.length;
  }

  private async createWorkflowBundle(): Promise<void> {
    if (this.workflowBundle) {
      return;
    }

    this.logger.info('Creating workflow bundle...');

    try {
      // Get workflow definitions from registry
      const allWorkflows = WorkflowRegistry.getAll();

      if (allWorkflows.length === 0) {
        throw new Error(
          'No workflows registered in WorkflowRegistry. ' +
            'You must register workflows using WorkflowRegistry.register() first.'
        );
      }

      // All workflows are guaranteed to have valid paths due to registry validation

      // Use registry-based workflow files
      this.logger.info(
        `Bundling ${allWorkflows.length} workflows from registry`
      );

      const tempDir = await this.createRegistryWorkflowIndex(allWorkflows);
      this.tempWorkflowDir = tempDir;

      this.logger.debug(`Attempting to bundle workflows from: ${tempDir}`);

      // List files in temp directory for debugging
      try {
        const files = await fs.readdir(tempDir);
        this.logger.debug(`Files in temp directory: ${files.join(', ')}`);
      } catch (error) {
        this.logger.warn({ error }, 'Failed to list temp directory contents');
      }

      // Point to the index.ts file specifically, not just the directory
      const indexPath = path.join(tempDir, 'index.ts');
      this.logger.debug(`Using workflow entry point: ${indexPath}`);
      this.workflowBundle = await bundleWorkflowCode({
        workflowsPath: require.resolve(indexPath),
      });

      this.logger.info('Workflow bundle created successfully');
    } catch (error) {
      this.logger.error({ error }, 'Failed to create workflow bundle');
      throw error;
    }
  }

  private async createRegistryWorkflowIndex(
    workflows: _WorkflowDefinitionWithAutoDiscoveredExportName[]
  ): Promise<string> {
    // Create a temporary directory relative to the current working directory (user's project)
    const tempDir = await fs.mkdtemp(
      path.join(process.cwd(), '.temporal-workflows-')
    );

    this.logger.debug(`Creating workflow bundle in temp directory: ${tempDir}`);

    // Create the main index file that imports workflow definitions and exports workflow functions
    const indexPath = path.join(tempDir, 'index.ts');
    const imports: string[] = [];
    const exports: string[] = [];

    for (const workflow of workflows) {
      // All workflows are guaranteed to have valid paths and existing files due to registry validation
      const workflowPath = path.resolve(workflow.path);

      // Create import statement for the workflow definition
      // Convert to relative path from the temp directory to the original file
      const relativePath = path.relative(tempDir, workflowPath);
      const moduleRef = relativePath.replace(/\\/g, '/').replace(/\.ts$/, '');

      // Use the export name that was discovered during registration
      const { exportName } = workflow;

      imports.push(`import { ${exportName} } from '${moduleRef}';`);
      exports.push(`export const ${workflow.name} = ${exportName}.workflow;`);

      this.logger.debug(`Added import: { ${exportName} } from ${moduleRef}`);
    }

    // Create the index file content
    const indexContent = `// Auto-generated workflow index from WorkflowRegistry
// This file imports workflow definitions and exports the workflow functions
${imports.join('\n')}

${exports.join('\n')}
`;

    await fs.writeFile(indexPath, indexContent, 'utf8');

    this.logger.debug(`Created workflow index at: ${indexPath}`);
    this.logger.debug(
      `Added ${imports.length} workflow imports and exports to index`
    );
    this.logger.debug(`Index content:\n${indexContent}`);

    return tempDir;
  }
}

/**
 * Creates a single Temporal worker with the provided configuration.
 * This is a simpler alternative to TemporalWorkerManager for single-worker scenarios.
 * Note: Only workflow bundling is supported - workflows must be registered in WorkflowRegistry.
 *
 * @param options - Configuration options for the worker
 * @param options.taskQueue - Task queue name for the worker to poll
 * @param options.activities - Object containing activity implementations
 * @param options.logLevel - Temporal logging level (optional)
 * @param options.logger - Custom logger instance (optional)
 *
 * @returns Promise that resolves to a Temporal Worker instance
 *
 * @throws {Error} When worker creation fails
 * @throws {Error} When no workflows are registered for the specified queue
 *
 * @example
 * ```typescript
 * // Register workflows first
 * await WorkflowRegistry.register(myWorkflowDefinition);
 *
 * // Create a single worker
 * const worker = await createTemporalWorker({
 *   taskQueue: 'my-queue',
 *   activities: {
 *     processData: async (data: string) => { ... }
 *   },
 *   logLevel: 'INFO'
 * });
 *
 * // Start the worker
 * await worker.run();
 * ```
 */
export async function createTemporalWorker(options: {
  taskQueue: string;
  activities?: object;
  logLevel?: LogLevel;
  logger?: GenericLogger;
  [key: string]: any; // Allow additional WorkerOptions
}): Promise<import('@temporalio/worker').Worker> {
  const manager = new TemporalWorkerManager(options.logger);
  await manager.initialize(options.logLevel);

  const workers = await manager.createWorkers(options.activities || {}, {
    logLevel: options.logLevel,
    queuesToPoll: [options.taskQueue],
  });

  if (workers.length === 0) {
    throw new Error('Failed to create worker');
  }

  return workers[0];
}
