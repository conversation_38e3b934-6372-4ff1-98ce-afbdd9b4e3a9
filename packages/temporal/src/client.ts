import {
  Connection,
  Client,
  ConnectionOptions,
  ClientOptions,
} from '@temporalio/client';
import pino from 'pino';
import { getTemporalConfig, getTlsConfig } from './config';
import { GenericLogger } from './types';

let clientInstance: Client | null = null;

/**
 * Creates or returns a singleton Temporal client instance.
 *
 * This function implements the singleton pattern to ensure only one client connection
 * is established per application instance. The client automatically configures itself
 * based on environment variables and handles TLS connections when enabled.
 *
 * **Configuration:**
 * - Uses `TEMPORAL_NAMESPACE` environment variable for namespace (defaults to 'default')
 * - Automatically derives connection address from namespace or uses localhost:7233
 * - Handles TLS configuration based on `TEMPORAL_DISABLE_TLS` environment variable
 * - Expects TLS certificates at `/etc/temporal/tls/tls.crt` and `/etc/temporal/tls/tls.key` when TLS is enabled
 *
 * **Graceful Shutdown:**
 * The client automatically registers SIGIN<PERSON> and SIGTERM handlers to close the connection gracefully.
 *
 * @param options - Optional configuration for the client
 * @param options.logger - Custom logger instance. If not provided, uses pino with 'temporal-client' name
 * @param options.* - Any additional ClientOptions from @temporalio/client
 *
 * @returns Promise that resolves to the Temporal Client instance
 *
 * @throws {Error} When connection to Temporal server fails
 *
 * @example
 * ```typescript
 * // Basic usage with default configuration
 * const client = await getTemporalClient();
 *
 * // With custom logger
 * const client = await getTemporalClient({
 *   logger: myCustomLogger
 * });
 *
 * // With additional client options
 * const client = await getTemporalClient({
 *   logger: myLogger,
 *   dataConverter: myDataConverter
 * });
 *
 * // Start a workflow
 * const handle = await client.workflow.start('MyWorkflow', {
 *   taskQueue: 'my-queue',
 *   workflowId: 'my-workflow-id',
 *   args: ['arg1', 'arg2']
 * });
 * ```
 *
 * @since 0.1.0
 */
export async function getTemporalClient(
  options?: Partial<ClientOptions> & { logger?: GenericLogger }
): Promise<Client> {
  if (clientInstance) {
    return clientInstance;
  }
  const logger = options?.logger || pino({ name: 'temporal-client' });
  const config = getTemporalConfig();
  const tlsConfig = getTlsConfig();

  const connectionOptions: ConnectionOptions = {
    address: config.address,
    tls: tlsConfig || false,
  };

  try {
    logger.info(
      `Attempting to connect to Temporal at ${config.address} in namespace ${config.namespace}...`
    );
    const connection = await Connection.connect(connectionOptions);
    clientInstance = new Client({
      connection,
      namespace: config.namespace,
      ...options,
    });
    logger.info(`Temporal client connected successfully.`);
    const shutdown = async (connection: Connection) => {
      logger.info('Received SIGINT, closing connection...');
      await connection.close();
    };
    process.on('SIGINT', shutdown);
    process.on('SIGTERM', shutdown);
    return clientInstance;
  } catch (err) {
    logger.error({ err }, 'Failed to connect to Temporal');
    throw err;
  }
}
