import { ApplicationFailure } from '@temporalio/common';
import { Logger } from 'pino';

/**
 * Get the HTTP status code from an error.
 * @param err - The error to get the HTTP status code from.
 * @returns The HTTP status code, or null if the error is not a HTTP error.
 */
function getHttpStatus<E extends Error>(err: E): string | null {
  if ('status' in err) {
    return (err as { status: string }).status.toString();
  }

  if ('code' in err) {
    return (err as { code: string }).code.toString();
  }

  if ('response' in err) {
    const { response } = err as { response: { status: string } };

    if ('status' in response) {
      return response.status.toString();
    }
  }

  return null;
}

/**
 * Check if an error is a server error i.e. the server could not process
 * the request at this time. While this could in some cases indicate the
 * problem is temporary, there are other cases that indicate a more permanent
 * error that would most likely be recoverable after some intervention on
 * the server e.g. bug fix or infrastructure repair
 * @param err - The error to check.
 * @returns True if the error is a server error, false otherwise.
 */
export function isServerError(status: number): boolean {
  return status >= 500;
}

/**
 * Check if an error is a client error i.e. if there is a fundamental problem
 * with the data provided by the client and is unrecoverable without some type of
 * intervention to correct the request payload.
 * @param err - The error to check.
 * @returns True if the error is a client error, false otherwise.
 */
export function isClientError(status: number): boolean {
  return status >= 400 && status <= 499;
}

/**
 * Is this error a network connection error i.e this would indicate that
 * the problem is temporary and could be recoverable in time.
 * @param err
 * @returns
 */
export function isConnectionError(code: string | null): boolean {
  if (code === 'ENOTFOUND') {
    return true;
  }

  return false;
}

/**
 * Is this error retryable i.e. the problem is temporary and could be
 * recoverable in time.
 * @param err
 * @returns
 */
export function isRetryableError<E extends Error>(err: E | null): boolean {
  // Would be an extremely rare case, but we should handle it
  if (!err) {
    return false;
  }

  const status = getHttpStatus(err);

  // This is an unsupported error object type, or it's not a network error
  if (status === null) {
    return false;
  }

  const statusNumber = parseInt(status, 10);

  if (Number.isNaN(statusNumber) === false) {
    if (isServerError(statusNumber)) {
      // 500 Internal Server Error - Server error, should be retried
      // 502 Bad Gateway - Server received an invalid response from an upstream server
      // 503 Service Unavailable - Server is temporarily overloaded or down
      // 504 Gateway Timeout - Server didn't receive a response from an upstream server in time
      return [500, 502, 503, 504].includes(statusNumber);
    }

    if (isClientError(statusNumber)) {
      // 408 Request Timeout - Client didn't produce a request within the time the server was prepared to wait
      // 429 Too Many Requests - Rate limiting, but should be retried with appropriate backoff
      // 409 Conflict - In some cases, especially with optimistic concurrency control
      return [408, 429, 409].includes(statusNumber);
    }
  }

  if (isConnectionError(status)) {
    return true;
  }

  return false;
}

/**
 * Is this error non-retryable i.e. the problem is permanent and cannot be
 * recovered from.
 * @param err
 * @returns
 */
export function isNonRetryableError<E extends Error>(err: E): boolean {
  return !isRetryableError(err);
}

/**
 * Logs the given error and returns a temporal application failure. The retryability will be
 * determined by the error HTTP status code.
 * @param err - The error to throw.
 * @param message - The message to log.
 * @param logger - The logger to use. Only needs to support the `error` method for compatibility with bunyan etc.
 * @returns
 */
export function toWorkflowError<E extends Error>(
  err: E,
  message: string,
  logger?: Pick<Logger, 'error'>
) {
  logger?.error({ err }, message);

  const retryable = isRetryableError(err);

  return ApplicationFailure.fromError(err, {
    message,
    nonRetryable: !retryable,
  });
}
