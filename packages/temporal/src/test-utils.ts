import {
  Worker,
  WorkerOptions,
  NativeConnection,
  LogLevel,
  bundleWorkflowCode,
  WorkflowBundle,
} from '@temporalio/worker';
import path from 'path';
import { promises as fs } from 'fs';
import { GenericLogger, WorkflowDefinition } from './types';

/**
 * Creates a simple logger for test environments.
 * @private
 */
function createTestLogger(): GenericLogger {
  const noop = () => {};
  const errorLog = (obj: object | string, msg?: string) => {
    if (typeof obj === 'string') {
      // eslint-disable-next-line no-console
      console.error(obj);
    } else {
      // eslint-disable-next-line no-console
      console.error(msg || 'Error', obj);
    }
  };

  return {
    info: noop,
    warn: noop,
    error: errorLog,
    debug: noop,
    child: () => createTestLogger(),
  };
}

/**
 * Cleans up existing test temporary directories from previous runs.
 * Only cleans up directories that are likely from previous runs (older than 1 minute)
 * to avoid interfering with parallel tests.
 * @private
 */
async function cleanupExistingTestTempDirectories(
  logger: GenericLogger
): Promise<void> {
  try {
    const cwd = process.cwd();
    const files = await fs.readdir(cwd);

    // Find all directories that start with .temporal-test-workflows-
    const tempDirs = files.filter(file =>
      file.startsWith('.temporal-test-workflows-')
    );

    if (tempDirs.length === 0) {
      return;
    }

    const now = Date.now();
    const oneMinuteAgo = now - 60 * 1000; // 1 minute ago
    let cleanedCount = 0;

    for (const tempDir of tempDirs) {
      const fullPath = path.join(cwd, tempDir);
      try {
        const stats = await fs.stat(fullPath);

        // Only clean up directories older than 1 minute to avoid interfering with parallel tests
        if (stats.mtime.getTime() < oneMinuteAgo) {
          await fs.rm(fullPath, { recursive: true, force: true });
          cleanedCount++;
          logger.debug(`Cleaned up old test temp directory: ${fullPath}`);
        }
      } catch (error) {
        // Directory might have been deleted by another process, or we can't access it
        // This is fine in parallel test scenarios
        logger.debug(
          { error, tempDir: fullPath },
          'Could not clean up test temp directory (may have been cleaned by another process)'
        );
      }
    }

    if (cleanedCount > 0) {
      logger.debug(`Cleaned up ${cleanedCount} old test temp directories`);
    }
  } catch (error) {
    logger.warn({ error }, 'Failed to scan for existing test temp directories');
  }
}

/**
 * Creates a workflow bundle for testing from a WorkflowDefinition.
 * @private
 */
async function createTestWorkflowBundle(
  workflowDefinition: WorkflowDefinition,
  logger: GenericLogger
): Promise<WorkflowBundle> {
  logger.debug(`Creating test workflow bundle for: ${workflowDefinition.name}`);

  // Clean up any old test temp directories from previous runs (only old ones to avoid parallel test conflicts)
  await cleanupExistingTestTempDirectories(logger);

  // Create a temporary directory for the test workflow bundle with process ID for uniqueness
  const tempDir = await fs.mkdtemp(
    path.join(process.cwd(), `.temporal-test-workflows-${process.pid}-`)
  );

  try {
    // Create the main index file that imports the workflow definition and exports the workflow function
    const indexPath = path.join(tempDir, 'index.ts');

    // Create import statement for the workflow definition
    const workflowPath = path.resolve(workflowDefinition.path);
    const relativePath = path.relative(tempDir, workflowPath);
    const moduleRef = relativePath.replace(/\\/g, '/').replace(/\.ts$/, '');

    // Create the index file content that extracts the workflow function
    const indexContent = `// Auto-generated test workflow index
// This file imports the workflow definition and exports the workflow function
import workflowDefinition from '${moduleRef}';

export const ${workflowDefinition.name} = workflowDefinition.workflow;
`;

    await fs.writeFile(indexPath, indexContent, 'utf8');

    logger.debug(`Created test workflow index at: ${indexPath}`);

    // Bundle the workflows
    const workflowBundle = await bundleWorkflowCode({
      workflowsPath: require.resolve(indexPath),
    });

    // Clean up the temporary directory after successful bundle creation
    // We can safely remove it since the bundle has been created and stored in memory
    await fs.rm(tempDir, { recursive: true, force: true });

    logger.debug('Test workflow bundle created successfully');

    return workflowBundle;
  } catch (error) {
    // Clean up on error
    try {
      await fs.rm(tempDir, { recursive: true, force: true });
    } catch (cleanupError) {
      logger.warn(
        { error: cleanupError },
        'Failed to clean up temp directory after error'
      );
    }
    throw error;
  }
}

/**
 * Creates a Temporal worker for testing that works with TestWorkflowEnvironment.
 * This function provides a bridge between WorkflowDefinitions and standard Temporal testing.
 *
 * **Key Features:**
 * - Works directly with TestWorkflowEnvironment.nativeConnection
 * - Uses workflow bundling to extract workflow functions from WorkflowDefinitions
 * - Accepts WorkflowDefinition for metadata and validation
 * - Automatic cleanup of temporary files
 * - Direct integration with @temporalio/testing
 *
 * @param options - Configuration options for the test worker
 * @param options.connection - Temporal connection (from TestWorkflowEnvironment.nativeConnection)
 * @param options.taskQueue - Task queue name for the worker to poll
 * @param options.workflowDefinition - WorkflowDefinition object for metadata
 * @param options.activities - Object containing activity implementations
 * @param options.logLevel - Temporal logging level (optional)
 * @param options.logger - Custom logger instance (optional)
 *
 * @returns Promise that resolves to a Temporal Worker instance configured for testing
 *
 * @throws {Error} When worker creation fails
 * @throws {Error} When workflow bundling fails
 *
 * @example
 * ```typescript
 * import { TestWorkflowEnvironment } from '@temporalio/testing';
 * import { Worker } from '@temporalio/worker';
 * import { expect } from 'chai';
 * import { createSandbox, SinonSandbox } from 'sinon';
 * import { randomUUID } from 'crypto';
 * import { createTestWorker } from '@ordermentum/temporal';
 * import upfrontPaymentWorkflowDefinition from '../../../../src/ng/workflow/definitions/upfront_payment.workflow';
 * import { upfrontPaymentFactory } from '../fixtures/upfront.fixture';
 * import { UpfrontPayment } from '../../../../src/ng/common/requests/upfront_payments.request';
 *
 * const noop = () => Promise.resolve();
 *
 * describe('UpfrontPaymentWorkflow', () => {
 *   let testEnv: TestWorkflowEnvironment;
 *   let sandbox: SinonSandbox;
 *   let taskQueue: string;
 *   let workflowId: string;
 *   let request: UpfrontPayment;
 *
 *   // Helper function to create workers - DIRECT REPLACEMENT
 *   async function createWorker(activities: object): Promise<Worker> {
 *     return createTestWorker({
 *       connection: testEnv.nativeConnection,
 *       taskQueue,
 *       workflowDefinition: upfrontPaymentWorkflowDefinition,
 *       activities,
 *     });
 *   }
 *
 *   before(async () => {
 *     testEnv = await TestWorkflowEnvironment.createLocal();
 *     sandbox = createSandbox();
 *   });
 *
 *   beforeEach(async () => {
 *     taskQueue = `test-queue-${randomUUID()}`;
 *     workflowId = randomUUID();
 *     request = upfrontPaymentFactory();
 *   });
 *
 *   afterEach(() => {
 *     sandbox.restore();
 *   });
 *
 *   after(async () => {
 *     await testEnv?.teardown();
 *   });
 *
 *   it('should execute workflow', async () => {
 *     const worker = await createWorker({
 *       // Your activity implementations
 *       processPayment: noop,
 *       sendNotification: noop,
 *     });
 *
 *     const client = testEnv.client;
 *     const result = await client.workflow.execute(upfrontPaymentWorkflowDefinition.name, {
 *       taskQueue,
 *       workflowId,
 *       args: [request]
 *     });
 *
 *     await worker.shutdown();
 *     expect(result).to.exist;
 *   });
 * });
 * ```
 */
export async function createTestWorker(options: {
  connection: NativeConnection;
  taskQueue: string;
  workflowDefinition: WorkflowDefinition;
  activities?: object;
  logLevel?: LogLevel;
  logger?: GenericLogger;
}): Promise<Worker> {
  const logger = options.logger || createTestLogger();

  // Validate that the workflow definition has a valid path
  if (!options.workflowDefinition.path) {
    throw new Error(
      'WorkflowDefinition must have a valid path property for testing'
    );
  }

  // Create workflow bundle from the WorkflowDefinition
  const workflowBundle = await createTestWorkflowBundle(
    options.workflowDefinition,
    logger
  );

  // Create worker with the bundled workflow
  const workerOptions: WorkerOptions = {
    connection: options.connection,
    taskQueue: options.taskQueue,
    workflowBundle,
    activities: options.activities || {},
  };

  const worker = await Worker.create(workerOptions);

  logger.info(
    `Test worker created for task queue: ${options.taskQueue} with workflow: ${options.workflowDefinition.name}`
  );

  return worker;
}
