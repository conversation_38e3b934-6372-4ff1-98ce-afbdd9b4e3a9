import { UpdateDefinition } from '@temporalio/client';
import { SignalDefinition, QueryDefinition } from '@temporalio/workflow';

/**
 * Generic workflow function type that represents any Temporal workflow.
 * All workflows must return a Promise to be compatible with Temporal's execution model.
 *
 * @template Args - Array of argument types that the workflow accepts
 * @template Return - The return type of the workflow (will be wrapped in Promise)
 */
export type WorkflowFunction = (...args: any[]) => Promise<any>;

/**
 * Generic logger interface compatible with popular logging libraries.
 * This interface allows the temporal package to work with different logging implementations
 * like pino, winston, or custom loggers.
 *
 * @example
 * ```typescript
 * // Using with pino
 * const logger = pino({ name: 'my-app' });
 * const client = await getTemporalClient({ logger });
 *
 * // Using with winston
 * const logger = winston.createLogger({
 *   format: winston.format.json(),
 *   transports: [new winston.transports.Console()]
 * });
 * const client = await getTemporalClient({ logger });
 * ```
 */
export interface GenericLogger {
  /** Log informational messages */
  info: (obj: object | string, msg?: string) => void;
  /** Log error messages */
  error: (obj: object | string, msg?: string) => void;
  /** Log warning messages */
  warn: (obj: object | string, msg?: string) => void;
  /** Log debug messages */
  debug: (obj: object | string, msg?: string) => void;
  /** Create a child logger with additional context */
  child: (options: object) => GenericLogger;
}

/**
 * Complete workflow definition that includes all necessary metadata for registration and execution.
 * This interface defines the contract for workflow definitions used throughout the temporal package.
 * The types are automatically inferred from the provided workflow function.
 *
 * **Key Requirements:**
 * - `name`: Must be unique across all registered workflows
 * - `workflow`: Must be an async function that returns a Promise
 * - `queueName`: Determines which task queue the workflow will be scheduled on
 * - `path`: Must point to the actual workflow file for bundling (use `__filename`)
 * - `generateWorkflowId`: Function to generate unique workflow IDs
 *
 * @template T - The workflow function type, from which argument and return types are inferred
 *
 * @example
 * ```typescript
 * // Workflow function with specific types
 * async function processOrderWorkflow(orderId: string, priority: number): Promise<{ status: string }> {
 *   // workflow implementation
 *   return { status: 'completed' };
 * }
 *
 * // Types are automatically inferred from the workflow function
 * const myWorkflowDefinition: WorkflowDefinition<typeof processOrderWorkflow> = {
 *   name: 'ProcessOrder',
 *   workflow: processOrderWorkflow, // Types inferred: (orderId: string, priority: number) => Promise<{ status: string }>
 *   queueName: 'order-processing',
 *   generateWorkflowId: (orderId: string) => `order-${orderId}`,
 *   signals: {
 *     cancelOrder: defineSignal<[string]>('cancelOrder'),
 *   },
 *   queries: {
 *     getStatus: defineQuery<string>('getStatus'),
 *   },
 *   updates: {},
 *   path: __filename, // Required for workflow bundling
 * };
 *
 * // Register the workflow
 * await WorkflowRegistry.register(myWorkflowDefinition);
 * ```
 */
export interface WorkflowDefinition<
  T extends WorkflowFunction = WorkflowFunction
> {
  /** Unique name for the workflow. Used for identification and registration. */
  name: string;
  /** The actual workflow function that will be executed by Temporal. */
  workflow: T;
  /** Task queue name where this workflow will be scheduled. */
  queueName: string;
  /** Function to generate unique workflow IDs. Receives an identifier and returns a workflow ID. */
  generateWorkflowId: (id: string) => string;
  /** Signal definitions that this workflow can receive. */
  signals: Record<string, SignalDefinition<any[]>>;
  /** Query definitions that this workflow can handle. */
  queries: Record<string, QueryDefinition<any, any>>;
  /** Update definitions that this workflow can handle. */
  updates: Record<string, UpdateDefinition<any, any[]>>;
  /** File path to the workflow source code. Set this to __filename to enable workflow bundling. */
  path: string;
}

/**
 * @private
 */
export type _WorkflowDefinitionWithAutoDiscoveredExportName =
  WorkflowDefinition & {
    exportName: string;
  };
