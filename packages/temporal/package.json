{"name": "@ordermentum/temporal", "version": "0.4.1", "description": "A Temporal client library for Ordermentum", "main": "build/index.js", "types": "build/index.d.ts", "files": ["build/*"], "license": "MIT", "scripts": {"lint": "yarn eslint 'src/**/*.{ts,js}'", "build:coverage": "nyc check-coverage --statements 70 --branches 60 --functions 60 --lines 70", "test": "NODE_ENV=test nyc npm run spec", "report": "./node_modules/.bin/nyc report --reporter=html", "spec": "mocha 'test/**/*.test.ts'", "spec:runner": "mocha", "build": "yarn clean && yarn tsc", "prepublish": "yarn run build && yarn spec", "clean": "rm -rf build", "reporter": "nyc --reporter=html yarn run test", "typecheck": "tsc --noEmit", "format": "eslint --fix '**/*.{ts,js}'"}, "dependencies": {"@temporalio/common": "1.12.1", "@temporalio/client": "1.12.1", "@temporalio/worker": "1.12.1", "@temporalio/workflow": "1.12.1", "pino": "^8.11.0", "zod": "^3.25.30"}, "peerDependencies": {"@temporalio/testing": "1.12.1", "@temporalio/worker": "1.12.1", "@temporalio/workflow": "1.12.1"}, "devDependencies": {"@temporalio/testing": "1.12.1", "@types/chai": "4.3.20", "@types/chai-as-promised": "^7.1.5", "@types/mocha": "8.2.3", "@types/moment-timezone": "0.5.30", "@types/sinon": "10.0.20", "@types/sinon-chai": "^4.0.0", "@types/uuid": "8.3.4", "chai": "4.5.0", "chai-as-promised": "^7.1.1", "cross-env": "7.0.3", "mocha": "9.2.2", "nyc": "15.1.0", "sinon": "11.1.2", "sinon-chai": "3.7.0", "ts-node": "10.9.2", "typescript": "5.1.3"}}