import assert from 'assert';
import { expect } from 'chai';
import { WorkflowRegistry } from '../src/workflow-registry';
import { WorkflowDefinition } from '../src/types';

describe('WorkflowRegistry', () => {
  beforeEach(() => {
    // Clear the registry before each test
    (WorkflowRegistry as any).workflows.clear();
    // Skip file validation for tests
    process.env.SKIP_WORKFLOW_FILE_VALIDATION = 'true';
  });

  afterEach(() => {
    // Clean up environment variable
    delete process.env.SKIP_WORKFLOW_FILE_VALIDATION;
  });

  const validWorkflowDefinition: WorkflowDefinition = {
    name: 'test-workflow',
    workflow: async () => 'result',
    queueName: 'test-queue',
    generateWorkflowId: (id: string) => `test-${id}`,
    signals: {},
    queries: {},
    updates: {},
    path: __filename, // Use current test file as a valid path
  };

  describe('register', () => {
    it('should register a valid workflow definition', async () => {
      await WorkflowRegistry.register(validWorkflowDefinition);

      expect(WorkflowRegistry.list()).to.include('test-workflow');
      expect(WorkflowRegistry.get('test-workflow')).to.exist;
    });

    it('should validate workflow definition before registering', async () => {
      const invalidWorkflow = { ...validWorkflowDefinition, name: '' };

      try {
        await WorkflowRegistry.register(invalidWorkflow as WorkflowDefinition);
        expect.fail('Should have thrown validation error');
      } catch (error: any) {
        expect(error.message).to.include('Workflow name cannot be empty');
      }
    });

    it('should validate that the file exists', async () => {
      // Enable file validation for this test
      delete process.env.SKIP_WORKFLOW_FILE_VALIDATION;

      const workflowWithInvalidPath = {
        ...validWorkflowDefinition,
        path: './non-existent-file.ts',
      };

      try {
        await WorkflowRegistry.register(workflowWithInvalidPath);
        expect.fail('Should have thrown file validation error');
      } catch (error: any) {
        expect(error.message).to.include('Cannot find module');
      }

      // Restore skip for other tests
      process.env.SKIP_WORKFLOW_FILE_VALIDATION = 'true';
    });

    it('should not register duplicate workflows', async () => {
      await WorkflowRegistry.register(validWorkflowDefinition);
      await WorkflowRegistry.register(validWorkflowDefinition); // Register again

      expect(WorkflowRegistry.list()).to.have.length(1);
    });
  });

  describe('utility methods', () => {
    beforeEach(async () => {
      await WorkflowRegistry.register(validWorkflowDefinition);
    });

    it('should list workflow names', () => {
      expect(WorkflowRegistry.list()).to.deep.equal(['test-workflow']);
    });

    it('should get workflow by name', () => {
      const workflow = WorkflowRegistry.get('test-workflow');
      expect(workflow).to.exist;
      assert(workflow, 'Workflow should exist');
      expect(workflow.name).to.equal('test-workflow');
    });

    it('should return undefined for non-existent workflow', () => {
      expect(WorkflowRegistry.get('non-existent')).to.be.undefined;
    });

    it('should get all workflows', () => {
      const workflows = WorkflowRegistry.getAll();
      expect(workflows).to.have.length(1);
      expect(workflows[0].name).to.equal('test-workflow');
    });

    it('should get queue names', () => {
      const queueNames = WorkflowRegistry.getQueueNames();
      expect(queueNames).to.deep.equal(['test-queue']);
    });
  });
});
