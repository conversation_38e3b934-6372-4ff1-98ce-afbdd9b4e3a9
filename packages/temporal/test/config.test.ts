import fs from 'fs';
import sinon from 'sinon';
import { expect } from './setup';

describe('Temporal Configuration', () => {
  let envBackup: NodeJS.ProcessEnv;
  let readFileSyncStub: sinon.SinonStub;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let configModule: any; // To hold the dynamically imported module
  const mockCertPath = '/etc/temporal/tls/tls.crt';
  const mockKeyPath = '/etc/temporal/tls/tls.key';
  const mockCertData = Buffer.from('mock-cert-data');
  const mockKeyData = Buffer.from('mock-key-data');

  const loadConfigModule = () => {
    // Clear cache and re-require the module to pick up new env vars and mocks
    delete require.cache[require.resolve('../src/config')];
    configModule = require('../src/config'); // eslint-disable-line
  };

  beforeEach(() => {
    envBackup = { ...process.env };
    readFileSyncStub = sinon.stub(fs, 'readFileSync');

    // Default behavior: successfully read certs if paths match
    readFileSyncStub.withArgs(mockCertPath).returns(mockCertData);
    readFileSyncStub.withArgs(mockKeyPath).returns(mockKeyData);
    // Fallback for any other path
    readFileSyncStub.callThrough();

    loadConfigModule();
  });

  afterEach(() => {
    process.env = envBackup;
    sinon.restore();
  });

  describe('getTemporalConfig', () => {
    it('should return default config (TLS enabled) if no conflicting env vars are set', () => {
      delete process.env.TEMPORAL_ADDRESS;
      delete process.env.TEMPORAL_NAMESPACE;
      delete process.env.TEMPORAL_DISABLE_TLS; // TLS enabled by default

      loadConfigModule(); // Re-import module for this specific env state

      const config = configModule.getTemporalConfig();
      expect(config).to.deep.equal({
        address: 'localhost:7233',
        namespace: 'default',
        clientCertPath: mockCertPath,
        clientKeyPath: mockKeyPath,
        enableTls: true,
      });
      expect(readFileSyncStub.calledWith(mockCertPath)).to.be.true;
      expect(readFileSyncStub.calledWith(mockKeyPath)).to.be.true;
    });

    it('should use TEMPORAL_NAMESPACE and derive address, with TLS enabled', () => {
      const mockNamespace = 'my-test-namespace';
      process.env.TEMPORAL_NAMESPACE = mockNamespace;
      delete process.env.TEMPORAL_ADDRESS; // Ensure address is derived
      delete process.env.TEMPORAL_DISABLE_TLS; // TLS enabled

      loadConfigModule();

      const config = configModule.getTemporalConfig();
      expect(config).to.deep.equal({
        address: `${mockNamespace}.tmprl.cloud:7233`,
        namespace: mockNamespace,
        clientCertPath: mockCertPath,
        clientKeyPath: mockKeyPath,
        enableTls: true,
      });
    });

    it('should respect TEMPORAL_DISABLE_TLS=true', () => {
      process.env.TEMPORAL_DISABLE_TLS = 'true';

      readFileSyncStub.resetHistory();

      loadConfigModule();

      const config = configModule.getTemporalConfig();
      expect(config).to.deep.equal({
        address: 'localhost:7233',
        namespace: 'default',
        clientCertPath: undefined,
        clientKeyPath: undefined,
        enableTls: false,
      });
      expect(readFileSyncStub.calledWith(mockCertPath)).to.be.false;
      expect(readFileSyncStub.calledWith(mockKeyPath)).to.be.false;
    });

    it('should throw error if TLS enabled but certs are not readable', () => {
      delete process.env.TEMPORAL_DISABLE_TLS; // TLS enabled
      readFileSyncStub
        .withArgs(mockCertPath)
        .throws(new Error('cannot read cert'));

      try {
        loadConfigModule(); // Module load will log warning
        configModule.getTemporalConfig(); // This will throw assertion
        expect.fail('Expected getTemporalConfig to throw an error');
      } catch (error: any) {
        expect(error.message).to.contain(
          'TLS is enabled, but client certificate or key is missing'
        );
      }
    });
  });

  describe('getTlsConfig', () => {
    it('should return undefined if TEMPORAL_DISABLE_TLS is true', () => {
      process.env.TEMPORAL_DISABLE_TLS = 'true';
      // Reset history for this specific load
      if (readFileSyncStub.resetHistory) {
        readFileSyncStub.resetHistory();
      } else {
        readFileSyncStub.reset();
      }
      loadConfigModule();

      const tlsConfig = configModule.getTlsConfig();
      expect(tlsConfig).to.be.undefined;
    });

    it('should return undefined if TLS is enabled but cert/key reading failed at module load', () => {
      delete process.env.TEMPORAL_DISABLE_TLS; // TLS enabled

      // Reset history and specifically set the throwing behavior for cert path for this test
      // The general .returns(mockKeyData) for key path and .callThrough() from beforeEach should persist.
      readFileSyncStub.resetHistory();
      readFileSyncStub
        .withArgs(mockCertPath)
        .throws(new Error('Simulated file read error for cert'));
      loadConfigModule(); // This load should encounter the error for certPath
      const tlsConfig = configModule.getTlsConfig();
      expect(tlsConfig).to.be.undefined;
    });

    it('should return correct TLS config if TLS is enabled and certs/keys are loaded', () => {
      delete process.env.TEMPORAL_DISABLE_TLS; // TLS enabled
      loadConfigModule();

      const tlsConfig = configModule.getTlsConfig();
      expect(tlsConfig).to.deep.equal({
        clientCertPair: {
          crt: mockCertData,
          key: mockKeyData,
        },
      });
      expect(readFileSyncStub.calledWith(mockCertPath)).to.be.true;
      expect(readFileSyncStub.calledWith(mockKeyPath)).to.be.true;
    });
  });
});
