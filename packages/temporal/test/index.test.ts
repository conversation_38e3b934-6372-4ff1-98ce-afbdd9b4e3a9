import { expect } from './setup';
import * as temporalPackage from '../src/index';

describe('Temporal Package Exports', () => {
  it('should export getTemporalClient function', () => {
    expect(temporalPackage.getTemporalClient).to.be.a('function');
  });

  it('should export createTemporalWorker function', () => {
    expect(temporalPackage.createTemporalWorker).to.be.a('function');
  });

  it('should export TemporalWorkerManager class', () => {
    expect(temporalPackage.TemporalWorkerManager).to.be.a('function');
  });

  it('should export startTemporalWorkers function', () => {
    expect(temporalPackage.startTemporalWorkers).to.be.a('function');
  });
});
