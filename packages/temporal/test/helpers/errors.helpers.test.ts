import { ApplicationFailure } from '@temporalio/common';
import * as sinon from 'sinon';
import { expect } from '../setup';
import {
  isServerError,
  isClientError,
  isConnectionError,
  isRetryableError,
  isNonRetryableError,
  toWorkflowError,
} from '../../src/helpers/errors.helpers';

describe('Error Helpers', () => {
  describe('isServerError', () => {
    it('should return true for errors with status >= 500', () => {
      expect(isServerError(500)).to.be.true;
      expect(isServerError(502)).to.be.true;
      expect(isServerError(503)).to.be.true;
      expect(isServerError(504)).to.be.true;
      expect(isServerError(599)).to.be.true;
    });

    it('should return false for errors with status < 500', () => {
      expect(isServerError(400)).to.be.false;
      expect(isServerError(404)).to.be.false;
      expect(isServerError(499)).to.be.false;
    });

    it('should return true for errors with response.status >= 500', () => {
      expect(isServerError(500)).to.be.true;
    });
  });

  describe('isClientError', () => {
    it('should return true for errors with status 400-499', () => {
      expect(isClientError(400)).to.be.true;
      expect(isClientError(401)).to.be.true;
      expect(isClientError(403)).to.be.true;
      expect(isClientError(404)).to.be.true;
      expect(isClientError(429)).to.be.true;
      expect(isClientError(499)).to.be.true;
      expect(isClientError(403)).to.be.true;
      expect(isClientError(404)).to.be.true;
      expect(isClientError(429)).to.be.true;
      expect(isClientError(499)).to.be.true;
    });

    it('should return false for errors with status < 400 or >= 500', () => {
      expect(isClientError(200)).to.be.false;
      expect(isClientError(399)).to.be.false;
      expect(isClientError(500)).to.be.false;
    });

    it('should return true for errors with response.status 400-499', () => {
      expect(isClientError(404)).to.be.true;
    });
  });

  describe('isConnectionError', () => {
    it('should return true for errors with code ENOTFOUND', () => {
      expect(isConnectionError('ENOTFOUND')).to.be.true;
    });

    it('should return false for errors with other codes', () => {
      expect(isConnectionError('ECONNREFUSED')).to.be.false;
      expect(isConnectionError('TIMEOUT')).to.be.false;
    });

    it('should return false for errors without code', () => {
      expect(isConnectionError(null)).to.be.false;
    });
  });

  describe('isRetryableError', () => {
    it('should return true for retryable server errors (500, 502, 503, 504)', () => {
      const error500 = new Error('Internal Server Error') as Error & {
        status: number;
      };
      error500.status = 500;
      expect(isRetryableError(error500)).to.be.true;

      const error502 = new Error('Bad Gateway') as Error & { status: number };
      error502.status = 502;
      expect(isRetryableError(error502)).to.be.true;

      const error503 = new Error('Service Unavailable') as Error & {
        status: number;
      };
      error503.status = 503;
      expect(isRetryableError(error503)).to.be.true;

      const error504 = new Error('Gateway Timeout') as Error & {
        status: number;
      };
      error504.status = 504;
      expect(isRetryableError(error504)).to.be.true;
    });

    it('should return false for non-retryable server errors', () => {
      const error501 = new Error('Not Implemented') as Error & {
        status: number;
      };
      error501.status = 501;
      expect(isRetryableError(error501)).to.be.false;

      const error505 = new Error('HTTP Version Not Supported') as Error & {
        status: number;
      };
      error505.status = 505;
      expect(isRetryableError(error505)).to.be.false;
    });

    it('should return true for retryable client errors (408, 429, 409)', () => {
      const error408 = new Error('Request Timeout') as Error & {
        status: number;
      };
      error408.status = 408;
      expect(isRetryableError(error408)).to.be.true;

      const error429 = new Error('Too Many Requests') as Error & {
        status: number;
      };
      error429.status = 429;
      expect(isRetryableError(error429)).to.be.true;

      const error409 = new Error('Conflict') as Error & { status: number };
      error409.status = 409;
      expect(isRetryableError(error409)).to.be.true;
    });

    it('should return false for non-retryable client errors', () => {
      const error400 = new Error('Bad Request') as Error & { status: number };
      error400.status = 400;
      expect(isRetryableError(error400)).to.be.false;

      const error401 = new Error('Unauthorized') as Error & { status: number };
      error401.status = 401;
      expect(isRetryableError(error401)).to.be.false;

      const error403 = new Error('Forbidden') as Error & { status: number };
      error403.status = 403;
      expect(isRetryableError(error403)).to.be.false;

      const error404 = new Error('Not Found') as Error & { status: number };
      error404.status = 404;
      expect(isRetryableError(error404)).to.be.false;
    });

    it('should return true for connection errors', () => {
      const error = new Error('Connection failed') as Error & { code: string };
      error.code = 'ENOTFOUND';
      expect(isRetryableError(error)).to.be.true;
    });

    it('should return false for errors without status information', () => {
      const error = new Error('Generic error');
      expect(isRetryableError(error)).to.be.false;
    });

    it('should work with response.status structure', () => {
      const retryableError = new Error('Server Error') as Error & {
        response: { status: number };
      };
      retryableError.response = { status: 503 };
      expect(isRetryableError(retryableError)).to.be.true;

      const nonRetryableError = new Error('Client Error') as Error & {
        response: { status: number };
      };
      nonRetryableError.response = { status: 404 };
      expect(isRetryableError(nonRetryableError)).to.be.false;
    });

    it('should return false for empty errors', () => {
      expect(isRetryableError(null)).to.be.false;
    });
  });

  describe('isNonRetryableError', () => {
    it('should return the opposite of isRetryableError', () => {
      const retryableError = new Error('Server Error') as Error & {
        status: number;
      };
      retryableError.status = 503;
      expect(isNonRetryableError(retryableError)).to.be.false;

      const nonRetryableError = new Error('Client Error') as Error & {
        status: number;
      };
      nonRetryableError.status = 404;
      expect(isNonRetryableError(nonRetryableError)).to.be.true;

      const genericError = new Error('Generic error');
      expect(isNonRetryableError(genericError)).to.be.true;
    });
  });

  describe('toWorkflowError', () => {
    let mockLogger: sinon.SinonStub;
    let applicationFailureStub: sinon.SinonStub;

    beforeEach(() => {
      mockLogger = sinon.stub();
      applicationFailureStub = sinon.stub(ApplicationFailure, 'fromError');
    });

    afterEach(() => {
      sinon.restore();
    });

    it('should log error and create ApplicationFailure for retryable errors', () => {
      const error = new Error('Server Error') as Error & { status: number };
      error.status = 503;
      const message = 'Workflow failed';
      const logger = { error: mockLogger };

      const mockApplicationFailure = { message: 'test failure' };
      applicationFailureStub.returns(mockApplicationFailure);

      const result = toWorkflowError(error, message, logger);

      expect(mockLogger).to.have.been.calledOnceWith({ err: error }, message);
      expect(applicationFailureStub).to.have.been.calledOnceWith(error, {
        message,
        nonRetryable: false,
      });
      expect(result).to.equal(mockApplicationFailure);
    });

    it('should log error and create ApplicationFailure for non-retryable errors', () => {
      const error = new Error('Client Error') as Error & { status: number };
      error.status = 404;
      const message = 'Resource not found';
      const logger = { error: mockLogger };

      const mockApplicationFailure = { message: 'test failure' };
      applicationFailureStub.returns(mockApplicationFailure);

      const result = toWorkflowError(error, message, logger);

      expect(mockLogger).to.have.been.calledOnceWith({ err: error }, message);
      expect(applicationFailureStub).to.have.been.calledOnceWith(error, {
        message,
        nonRetryable: true,
      });
      expect(result).to.equal(mockApplicationFailure);
    });

    it('should work without logger', () => {
      const error = new Error('Generic Error');
      const message = 'Something went wrong';

      const mockApplicationFailure = { message: 'test failure' };
      applicationFailureStub.returns(mockApplicationFailure);

      const result = toWorkflowError(error, message);

      expect(applicationFailureStub).to.have.been.calledOnceWith(error, {
        message,
        nonRetryable: true,
      });
      expect(result).to.equal(mockApplicationFailure);
    });

    it('should handle connection errors as retryable', () => {
      const error = new Error('Connection failed') as Error & { code: string };
      error.code = 'ENOTFOUND';
      const message = 'Connection failed';
      const logger = { error: mockLogger };

      const mockApplicationFailure = { message: 'test failure' };
      applicationFailureStub.returns(mockApplicationFailure);

      const result = toWorkflowError(error, message, logger);

      expect(mockLogger).to.have.been.calledOnceWith({ err: error }, message);
      expect(applicationFailureStub).to.have.been.calledOnceWith(error, {
        message,
        nonRetryable: false,
      });
      expect(result).to.equal(mockApplicationFailure);
    });

    it('should handle errors with response.status structure', () => {
      const error = new Error('HTTP Error') as Error & {
        response: { status: number };
      };
      error.response = { status: 500 };
      const message = 'HTTP request failed';
      const logger = { error: mockLogger };

      const mockApplicationFailure = { message: 'test failure' };
      applicationFailureStub.returns(mockApplicationFailure);

      const result = toWorkflowError(error, message, logger);

      expect(mockLogger).to.have.been.calledOnceWith({ err: error }, message);
      expect(applicationFailureStub).to.have.been.calledOnceWith(error, {
        message,
        nonRetryable: false,
      });
      expect(result).to.equal(mockApplicationFailure);
    });

    it('should handle logger that only has error method', () => {
      const error = new Error('Test Error') as Error & { status: number };
      error.status = 400;
      const message = 'Test message';
      const logger = { error: mockLogger };

      const mockApplicationFailure = { message: 'test failure' };
      applicationFailureStub.returns(mockApplicationFailure);

      toWorkflowError(error, message, logger);

      expect(mockLogger).to.have.been.calledOnceWith({ err: error }, message);
    });
  });
});
