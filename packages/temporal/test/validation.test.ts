import { expect } from 'chai';
import { z } from 'zod';
import {
  WorkflowDefinitionSchema,
  isValidWorkflowDefinition,
  validateWorkflowDefinition,
} from '../src/validation';
import { WorkflowDefinition } from '../src/types';

describe('Workflow Validation', () => {
  const validWorkflowDefinition: WorkflowDefinition = {
    name: 'test-workflow',
    workflow: async () => 'result',
    queueName: 'test-queue',
    generateWorkflowId: (id: string) => `test-${id}`,
    signals: {},
    queries: {},
    updates: {},
    path: '/path/to/workflow.ts',
  };

  describe('WorkflowDefinitionSchema', () => {
    it('should validate a correct workflow definition', () => {
      expect(() =>
        WorkflowDefinitionSchema.parse(validWorkflowDefinition)
      ).to.not.throw();
    });

    it('should reject workflow definition with empty name', () => {
      const invalid = { ...validWorkflowDefinition, name: '' };
      expect(() => WorkflowDefinitionSchema.parse(invalid)).to.throw(
        z.ZodError
      );
    });

    it('should reject workflow definition with empty queue name', () => {
      const invalid = { ...validWorkflowDefinition, queueName: '' };
      expect(() => WorkflowDefinitionSchema.parse(invalid)).to.throw(
        z.ZodError
      );
    });

    it('should reject workflow definition with empty path', () => {
      const invalid = { ...validWorkflowDefinition, path: '' };
      expect(() => WorkflowDefinitionSchema.parse(invalid)).to.throw(
        z.ZodError
      );
    });

    it('should reject workflow definition with missing required fields', () => {
      const invalid = { name: 'test' };
      expect(() => WorkflowDefinitionSchema.parse(invalid)).to.throw(
        z.ZodError
      );
    });
  });

  describe('isValidWorkflowDefinition', () => {
    it('should return true for valid workflow definition', () => {
      expect(isValidWorkflowDefinition(validWorkflowDefinition)).to.be.true;
    });

    it('should return false for invalid workflow definition', () => {
      const invalid = { ...validWorkflowDefinition, name: '' };
      expect(isValidWorkflowDefinition(invalid)).to.be.false;
    });

    it('should return false for non-object input', () => {
      expect(isValidWorkflowDefinition('not an object')).to.be.false;
      expect(isValidWorkflowDefinition(null)).to.be.false;
      expect(isValidWorkflowDefinition(undefined)).to.be.false;
    });
  });

  describe('validateWorkflowDefinition', () => {
    it('should return the workflow definition for valid input', () => {
      const result = validateWorkflowDefinition(validWorkflowDefinition);
      // Check all properties except workflow function (which gets wrapped by Zod)
      expect(result.name).to.equal(validWorkflowDefinition.name);
      expect(result.queueName).to.equal(validWorkflowDefinition.queueName);
      expect(result.path).to.equal(validWorkflowDefinition.path);
      expect(result.signals).to.deep.equal(validWorkflowDefinition.signals);
      expect(result.queries).to.deep.equal(validWorkflowDefinition.queries);
      expect(result.updates).to.deep.equal(validWorkflowDefinition.updates);
      expect(typeof result.workflow).to.equal('function');
      expect(typeof result.generateWorkflowId).to.equal('function');
    });

    it('should throw ZodError for invalid input', () => {
      const invalid = { ...validWorkflowDefinition, name: '' };
      expect(() => validateWorkflowDefinition(invalid)).to.throw(z.ZodError);
    });
  });
});
