import sinon from 'sinon';
import { expect } from './setup';
import * as workerModule from '../src/worker';
import { TemporalWorkerManager } from '../src/worker';

describe('Temporal Worker', () => {
  describe('exports', () => {
    it('should export createTemporalWorker function', () => {
      expect(workerModule.createTemporalWorker).to.be.a('function');
    });
  });

  describe('createTemporalWorker', () => {
    it('should use provided logger', async () => {
      const mockLogger = {
        info: sinon.stub(),
        error: sinon.stub(),
        warn: sinon.stub(),
        debug: sinon.stub(),
        child: sinon.stub().returnsThis(),
      };

      const createWorkerStub = sinon
        .stub(workerModule, 'createTemporalWorker')
        .resolves({} as unknown as import('@temporalio/worker').Worker);

      try {
        await workerModule.createTemporalWorker({
          taskQueue: 'test-queue',
          logger: mockLogger,
        });

        expect(createWorkerStub.calledOnce).to.be.true;
        expect(createWorkerStub.firstCall.args[0]).to.deep.include({
          taskQueue: 'test-queue',
          logger: mockLogger,
        });
      } finally {
        createWorkerStub.restore();
      }
    });

    it('should handle errors and rethrow them', async () => {
      const mockLogger = {
        info: sinon.stub(),
        error: sinon.stub(),
        warn: sinon.stub(),
        debug: sinon.stub(),
        child: sinon.stub().returnsThis(),
      };

      const testError = new Error('Test error');
      const createWorkerStub = sinon
        .stub(workerModule, 'createTemporalWorker')
        .rejects(testError);

      try {
        await expect(
          workerModule.createTemporalWorker({
            taskQueue: 'test-queue',
            logger: mockLogger,
          })
        ).to.be.rejectedWith(testError);

        expect(createWorkerStub.calledOnce).to.be.true;
      } finally {
        createWorkerStub.restore();
      }
    });
  });

  describe('TemporalWorkerManager', () => {
    let manager: TemporalWorkerManager;
    let mockLogger: any;

    beforeEach(() => {
      mockLogger = {
        info: sinon.stub(),
        error: sinon.stub(),
        warn: sinon.stub(),
        debug: sinon.stub(),
        child: sinon.stub().returnsThis(),
      };
      manager = new TemporalWorkerManager(mockLogger);
    });

    describe('constructor', () => {
      it('should initialize correctly', () => {
        const healthStatus = manager.getHealthStatus();
        expect(healthStatus.state).to.equal('unhealthy');
        expect(healthStatus.reason).to.equal(
          'No connection to Temporal server'
        );
      });

      it('should create default logger if none provided', () => {
        const managerWithoutLogger = new TemporalWorkerManager();
        const healthStatus = managerWithoutLogger.getHealthStatus();
        expect(healthStatus.state).to.equal('unhealthy');
      });
    });

    describe('getHealthStatus', () => {
      it('should return unhealthy when no connection', () => {
        const healthStatus = manager.getHealthStatus();

        expect(healthStatus.state).to.equal('unhealthy');
        expect(healthStatus.reason).to.equal(
          'No connection to Temporal server'
        );
      });

      it('should return unhealthy when no workers', () => {
        // Mock connection but no workers
        (manager as any).connection = { mock: 'connection' };

        const healthStatus = manager.getHealthStatus();
        expect(healthStatus.state).to.equal('unhealthy');
        expect(healthStatus.reason).to.equal('No workers created');
      });

      it('should return healthy when connection and workers are good', () => {
        // Mock connection and healthy workers
        (manager as any).connection = { mock: 'connection' };
        (manager as any).workers = [
          { getState: sinon.stub().returns('RUNNING') },
          { getState: sinon.stub().returns('INITIALIZED') },
        ];

        const healthStatus = manager.getHealthStatus();
        expect(healthStatus.state).to.equal('healthy');
        expect(healthStatus.reason).to.be.undefined;
      });

      it('should return unhealthy when workers are failed', () => {
        // Mock connection and failed workers
        (manager as any).connection = { mock: 'connection' };
        (manager as any).workers = [
          { getState: sinon.stub().returns('RUNNING') },
          { getState: sinon.stub().returns('FAILED') },
        ];

        const healthStatus = manager.getHealthStatus();
        expect(healthStatus.state).to.equal('unhealthy');
        expect(healthStatus.reason).to.equal('1 worker(s) failed or stopped');
      });
    });

    describe('shutdown', () => {
      it('should properly shutdown workers and clean up resources', async () => {
        // Set up manager with mock resources
        const mockWorker = { shutdown: sinon.stub().resolves() };

        (manager as any).workers = [mockWorker];

        // Mock cleanupTempDirectory
        const cleanupStub = sinon
          .stub(manager as any, 'cleanupTempDirectory')
          .resolves();

        try {
          await manager.shutdown();

          expect(mockWorker.shutdown.calledOnce).to.be.true;
          expect(cleanupStub.calledOnce).to.be.true;
        } finally {
          cleanupStub.restore();
        }
      });
    });

    describe('worker management', () => {
      it('should track worker count correctly', () => {
        expect(manager.getWorkerCount()).to.equal(0);

        (manager as any).workers = [
          {
            shutdown: sinon.stub(),
            getState: sinon.stub().returns('INITIALIZED'),
          },
          { shutdown: sinon.stub(), getState: sinon.stub().returns('RUNNING') },
        ];

        expect(manager.getWorkerCount()).to.equal(2);
      });

      it('should return detailed worker information', () => {
        const mockWorkers = [
          {
            shutdown: sinon.stub(),
            getState: sinon.stub().returns('INITIALIZED'),
          },
          { shutdown: sinon.stub(), getState: sinon.stub().returns('RUNNING') },
        ];
        (manager as any).workers = mockWorkers;

        const workerDetails = manager.getWorkerDetails();

        expect(workerDetails).to.have.length(2);
        expect(workerDetails[0]).to.deep.equal({
          index: 0,
          state: 'INITIALIZED',
        });
        expect(workerDetails[1]).to.deep.equal({ index: 1, state: 'RUNNING' });
      });
    });
  });
});
