# Temporal Package

A comprehensive Temporal client library for Ordermentum that provides utilities for creating Temporal clients and workers with integrated logging, validation, and graceful shutdown.

## 📚 Documentation

- **[Quick Start Guide](./docs/quick-start.md)** - Get up and running quickly
- **[API Reference](./docs/api-reference.md)** - Complete API documentation
- **[Worker Management](./docs/worker-management.md)** - Advanced worker features

## 🚀 Quick Start

```typescript
import { startTemporalWorkers } from '@ordermentum/temporal';
import * as activities from './activities';
import myWorkflowDefinition from './workflows/my-workflow';

// Start workers with automatic workflow registration
const manager = await startTemporalWorkers(
  activities,
  [myWorkflowDefinition],
  { 
    logLevel: 'INFO',
    logger: myLogger
  }
);
```

## ⚙️ Configuration

Configure the connection details and TLS settings via environment variables:

| Variable | Description | Default |
|----------|-------------|---------|
| `TEMPORAL_NAMESPACE` | Temporal namespace to connect to | `'default'` |
| `TEMPORAL_DISABLE_TLS` | Set to `'true'` or `'1'` to disable TLS | `false` |
| `TEMPORAL_MAX_WORKFLOWS` | Max concurrent workflow executions | Not set |
| `TEMPORAL_MAX_ACTIVITIES` | Max concurrent activity executions | Not set |

### TLS Configuration

When TLS is enabled (default), the package expects client certificates at:
- **Client Certificate:** `/etc/temporal/tls/tls.crt`
- **Client Key:** `/etc/temporal/tls/tls.key`

For local development with Temporal CLI:
```bash
export TEMPORAL_NAMESPACE="default"
export TEMPORAL_DISABLE_TLS="true"
```

## 🏗️ Architecture

### Workflow Bundling

This package **only supports workflow bundling** for consistency and performance. Each workflow definition must include a `path` property:

```typescript
const myWorkflowDefinition: WorkflowDefinition = {
  name: 'MyWorkflow',
  workflow: myWorkflowFunction,
  queueName: 'my-queue',
  path: __filename, // Required for bundling
  // ... other properties
};
```

### Workflow Registry

The `WorkflowRegistry` provides centralized workflow management with automatic validation. It is used internally by `startTemporalWorkers()` and is not exported from the main package:

```typescript
// WorkflowRegistry is used automatically by startTemporalWorkers
import { startTemporalWorkers } from '@ordermentum/temporal';

const manager = await startTemporalWorkers(
  activities,
  [myWorkflowDefinition], // Workflows are automatically registered
  { 
    logLevel: 'INFO',
    logger: myLogger
  }
);
```

### Validation

Built-in Zod-based validation ensures type safety and catches configuration errors early. Validation happens automatically during workflow registration:

```typescript
import { startTemporalWorkers, WorkflowDefinition } from '@ordermentum/temporal';

// Validation happens automatically when workflows are registered
const myWorkflow: WorkflowDefinition = {
  name: 'MyWorkflow',
  workflow: myWorkflowFunction,
  queueName: 'my-queue',
  // ... other properties
};

// This will validate the workflow definition automatically
const manager = await startTemporalWorkers(activities, [myWorkflow], {
  logLevel: 'INFO',
  logger: myLogger
});
```

## 📖 Usage Examples

### Creating a Client

```typescript
import { getTemporalClient } from '@ordermentum/temporal';

// Singleton client with automatic configuration
const client = await getTemporalClient();

// Start a workflow
const handle = await client.workflow.start('MyWorkflow', {
  taskQueue: 'my-queue',
  workflowId: 'unique-id',
  args: ['arg1', 'arg2']
});
```

### Creating Workers

#### Simple Worker

```typescript
import { createTemporalWorker } from '@ordermentum/temporal';

const worker = await createTemporalWorker({
  taskQueue: 'my-queue',
  activities: { processData: async (data) => { ... } }
});

await worker.run();
```

#### Advanced Worker Manager

```typescript
import { TemporalWorkerManager } from '@ordermentum/temporal';

const manager = new TemporalWorkerManager();
await manager.createWorkers(activities);

// Health monitoring
setInterval(() => {
  const health = manager.getHealthStatus();
  console.log('Health:', health.state);
}, 5000);

await manager.start();
```

### Workflow Definition

```typescript
import { WorkflowDefinition } from '@ordermentum/temporal';
import { defineSignal, defineQuery } from '@temporalio/workflow';

export async function processOrderWorkflow(orderId: string): Promise<string> {
  // Your workflow logic
  return `Order ${orderId} processed`;
}

const processOrderDefinition: WorkflowDefinition = {
  name: 'ProcessOrder',
  workflow: processOrderWorkflow,
  queueName: 'order-processing',
  generateWorkflowId: (orderId: string) => `order-${orderId}`,
  signals: {
    cancelOrder: defineSignal<[string]>('cancelOrder'),
  },
  queries: {
    getStatus: defineQuery<string>('getStatus'),
  },
  updates: {},
  path: __filename,
};

export default processOrderDefinition;
```

## 🔧 Features

### ✅ Core Features
- **Singleton Client Pattern** - Automatic connection management
- **Workflow Bundling** - Automatic workflow code bundling
- **Health Monitoring** - Built-in worker health checks
- **Graceful Shutdown** - Automatic SIGINT/SIGTERM handling
- **Validation** - Zod-based workflow definition validation
- **Structured Logging** - Compatible with pino, winston, and custom loggers

### ✅ Worker Management
- **Multi-Queue Support** - Deploy workers across multiple task queues
- **Connection Pooling** - Efficient connection management
- **Temporary File Cleanup** - Automatic cleanup of bundled workflow files

### ✅ Developer Experience
- **TypeScript First** - Full TypeScript support with strict typing
- **Comprehensive Documentation** - JSDoc comments and markdown guides
- **Error Handling** - Detailed error messages and validation feedback
- **Testing Support** - Environment variable overrides for testing

## 🏥 Health Monitoring

Monitor worker health in production:

```typescript
const health = manager.getHealthStatus();

if (health.state === 'unhealthy') {
  console.error('Worker unhealthy:', health.reason);
  // Alert monitoring system
}

// Get detailed worker information
const details = manager.getWorkerDetails();
details.forEach(worker => {
  console.log(`Worker ${worker.index}: ${worker.state}`);
});
```

## 🔍 Troubleshooting

### Common Issues

1. **Connection Failures**
   - Verify `TEMPORAL_NAMESPACE` environment variable
   - Check TLS certificate accessibility
   - Ensure Temporal server is running

2. **Workflow Registration Errors**
   - Validate workflow definition structure
   - Check file paths are correct
   - Ensure default exports exist

3. **Worker Creation Failures**
   - Register workflows before creating workers
   - Verify queue names match registered workflows
   - Check activity implementations

### Debug Mode

Enable debug logging:

```typescript
const manager = new TemporalWorkerManager();
await manager.createWorkers(activities, { logLevel: 'DEBUG' });
```

## 📝 Best Practices

1. **Always register workflows before creating workers**
2. **Use health monitoring for production deployments**
3. **Handle graceful shutdown in your applications**
4. **Validate workflow definitions early in development**
5. **Use structured logging for better debugging**
6. **Set appropriate timeouts for activities**
7. **Use meaningful workflow and activity names**

## 🔗 Related Documentation

- [Temporal Documentation](https://docs.temporal.io/)
- [TypeScript SDK Guide](https://docs.temporal.io/typescript)
- [Workflow Development](https://docs.temporal.io/workflows)
- [Activity Development](https://docs.temporal.io/activities)

## 📄 License

MIT

## Testing

The Temporal package provides testing utilities that work seamlessly with `@temporalio/testing` and `TestWorkflowEnvironment`.

### Using `createTestWorker` with TestWorkflowEnvironment

This approach provides a bridge between WorkflowDefinitions and standard Temporal testing:

```typescript
import { TestWorkflowEnvironment } from '@temporalio/testing';
import { Worker } from '@temporalio/worker';
import { expect } from 'chai';
import { createSandbox, SinonSandbox } from 'sinon';
import { randomUUID } from 'crypto';
import { createTestWorker } from '@ordermentum/temporal';
import upfrontPaymentWorkflowDefinition from '../../../../src/ng/workflow/definitions/upfront_payment.workflow';
import { upfrontPaymentFactory } from '../fixtures/upfront.fixture';
import { UpfrontPayment } from '../../../../src/ng/common/requests/upfront_payments.request';

const noop = () => Promise.resolve();

describe('UpfrontPaymentWorkflow', () => {
  let testEnv: TestWorkflowEnvironment;
  let sandbox: SinonSandbox;
  let taskQueue: string;
  let workflowId: string;
  let request: UpfrontPayment;

  // Helper function to create workers - DIRECT REPLACEMENT for your current function
  async function createWorker(activities: object): Promise<Worker> {
    return createTestWorker({
      connection: testEnv.nativeConnection,
      taskQueue,
      workflowDefinition: upfrontPaymentWorkflowDefinition, // Pass the WorkflowDefinition
      activities,
    });
  }

  before(async () => {
    testEnv = await TestWorkflowEnvironment.createLocal();
    sandbox = createSandbox();
  });

  beforeEach(async () => {
    taskQueue = `test-queue-${randomUUID()}`;
    workflowId = randomUUID();
    request = upfrontPaymentFactory();
  });

  afterEach(() => {
    sandbox.restore();
  });

  after(async () => {
    await testEnv?.teardown();
  });

  it('should execute workflow', async () => {
    const worker = await createWorker({
      // Your activity implementations
      processPayment: noop,
      sendNotification: noop,
    });

    const client = testEnv.client;
    const result = await client.workflow.execute(upfrontPaymentWorkflowDefinition.name, {
      taskQueue,
      workflowId,
      args: [request]
    });

    await worker.shutdown();
    expect(result).to.exist;
  });
});
```

### Key Features

- **TestWorkflowEnvironment integration**: Works directly with `testEnv.nativeConnection`
- **WorkflowDefinition-based**: Uses your WorkflowDefinition for metadata and validation
- **Standard Temporal testing**: Uses the `workflowsPath` approach under the hood
- **No bundling complexity**: Leverages Temporal's built-in workflow loading
- **Drop-in replacement**: Minimal changes to your existing test code


### Benefits

1. **Type Safety**: WorkflowDefinition provides compile-time validation
2. **Consistency**: Same WorkflowDefinition used in production and tests
3. **Metadata Access**: Access to workflow name, queue, and other metadata
4. **Path Management**: No need to manually manage workflow file paths
5. **Future-proof**: Easy to extend with additional testing features

### Import Path

```typescript
// Import test utilities from the dedicated test-utils module
import { createTestWorker } from '@ordermentum/temporal';
```

This approach keeps test utilities separate from production code while providing a seamless bridge between WorkflowDefinitions and standard Temporal testing patterns.

### Testing Activities

Activities can be tested independently using `MockActivityEnvironment` from `@temporalio/testing`. This allows you to test activity logic in isolation with proper dependency injection and mocking.

#### Basic Activity Testing Pattern

```typescript
import sinon, {
  createStubInstance,
  SinonSandbox,
  SinonStubbedInstance,
} from 'sinon';
import { StubbedInstance } from 'ts-sinon';
import { expect } from 'chai';
import { MockActivityEnvironment } from '@temporalio/testing';
import { uuid4 } from '@ordermentum/temporal';
import {
  createFundingActivities,
  HoldFundsInEscrow,
} from './activities/funding.activities';
import { TransactionsRepository } from './repositories/transactions.repository';
import logger from './lib/logger';

describe('Funding Activities', () => {
  let sandbox: SinonSandbox;
  let transactionsRepository: SinonStubbedInstance<TransactionsRepository>;
  let activities: ReturnType<typeof createFundingActivities>;
  let request: HoldFundsInEscrow;

  before(() => {
    sandbox = sinon.createSandbox();
  });

  beforeEach(() => {
    sandbox.restore();
    
    // Create stubbed dependencies
    transactionsRepository = createStubInstance(TransactionsRepository);
    
    // Initialize activities with mocked dependencies
    activities = createFundingActivities(
      transactionsRepository,
      logger
    );
    
    // Setup test data
    request = {
      transactionId: uuid4(),
      amountCents: 10000,
      buyerId: uuid4(),
      sellerId: uuid4(),
    };
  });

  describe('holdFundsInEscrow', () => {
    it('should succeed with valid data', async () => {
      // ARRANGE
      const expectedJournalId = uuid4();
      transactionsRepository.createJournal.resolves({ id: expectedJournalId });

      // ACT
      const env = new MockActivityEnvironment({ attempt: 1 });
      const result = await env.run(activities.holdFundsInEscrow, request);

      // ASSERT
      expect(result).to.be.ok;
      expect(result.journalId).to.equal(expectedJournalId);
      expect(transactionsRepository.createJournal.calledOnce).to.be.true;
    });

    it('should handle errors gracefully', async () => {
      // ARRANGE
      transactionsRepository.createJournal.rejects(new Error('Database error'));

      // ACT & ASSERT
      const env = new MockActivityEnvironment({ attempt: 1 });
      
      try {
        await env.run(activities.holdFundsInEscrow, request);
        expect.fail('Expected error to be thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
        expect(error.message).to.equal('Database error');
      }
    });

    it('should retry on transient failures', async () => {
      // ARRANGE
      transactionsRepository.createJournal
        .onFirstCall().rejects(new Error('Temporary failure'))
        .onSecondCall().resolves({ id: uuid4() });

      // ACT
      const env = new MockActivityEnvironment({ attempt: 2 });
      const result = await env.run(activities.holdFundsInEscrow, request);

      // ASSERT
      expect(result).to.be.ok;
      expect(transactionsRepository.createJournal.calledTwice).to.be.true;
    });
  });
});
```

#### Advanced Activity Testing with Multiple Dependencies

```typescript
import sinon, {
  createStubInstance,
  SinonSandbox,
  SinonStubbedInstance,
} from 'sinon';
import { StubbedInstance } from 'ts-sinon';
import { expect } from 'chai';
import { MockActivityEnvironment } from '@temporalio/testing';
import { uuid4 } from '@temporalio/workflow';

describe('Complex Activity Testing', () => {
  let sandbox: SinonSandbox;
  let userRepository: StubbedInstance<UserRepository>;
  let paymentService: StubbedInstance<PaymentService>;
  let gateway: StubbedInstance<PaymentGateway>;
  let activities: ReturnType<typeof createPaymentActivities>;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    
    // Create factory-based fixtures for consistent test data
    userRepository = userRepositoryFactory({
      users: [
        { id: 'buyer-123', name: 'John Buyer' },
        { id: 'seller-456', name: 'Jane Seller' }
      ],
    });
    
    paymentService = paymentServiceFactory();
    gateway = paymentGatewayFactory();
    
    activities = createPaymentActivities(
      userRepository,
      paymentService,
      gateway,
      logger
    );
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('processPayment', () => {
    it('should process payment successfully', async () => {
      // ARRANGE
      const request = paymentRequestFactory.build({
        buyerId: 'buyer-123',
        sellerId: 'seller-456',
        amountCents: 50000,
      });

      gateway.processPayment.resolves({
        transactionId: 'txn-789',
        status: 'completed',
      });

      // ACT
      const env = new MockActivityEnvironment({ attempt: 1 });
      const result = await env.run(activities.processPayment, request);

      // ASSERT
      expect(result.transactionId).to.equal('txn-789');
      expect(result.status).to.equal('completed');
      
      // Verify all dependencies were called correctly
      expect(userRepository.getUserById.calledWith('buyer-123')).to.be.true;
      expect(userRepository.getUserById.calledWith('seller-456')).to.be.true;
      expect(paymentService.validatePayment.calledOnce).to.be.true;
      expect(gateway.processPayment.calledOnce).to.be.true;
    });

    it('should handle validation failures', async () => {
      // ARRANGE
      const request = paymentRequestFactory.build();
      paymentService.validatePayment.resolves({ isValid: false, errors: ['Invalid amount'] });

      // ACT & ASSERT
      const env = new MockActivityEnvironment({ attempt: 1 });
      
      try {
        await env.run(activities.processPayment, request);
        expect.fail('Expected validation error');
      } catch (error) {
        expect(error.message).to.include('Invalid amount');
      }
    });

    it('should handle missing users', async () => {
      // ARRANGE
      const request = paymentRequestFactory.build({ buyerId: 'nonexistent' });
      userRepository.getUserById.withArgs('nonexistent').resolves(null);

      // ACT & ASSERT
      const env = new MockActivityEnvironment({ attempt: 1 });
      
      try {
        await env.run(activities.processPayment, request);
        expect.fail('Expected user not found error');
      } catch (error) {
        expect(error.message).to.equal('Buyer not found');
      }
    });

    it('should handle gateway timeouts', async () => {
      gateway.processPayment.rejects(new Error('Gateway timeout'));
      
      const env = new MockActivityEnvironment({ attempt: 1 });
      
      await expect(
        env.run(activities.processPayment, request)
      ).to.be.rejectedWith('Gateway timeout');
    });
  });
});
```

This testing approach ensures your activities are thoroughly tested in isolation while maintaining the ability to test complex workflows end-to-end using the workflow testing utilities described above.
