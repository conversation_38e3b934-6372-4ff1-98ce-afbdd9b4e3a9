# @ordermentum/temporal

## 0.4.1

### Patch Changes

- 8c4a67a: Safeguard against null error checks in error helpers

## 0.4.0

### Minor Changes

- ca9f8dd: Added error determination helpers

## 0.3.1

### Patch Changes

- 174a838: Add prometheus metrics support to temporal workers

## 0.3.0

### Minor Changes

- 2ac0751: Remove [queuesToPoll] config for consumers to decide which workflows to run

## 0.2.3

### Patch Changes

- f18e2bc: Minor export change to help with stubbing in consumer services

## 0.2.2

### Patch Changes

- 8613a52: Bolster documentation

## 0.2.1

### Patch Changes

- 7a0c4bb: Add test utilities for creating test workers

## 0.2.0

### Minor Changes

- f1e72ed: Add worker management and other QOL changes

## 0.1.0

### Minor Changes

- 8357347: Temporal client and worker package for Ordermentum

## 0.0.0

### Minor Changes

- 6ef08ac: Add temporal client and worker package
