# Quick Start Guide

This guide will help you get started with the `@ordermentum/temporal` package quickly.

## Installation

```bash
npm install @ordermentum/temporal
# or
yarn add @ordermentum/temporal
```

## Basic Setup

### 1. Environment Configuration

Set up your environment variables:

```bash
# Required
export TEMPORAL_NAMESPACE="your-namespace"

# Optional - TLS is enabled by default
export TEMPORAL_DISABLE_TLS="false"
```

For local development with Temporal CLI:
```bash
export TEMPORAL_NAMESPACE="default"
export TEMPORAL_DISABLE_TLS="true"
```

### 2. Create a Workflow

Create a workflow file (e.g., `src/workflows/process-order.ts`):

```typescript
import { defineSignal, defineQuery } from '@temporalio/workflow';
import { WorkflowDefinition } from '@ordermentum/temporal';

// Define your workflow function
export async function processOrderWorkflow(orderId: string): Promise<string> {
  // Your workflow logic here
  console.log(`Processing order: ${orderId}`);
  
  // Simulate some work
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  return `Order ${orderId} processed successfully`;
}

// Define signals and queries (optional)
export const cancelOrderSignal = defineSignal<[string]>('cancelOrder');
export const getOrderStatusQuery = defineQuery<string>('getOrderStatus');

// Export the workflow definition
const processOrderDefinition: WorkflowDefinition = {
  name: 'ProcessOrder',
  workflow: processOrderWorkflow,
  queueName: 'order-processing',
  generateWorkflowId: (orderId: string) => `order-${orderId}`,
  signals: {
    cancelOrder: cancelOrderSignal,
  },
  queries: {
    getOrderStatus: getOrderStatusQuery,
  },
  updates: {},
  path: __filename, // Important: This enables workflow bundling
};

export default processOrderDefinition;
```

### 3. Create Activities

Create an activities file (e.g., `src/activities/index.ts`):

```typescript
export async function sendEmail(to: string, subject: string, body: string): Promise<void> {
  console.log(`Sending email to ${to}: ${subject}`);
  // Your email sending logic here
}

export async function updateDatabase(orderId: string, status: string): Promise<void> {
  console.log(`Updating order ${orderId} status to ${status}`);
  // Your database update logic here
}

export async function processPayment(amount: number, cardToken: string): Promise<string> {
  console.log(`Processing payment of $${amount}`);
  // Your payment processing logic here
  return 'payment-id-123';
}
```

### 4. Start Workers

Create a worker file (e.g., `src/worker.ts`):

```typescript
import { startTemporalWorkers } from '@ordermentum/temporal';
import * as activities from './activities';
import processOrderDefinition from './workflows/process-order';

async function main() {
  try {
    console.log('Starting Temporal workers...');
    
    const manager = await startTemporalWorkers(
      activities,
      [processOrderDefinition],
      {
        logLevel: 'INFO',
        logger: console, 
      }
    );
    
    console.log('Workers started successfully');
    // The process will run until interrupted (Ctrl+C)
    
  } catch (error) {
    console.error('Failed to start workers:', error);
    process.exit(1);
  }
}

main();
```

### 5. Start Workflows from Client

Create a client file (e.g., `src/client.ts`):

```typescript
import { getTemporalClient } from '@ordermentum/temporal';

async function startOrderProcessing(orderId: string) {
  try {
    const client = await getTemporalClient();
    
    const handle = await client.workflow.start('ProcessOrder', {
      taskQueue: 'order-processing',
      workflowId: `order-${orderId}`,
      args: [orderId],
    });
    
    console.log(`Started workflow: ${handle.workflowId}`);
    
    // Wait for result (optional)
    const result = await handle.result();
    console.log('Workflow result:', result);
    
  } catch (error) {
    console.error('Failed to start workflow:', error);
  }
}

// Start processing an order
startOrderProcessing('12345');
```

## Running the Example

1. **Start Temporal Server** (for local development):
   ```bash
   temporal server start-dev
   ```

2. **Start the Worker**:
   ```bash
   npx ts-node src/worker.ts
   ```

3. **Run the Client** (in another terminal):
   ```bash
   npx ts-node src/client.ts
   ```

## Advanced Usage

### Health Monitoring

```typescript
import { TemporalWorkerManager } from '@ordermentum/temporal';

const manager = new TemporalWorkerManager();
await manager.createWorkers(activities);

// Set up health monitoring
setInterval(() => {
  const health = manager.getHealthStatus();
  console.log(`Health: ${health.state}`);
  
  if (health.state === 'unhealthy') {
    console.error(`Reason: ${health.reason}`);
    // Alert your monitoring system
  }
}, 10000);

await manager.start();
```

### Custom Logger

```typescript
import pino from 'pino';
import { getTemporalClient, TemporalWorkerManager } from '@ordermentum/temporal';

const logger = pino({ 
  name: 'my-app',
  level: 'debug'
});

// Use with client
const client = await getTemporalClient({ logger });

// Use with worker manager
const manager = new TemporalWorkerManager(logger);
```

### Multiple Workflows

```typescript
import { WorkflowRegistry } from '@ordermentum/temporal';
import processOrderDefinition from './workflows/process-order';
import processPaymentDefinition from './workflows/process-payment';
import sendNotificationDefinition from './workflows/send-notification';

// Register multiple workflows
await WorkflowRegistry.register(processOrderDefinition);
await WorkflowRegistry.register(processPaymentDefinition);
await WorkflowRegistry.register(sendNotificationDefinition);

// Start workers for all registered workflows
const manager = new TemporalWorkerManager();
await manager.createWorkers(activities);
await manager.start();
```

### Queue-Specific Workers

```typescript
// Create workers for all registered workflows
const manager = new TemporalWorkerManager();
await manager.createWorkers(activities);
await manager.start();
```

## Common Patterns

### Workflow with Activities

```typescript
import { proxyActivities } from '@temporalio/workflow';
import type * as activities from '../activities';

// Create activity proxy
const { sendEmail, updateDatabase, processPayment } = proxyActivities<typeof activities>({
  startToCloseTimeout: '1 minute',
});

export async function processOrderWorkflow(orderId: string): Promise<string> {
  // Update order status
  await updateDatabase(orderId, 'processing');
  
  // Process payment
  const paymentId = await processPayment(100, 'card-token');
  
  // Send confirmation email
  await sendEmail('<EMAIL>', 'Order Confirmed', `Order ${orderId} confirmed`);
  
  // Final status update
  await updateDatabase(orderId, 'completed');
  
  return `Order ${orderId} processed with payment ${paymentId}`;
}
```

### Error Handling

```typescript
import { ApplicationFailure } from '@temporalio/common';

export async function processOrderWorkflow(orderId: string): Promise<string> {
  try {
    // Your workflow logic
    const result = await processPayment(100, 'invalid-token');
    return result;
  } catch (error) {
    // Handle specific errors
    if (error.message.includes('invalid card')) {
      throw ApplicationFailure.create({
        message: 'Payment failed: Invalid card',
        type: 'PaymentError',
        nonRetryable: true, // Don't retry this error
      });
    }
    
    // Re-throw other errors for retry
    throw error;
  }
}
```

## Troubleshooting

### Common Issues

1. **Connection Failed**
   - Check `TEMPORAL_NAMESPACE` environment variable
   - Verify Temporal server is running
   - Check TLS configuration

2. **Workflow Not Found**
   - Ensure workflows are registered before starting workers
   - Check workflow names match exactly
   - Verify file paths in workflow definitions

3. **Worker Not Processing Tasks**
   - Check queue names match between client and worker
   - Verify activities are properly exported
   - Check worker logs for errors

### Debug Mode

Enable debug logging:

```typescript
const manager = new TemporalWorkerManager();
await manager.createWorkers(activities, { logLevel: 'DEBUG' });
```

### Validation Issues

```typescript
import { validateWorkflowDefinition } from '@ordermentum/temporal';

try {
  validateWorkflowDefinition(myWorkflow);
  console.log('Workflow is valid');
} catch (error) {
  console.error('Validation failed:', error.message);
}
```

## Next Steps

- Read the [API Reference](./api-reference.md) for detailed documentation
- Check out [Worker Management](./worker-management.md) for advanced worker features
- Review the main [README](../README.md) for configuration details 