# Temporal Worker Management

The `TemporalWorkerManager` includes health monitoring capabilities and proper shutdown functionality.

## Features

### Health Check
Monitor the health of your Temporal workers:

```typescript
import { TemporalWorkerManager } from '@ordermentum/temporal';

const manager = new TemporalWorkerManager();
const healthStatus = manager.getHealthStatus();

console.log('Health State:', healthStatus.state); // 'healthy' or 'unhealthy'
if (healthStatus.state === 'unhealthy') {
  console.log('Reason:', healthStatus.reason);
}
```

### Worker Details
Get detailed information about individual workers:

```typescript
const workerDetails = manager.getWorkerDetails();
workerDetails.forEach(worker => {
  console.log(`Worker ${worker.index}: ${worker.state}`);
});
```

### Shutdown

Gracefully shut down all workers:

```typescript
// Manual shutdown - shuts down workers and cleans up resources
await manager.shutdown();
```

**Note**: The `TemporalWorkerManager` automatically sets up graceful shutdown handlers for `SIGINT` and `SIGTERM` signals when you call `start()`. You don't need to set up your own signal handlers unless you have specific cleanup requirements.

## Complete Example

```typescript
// File: activities.ts
function createUserActivities(
  dbRepository : DbRepository,
  restClient: RestClient
) {
  return {
    async getUser: async (userId: string) => {
      return dbRepository.getUser(userId);
    },
    async getUserByEmail: async (email: string) => {
      return dbRepository.getUserByEmail(email);
    },
    async getUserByPhone: async (phone: string) => {
      return dbRepository.getUserByPhone(phone);
    },
    async patchUser: async (userId: string, user: User) => {
      return restClient.patchUser(userId, user);
    }
  };
}

/**
 * Bootstrap your activities with dependencies injected 
 * in the factory method
 */
export const createActivities = (
  dbRepository : DbRepository,
  restClient: RestClient
) => {
  const userActivities = createUserActivities(
    dbRepository,
    restClient
  );

  return {
    ...userActivities,
  };
};
```

```typescript
// File: main.ts
import { startTemporalWorkers } from '@ordermentum/temporal';
import { createActivities } from './activities';

// Define your workflow
const myWorkflowDefinition = {
  name: 'MyWorkflow',
  workflow: myWorkflowFunction,
  queueName: 'my-queue',
  path: './workflows/my-workflow.ts',
  generateWorkflowId: (id: string) => `my-workflow-${id}`,
  signals: {},
  queries: {},
  updates: {}
};

const activities = createActivities(dbRepository, restClient);

async function main() {
  try {
    // Start workers with automatic workflow registration
    const manager = await startTemporalWorkers(activities, [myWorkflowDefinition]);
    
    // Set up health monitoring
    const healthInterval = setInterval(() => {
      const health = manager.getHealthStatus();
      console.log(`Health: ${health.state}`);
      if (health.state === 'unhealthy') {
        console.log(`Reason: ${health.reason}`);
      }
      
      // Optionally log individual worker states
      const details = manager.getWorkerDetails();
      details.forEach(worker => {
        console.log(`  Worker ${worker.index}: ${worker.state}`);
      });
    }, 5000);
    
    // Workers are already started by startTemporalWorkers
    // Note: Graceful shutdown handlers (SIGINT, SIGTERM) are automatically set up
    
  } catch (error) {
    console.error('Worker error:', error);
    process.exit(1);
  }
}

main().catch(console.error);
```

## API Reference

### `getHealthStatus(): HealthStatus`

Returns the current health status of the worker manager.

**Returns:**
```typescript
interface HealthStatus {
  state: 'healthy' | 'unhealthy';
  reason?: string; // Present when state is 'unhealthy'
}
```

### `getWorkerDetails(): Array<{ index: number; state: string }>`

Returns detailed information about individual workers, including their current Temporal states.

**Returns:** Array of objects containing worker index and current state (INITIALIZED, RUNNING, STOPPED, etc.)

### `shutdown(): Promise<void>`

Shuts down all workers and cleans up resources.

## Health Check Logic

The health check evaluates the following conditions in order:

1. **Connection Check**: Returns `unhealthy` if no connection to Temporal server
2. **Worker Count Check**: Returns `unhealthy` if no workers are created
3. **Worker State Check**: Returns `unhealthy` if any workers are in `FAILED` or `STOPPED` state
4. **All Good**: Returns `healthy` if all checks pass

## Best Practices

1. **Health Monitoring**: Regularly check health status to monitor overall system health
2. **Automatic Shutdown**: The manager automatically handles `SIGINT` and `SIGTERM` signals - no need to set up your own handlers
3. **Manual Shutdown**: Use `shutdown()` for programmatic shutdown or in error handlers
4. **Error Handling**: Use [Temporal error handling](https://github.com/temporalio/sdk-typescript/blob/main/packages/common/src/failure.ts#L165) to handle errors in your workflows and activities
5. **Resource Cleanup**: Use `shutdown()` in error handlers to ensure cleanup
6. **Detailed Monitoring**: Use `getWorkerDetails()` for detailed worker state information when needed 