# Temporal Package API Reference

This document provides a comprehensive reference for all public APIs in the `@ordermentum/temporal` package.

## Table of Contents

- [Client API](#client-api)
- [Worker Management](#worker-management)
- [Workflow Registry](#workflow-registry)
- [Validation](#validation)
- [Configuration](#configuration)
- [Types and Interfaces](#types-and-interfaces)

## Client API

### `getTemporalClient(options?)`

Creates or returns a singleton Temporal client instance with automatic configuration.

**Parameters:**
- `options` (optional): Configuration options
  - `logger?: GenericLogger` - Custom logger instance
  - Additional `ClientOptions` from `@temporalio/client`

**Returns:** `Promise<Client>` - Temporal client instance

**Environment Variables:**
- `TEMPORAL_NAMESPACE` - Namespace to connect to (default: 'default')
- `TEMPORAL_DISABLE_TLS` - Set to 'true' or '1' to disable TLS

**Example:**
```typescript
import { getTemporalClient } from '@ordermentum/temporal';

// Basic usage
const client = await getTemporalClient();

// With custom logger
const client = await getTemporalClient({ logger: myLogger });

// Start a workflow
const handle = await client.workflow.start('MyWorkflow', {
  taskQueue: 'my-queue',
  workflowId: 'unique-id',
  args: ['arg1', 'arg2']
});
```

## Worker Management

### `TemporalWorkerManager`

Advanced worker manager with health monitoring and lifecycle management.

#### Constructor

```typescript
new TemporalWorkerManager(logger?: GenericLogger)
```

#### Methods

##### `initialize(logLevel?)`

Initializes the worker manager and establishes connection to Temporal.

**Parameters:**
- `logLevel?: LogLevel` - Temporal logging level (default: 'INFO')

**Returns:** `Promise<void>`

##### `createWorkers(activities, options?)`

Creates workers for all registered workflows.

**Parameters:**
- `activities: object` - Activity implementations
- `options?: object`
  - `logLevel?: LogLevel` - Temporal logging level

**Returns:** `Promise<Worker[]>` - Array of created workers

##### `start()`

Starts all workers and blocks until shutdown.

**Returns:** `Promise<void>`

##### `shutdown()`

Gracefully shuts down all workers and cleans up resources.

**Returns:** `Promise<void>`

##### `getHealthStatus()`

Gets the current health status of the worker manager.

**Returns:** `HealthStatus`

##### `getWorkerDetails()`

Gets detailed information about individual workers.

**Returns:** `Array<{ index: number; state: string }>`

##### `getWorkerCount()`

Gets the number of created workers.

**Returns:** `number`

**Example:**
```typescript
import { TemporalWorkerManager } from '@ordermentum/temporal';

const manager = new TemporalWorkerManager();
await manager.createWorkers(activities);

// Monitor health
setInterval(() => {
  const health = manager.getHealthStatus();
  console.log('Health:', health.state);
}, 5000);

await manager.start();
```

### `createTemporalWorker(options)`

Creates a single Temporal worker (simpler alternative to TemporalWorkerManager).

**Parameters:**
- `options: object`
  - `taskQueue: string` - Task queue name
  - `activities?: object` - Activity implementations
  - `logLevel?: LogLevel` - Temporal logging level
  - `logger?: GenericLogger` - Custom logger

**Returns:** `Promise<Worker>` - Temporal worker instance

**Example:**
```typescript
import { createTemporalWorker } from '@ordermentum/temporal';

const worker = await createTemporalWorker({
  taskQueue: 'my-queue',
  activities: { processData: async (data) => { ... } }
});

await worker.run();
```

### `startTemporalWorkers(activities, workflows, options)`

Convenience function that registers workflows and starts workers.

**Parameters:**
- `activities: object` - Activity implementations
- `workflows: WorkflowDefinition[]` - Workflow definitions to register
- `options: object` - Configuration options (required)
  - `logLevel: LogLevel` - Temporal logging level (required)
  - `logger: GenericLogger` - Custom logger (required)

**Returns:** `Promise<TemporalWorkerManager>` - Worker manager instance

**Example:**
```typescript
import { startTemporalWorkers } from '@ordermentum/temporal';

const manager = await startTemporalWorkers(activities, workflows, {
  logLevel: 'DEBUG',
  logger: myLogger
});
```

## Workflow Registry

### `WorkflowRegistry` (Internal API)

**Note:** `WorkflowRegistry` is used internally by `startTemporalWorkers()` and is not exported from the package. Workflows are automatically registered when using `startTemporalWorkers()`.

The registry provides centralized workflow management with automatic validation. Instead of using the registry directly, use the `startTemporalWorkers()` function which handles registration automatically:

**Example:**
```typescript
import { startTemporalWorkers } from '@ordermentum/temporal';

// Workflows are automatically registered
const manager = await startTemporalWorkers(activities, [myWorkflowDefinition], {
  logLevel: 'INFO',
  logger: myLogger
});
```

## Validation

### `validateWorkflowDefinition(obj)`

Validates a workflow definition and returns the validated object.

**Parameters:**
- `obj: unknown` - Object to validate

**Returns:** `WorkflowDefinition` - Validated workflow definition

**Throws:** `z.ZodError` - When validation fails

### `isValidWorkflowDefinition(obj)`

Type guard to check if an object is a valid workflow definition.

**Parameters:**
- `obj: unknown` - Object to validate

**Returns:** `boolean` - True if valid, false otherwise

### `validateWorkflowFile(workflowDefinition)`

Validates that a workflow file exists and has a valid default export.

**Parameters:**
- `workflowDefinition: WorkflowDefinition` - Workflow definition with file path

**Returns:** `Promise<void>`

**Throws:** `Error` - When file validation fails

**Example:**
```typescript
import { validateWorkflowDefinition, isValidWorkflowDefinition, startTemporalWorkers } from '@ordermentum/temporal';

// Type guard usage
if (isValidWorkflowDefinition(someObject)) {
  // Use with startTemporalWorkers (validation happens automatically)
  const manager = await startTemporalWorkers(activities, [someObject], {
    logLevel: 'INFO',
    logger: myLogger
  });
}

// Validation with error details
try {
  const valid = validateWorkflowDefinition(someObject);
  console.log('Valid workflow:', valid.name);
} catch (error) {
  console.error('Validation failed:', error.message);
}
```

## Configuration

### `getTemporalConfig()`

Gets Temporal configuration based on environment variables.

**Returns:** `TemporalConfig` - Configuration object

### `getTlsConfig()`

Gets TLS configuration with loaded certificates.

**Returns:** `TemporalTlsConfig | undefined` - TLS config or undefined if disabled

**Example:**
```typescript
import { getTemporalConfig, getTlsConfig } from '@ordermentum/temporal';

const config = getTemporalConfig();
const tlsConfig = getTlsConfig();

console.log(`Connecting to: ${config.address}`);
console.log(`Namespace: ${config.namespace}`);
console.log(`TLS enabled: ${config.enableTls}`);
```

## Types and Interfaces

### `WorkflowDefinition<Args, Return>`

Complete workflow definition interface.

**Properties:**
- `name: string` - Unique workflow name
- `workflow: WorkflowFunction<Args, Return>` - Workflow function
- `queueName: string` - Task queue name
- `generateWorkflowId: (id: string) => string` - ID generator function
- `signals: Record<string, SignalDefinition<any[]>>` - Signal definitions
- `queries: Record<string, QueryDefinition<any, any>>` - Query definitions
- `updates: Record<string, UpdateDefinition<any, any[]>>` - Update definitions
- `path: string` - File path to workflow source

### `GenericLogger`

Logger interface compatible with popular logging libraries.

**Methods:**
- `info(obj: object | string, msg?: string): void`
- `error(obj: object | string, msg?: string): void`
- `warn(obj: object | string, msg?: string): void`
- `debug(obj: object | string, msg?: string): void`
- `child(options: object): GenericLogger`

### `HealthStatus`

Health status information for worker manager.

**Properties:**
- `state: 'healthy' | 'unhealthy'` - Current health state
- `reason?: string` - Reason for unhealthy state (optional)

### `TemporalConfig`

Configuration object for Temporal connections.

**Properties:**
- `address: string` - Temporal server address
- `namespace: string` - Temporal namespace
- `clientCertPath?: string` - Path to client certificate
- `clientKeyPath?: string` - Path to client key
- `enableTls: boolean` - Whether TLS is enabled

### `TemporalTlsConfig`

TLS configuration with client certificates.

**Properties:**
- `clientCertPair: { crt: Buffer; key: Buffer }` - Certificate pair

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `TEMPORAL_NAMESPACE` | Temporal namespace to connect to | `'default'` |
| `TEMPORAL_DISABLE_TLS` | Set to 'true' or '1' to disable TLS | `false` |
| `TEMPORAL_MAX_WORKFLOWS` | Max concurrent workflow executions | Not set |
| `TEMPORAL_MAX_ACTIVITIES` | Max concurrent activity executions | Not set |
| `SKIP_WORKFLOW_FILE_VALIDATION` | Skip file validation (testing) | `false` |

## Error Handling

### Common Error Scenarios

1. **Connection Failures**
   - Check `TEMPORAL_NAMESPACE` environment variable
   - Verify TLS certificates are accessible
   - Ensure Temporal server is running

2. **Workflow Registration Errors**
   - Validate workflow definition structure
   - Check file paths are correct
   - Ensure default exports exist

3. **Worker Creation Failures**
   - Verify workflows are registered before creating workers
   - Check queue names match registered workflows
   - Ensure activities are properly defined

### Best Practices

1. **Always register workflows before creating workers**
2. **Use health monitoring for production deployments**
3. **Handle graceful shutdown in your applications**
4. **Validate workflow definitions early in development**
5. **Use structured logging for better debugging**