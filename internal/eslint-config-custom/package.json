{"name": "eslint-config-custom", "version": "0.0.0", "private": true, "license": "MIT", "main": ".eslintrc", "devDependencies": {"@ordermentum/eslint-config-ordermentum": "*", "@typescript-eslint/eslint-plugin": "^7.13.0", "@typescript-eslint/parser": "^7.13.0", "eslint-config-turbo": "^2.0.3", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^8.6.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-mocha": "^9.0.0", "eslint-plugin-prettier": "^4.2", "eslint-plugin-promise": "^6.2.0", "eslint-plugin-react": "^7.34.2", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-unicorn": "^45.0.2", "prettier": "^2.8.3"}, "publishConfig": {"access": "public"}}