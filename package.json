{"scripts": {"build": "turbo run build", "typecheck": "turbo run typecheck --parallel", "spec": "turbo run spec --parallel", "lint": "turbo run lint --parallel", "format": "turbo run lint --parallel -- --fix", "release": "yarn build && yarn lint && yarn typecheck && changeset version && changeset publish"}, "devDependencies": {"@changesets/cli": "^2.26.2", "@types/chai": "^4.2.16", "@types/mocha": "8.2.3", "@types/node": "14.18.63", "@types/sinon": "^9.0.11", "chai": "4.3.0", "eslint-config-custom": "*", "husky": "4.3.8", "lint-staged": "10.5.4", "mocha": "8.4.0", "mocha-sinon": "2.1.2", "sinon": "9.2.4", "ts-node": "^10.9.1", "turbo": "^1.10.12", "typescript": "5.1.6", "ioredis": "5.7.0"}, "resolutions": {"**/axios": "1.11.0"}, "workspaces": ["packages/*", "internal/*"], "private": true, "packageManager": "yarn@1.22.22", "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{ts,js,jsx}": ["eslint --max-warnings 0 --no-ignore"]}, "version": "2.0.0"}